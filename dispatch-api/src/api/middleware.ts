import { Express } from 'express';
import { Knex } from 'knex';
import { SqlDbSource } from '../datasources';
import authMiddleware from '../modules/auth/middleware';
import documentMiddleware from '../modules/document/middleware';
import finixWebhooks from '../modules/payment/finix/webhooks';
import paymentMiddleware from '../modules/payment/middleware';
import qualiphyMiddleware from '../modules/qualiphy/middleware';
import reportMiddleware from '../modules/report/middleware';
import { CustomRequest } from './context';

interface ApplyMiddlewareOptions {
  knex: Knex;
}

export default (app: Express, { knex }: ApplyMiddlewareOptions): void => {
  // if (config.prod) {
  //   app.use((req, res, next) => {
  //     if (!req.secure && req.header('x-forwarded-proto') !== 'https') {
  //       res.redirect(`https://${req.hostname}${req.url}`);
  //     } else {
  //       next();
  //     }
  //   });
  // }

  app.use((req, res, next) => {
    const customReq = req as CustomRequest;
    customReq.context = customReq.context || {};
    customReq.context.knex = knex;

    customReq.context.getUser = () => {
      const { context } = customReq;
      const sqldb = new SqlDbSource(context.knex);
      return context.kid
        ? sqldb.apiUser(context.kid)
        : sqldb.user(context.userId);
    };

    return next();
  });

  authMiddleware(app);
  documentMiddleware(app);
  paymentMiddleware(app);
  finixWebhooks(app);
  qualiphyMiddleware(app);
  reportMiddleware(app);
};
