import { GraphQLSchema } from 'graphql';
import { buildSchema as build } from 'type-graphql';
import { getConfig } from '../config';
import appointmentResolvers from '../modules/appointment/resolvers';
import attentiveResolvers from '../modules/attentive/resolvers';
import authResolvers from '../modules/auth/resolvers';
import availabilityResolvers from '../modules/availability/resolvers';
import clientProfileResolvers from '../modules/client-profile/resolvers';
import dispatchResolvers from '../modules/dispatch/resolvers';
import documentResolvers from '../modules/document/resolvers';
import emrResolvers from '../modules/emr/resolvers';
import geoperimeterResolvers from '../modules/geoperimeter/resolvers';
import inventoryResolvers from '../modules/inventory/resolvers';
import marketplaceResolvers from '../modules/marketplace/resolvers';
import membershipResolvers from '../modules/membership/resolvers';
import metricsResolvers from '../modules/metrics/resolvers';
import organizationResolvers from '../modules/organization/resolvers';
import paymentResolvers from '../modules/payment/resolvers';
import procedureBaseDefGroupResolvers from '../modules/procedure-base-def-group/resolvers';
import procedureResolvers from '../modules/procedure/resolvers';
import profileResolvers from '../modules/profile/resolvers';
import pushResolvers from '../modules/push/resolvers';
import qualiphyResolvers from '../modules/qualiphy/resolvers';
import remoteStorageResolvers from '../modules/remote-storage/resolvers';
import roleResolvers from '../modules/role/resolvers';
import userResolvers from '../modules/user/resolvers';
import twilioResolvers from '../modules/twilio/resolvers';
import reportResolvers from '../modules/report/resolvers';

export const buildSchema = async (): Promise<GraphQLSchema> =>
  build({
    resolvers: [
      ...authResolvers,
      ...userResolvers,
      ...roleResolvers,
      ...profileResolvers,
      ...organizationResolvers,
      ...marketplaceResolvers,
      ...procedureResolvers,
      ...availabilityResolvers,
      ...appointmentResolvers,
      ...clientProfileResolvers,
      ...dispatchResolvers,
      ...emrResolvers,
      ...inventoryResolvers,
      ...pushResolvers,
      ...documentResolvers,
      ...remoteStorageResolvers,
      ...geoperimeterResolvers,
      ...procedureBaseDefGroupResolvers,
      ...paymentResolvers,
      ...metricsResolvers,
      ...qualiphyResolvers,
      ...membershipResolvers,
      ...attentiveResolvers,
      ...twilioResolvers,
      ...reportResolvers,
    ],
    emitSchemaFile: !getConfig().prod,
    validate: {
      forbidUnknownValues: false,
    },
  });
