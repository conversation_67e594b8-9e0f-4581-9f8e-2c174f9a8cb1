import 'reflect-metadata';
// import { loadConfig } from '../config';
// import { SqlDbSource } from '../datasources';
// import { createKnex } from '../knex';
// import { Appointment } from '../modules/appointment/sqldb';
// import {
//   AppointmentStatus,
//   ParticipantType,
// } from '../modules/appointment/sqldb/types';
// import { marketplaceSend } from '../modules/email/sendgrid';
// import { marketplaceRender } from '../modules/email/notification/template';
// import { getName } from '../modules/twilio/notifications';
// import { getMarketplaceMessagingServiceSid } from '../modules/twilio/integration';
// import sendText from '../modules/twilio/text';
// import dayjs from '../modules/common/dayjs';
// import { groupIncludesBaseDef } from '../modules/procedure-base-def-group/common';

export const handler = async () => {
  // let knex;
  // try {
  //   const config = await loadConfig();
  //   knex = createKnex(config.sqldb);
  //   const sqldb = new SqlDbSource(knex);
  //   const sod = dayjs().add(1, 'day').startOf('day');
  //   const eod = sod.endOf('day');
  //   const appointments = await Appointment.query(knex)
  //     .where('start', '>=', sod.toDate())
  //     .where('start', '<', eod.toDate())
  //     .where('status', AppointmentStatus.BOOKED)
  //     .withGraphFetched(
  //       '[participants.clientProfile.[marketplaceUser, marketplaceUsers], procedureBaseDefs.marketplace.[twilio, sendgrid]]',
  //     );
  //   for (const appointment of appointments) {
  //     try {
  //       const marketplace =
  //         appointment.procedureBaseDefs?.find(Boolean)?.marketplace;
  //       if (!marketplace) {
  //         return;
  //       }
  //       const twilio = marketplace.twilio;
  //       const sendgrid = marketplace.sendgrid;
  //       if (!twilio?.clientDayReminder || !sendgrid?.enabled) {
  //         continue;
  //       }
  //       const group = await sqldb.procedureBaseDefGroup(
  //         marketplace?.notificationsGroupId,
  //       );
  //       if (
  //         !groupIncludesBaseDef(
  //           group,
  //           marketplace?.procedureBaseDefs ?? [],
  //           sqldb,
  //         )
  //       ) {
  //         return;
  //       }
  //       const patient = appointment.participants?.find(
  //         (p) => p.type === ParticipantType.PATIENT,
  //       );
  //       const client = patient?.clientProfile;
  //       const marketplaceUsers = client?.marketplaceUsers;
  //       const marketplaceUserRecord = client?.marketplaceUser;
  //       const marketplaceUser = [
  //         marketplaceUserRecord,
  //         ...(marketplaceUsers ?? []),
  //       ].find(
  //         (user) =>
  //           user?.email === client?.email &&
  //           user?.groupId === marketplace.groupId,
  //       );
  //       if (!marketplaceUser) {
  //         return;
  //       }
  //       const name = marketplace.name;
  //       if (marketplaceUser.email && marketplaceUser.emailConfirmed) {
  //         try {
  //           marketplaceSend({
  //             name,
  //             sendgrid,
  //             to: marketplaceUser.email,
  //             subject: `A friendly reminder for your upcoming appointment!`,
  //             template: marketplaceRender({
  //               title: `Your appointment is coming up!`,
  //               messages: [
  //                 {
  //                   text: `Your appointment for ${getName(
  //                     appointment.procedureBaseDefs ?? [],
  //                   )} is coming up at ${dayjs(appointment.start)
  //                     .tz(client?.tzid)
  //                     .format(
  //                       'hh:mm a - MM/DD/YYYY',
  //                     )}. Click the button below to view your appointment.`,
  //                 },
  //               ],
  //               action: {
  //                 text: 'View your appointment',
  //                 url: `${sendgrid.webOrigin}/appointments/appointment-${appointment.id}`,
  //               },
  //             }),
  //           });
  //         } catch (err) {
  //           console.log(err);
  //         }
  //       }
  //       if (marketplaceUser.phone && marketplaceUser.phoneConfirmed) {
  //         const messagingServiceSid = await getMarketplaceMessagingServiceSid({
  //           sqldb,
  //           marketplaceId: marketplace.id,
  //         });
  //         try {
  //           sendText({
  //             to: marketplaceUser.phone,
  //             body: `Your appointment with ${name} is coming up at ${dayjs(
  //               appointment.start,
  //             )
  //               .tz(client?.tzid)
  //               .format('hh:mm a - MM/DD/YYYY')} !`,
  //             messagingServiceSid,
  //           });
  //         } catch (err) {
  //           console.log(err);
  //         }
  //       }
  //     } catch (error) {
  //       console.log(error);
  //     }
  //   }
  //   return {
  //     Code: 200,
  //     body: JSON.stringify({
  //       message: 'Notifications sent successfully.',
  //     }),
  //   };
  // } catch (error) {
  //   console.error(error);
  //   return {
  //     Code: 500,
  //     body: JSON.stringify({
  //       message: 'There was an error sending notifications.',
  //       error,
  //     }),
  //   };
  // } finally {
  //   await knex?.destroy();
  // }
};
