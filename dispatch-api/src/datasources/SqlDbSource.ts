import { Knex } from 'knex';
import {
  Appointment,
  AppointmentParticipant,
  AppointmentRequest,
} from '../modules/appointment/sqldb';
import {
  getAppointment,
  getAppointmentParticipant,
  getAppointmentRequest,
} from '../modules/appointment/sqldb/queries';
import { AttentiveIntegration } from '../modules/attentive/sqldb';
import { getAttentiveIntegrationByMarketplaceId } from '../modules/attentive/sqldb/queries';
import {
  Availability,
  AvailabilityCoverage,
} from '../modules/availability/sqldb';
import {
  getAvailability,
  getAvailabilityCoverage,
  getOrganizationAvailabilityCoverages,
} from '../modules/availability/sqldb/queries';
import { ClientProfile } from '../modules/client-profile/sqldb';
import { getClientProfile } from '../modules/client-profile/sqldb/queries';
import { DocumentSignature } from '../modules/document/sqldb';
import Document from '../modules/document/sqldb/Document';
import {
  getDocument,
  getDocumentSignature,
} from '../modules/document/sqldb/queries';
import {
  AllergyIntolerance,
  ClinicalProcedure,
  Condition,
  EmrInstance,
  Form,
  FormAttachment,
  FormNote,
  FormTemplate,
  Immunization,
  Lab,
  MedicationStatement,
  Observation,
  Order,
  Patient,
  VitalSign,
} from '../modules/emr/sqldb';
import {
  getAllergyIntolerance,
  getClinicalProcedure,
  getCondition,
  getEmrInstance,
  getForm,
  getFormAttachments,
  getFormNote,
  getFormTemplate,
  getImmunization,
  getLab,
  getMedicationStatement,
  getObservation,
  getOrder,
  getPatient,
  getVitalSign,
} from '../modules/emr/sqldb/queries';
import { Geoperimeter } from '../modules/geoperimeter/sqldb';
import { getGeoperimeter } from '../modules/geoperimeter/sqldb/queries';
import { Medication } from '../modules/inventory/sqldb';
import { getMedication } from '../modules/inventory/sqldb/queries';
import {
  Marketplace,
  MarketplaceGroup,
  MarketplaceUser,
} from '../modules/marketplace/sqldb';
import {
  getMarketplace,
  getMarketplaceGroup,
  getMarketplaceUser,
  getMarketplacesByGroup,
  getOrganizationsByGroup,
} from '../modules/marketplace/sqldb/queries';
import {
  Discount,
  Membership,
  MembershipDefinition,
  Package,
  PackageItem,
  PackageItemDefinition,
} from '../modules/membership/sqldb';
import {
  getDiscount,
  getMembership,
  getMembershipDefinition,
  getPackage,
  getPackageItem,
  getPackageItemDefinition,
} from '../modules/membership/sqldb/queries';
import {
  AppointmentMetricsParams,
  RequestMetricsParams,
  getAppointmentLocationTrendMetrics,
  getAppointmentProcedureTrendMetrics,
  getAppointmentRequestByMarketplaceMetrics,
  getAppointmentRequestByStatusMetrics,
  getAppointmentsByStatusMetrics,
  getMembershipMetrics,
  getUserMetrics,
} from '../modules/metrics/sqldb/queries';
import { Organization } from '../modules/organization/sqldb';
import {
  getOrganization,
  getOrganizationsById,
} from '../modules/organization/sqldb/queries';
import {
  Checkout,
  Payment,
  PaymentAccount,
  Payout,
} from '../modules/payment/sqldb';
import PaymentInstrument from '../modules/payment/sqldb/PaymentInstrument';
import {
  getCheckout,
  getPayment,
  getPaymentAccount,
  getPaymentInstrument,
  getPayout,
} from '../modules/payment/sqldb/queries';
import { ProcedureBaseDefinitionGroup } from '../modules/procedure-base-def-group/sqldb';
import { getProcedureBaseDefGroup } from '../modules/procedure-base-def-group/sqldb/queries';
import {
  ProcedureBaseDefinition,
  ProcedureBaseDefinitionTag,
  ProcedureDefinition,
  ProcedureProfile,
} from '../modules/procedure/sqldb';
import {
  getProcedureBaseDefinition,
  getProcedureBaseDefinitionTag,
  getProcedureDefinition,
  getProcedureProfile,
} from '../modules/procedure/sqldb/queries';
import { Profile } from '../modules/profile/sqldb';
import { getProfileAndPermissions } from '../modules/profile/sqldb/queries';
import {
  QualiphyExam,
  QualiphyIntegration,
  QualiphyInvitation,
} from '../modules/qualiphy/sqldb';
import {
  getQualiphyExam,
  getQualiphyIntegrationByOrgId,
  getQualiphyInvitation,
} from '../modules/qualiphy/sqldb/queries';
import Role, { RoleScope } from '../modules/role/sqldb/Role';
import { getRole, getRoles } from '../modules/role/sqldb/queries';
import { User } from '../modules/user/sqldb';
import {
  getApiUserAndPermissions,
  getUserAndPermissions,
} from '../modules/user/sqldb/queries';
import CacheableDataSource, { cacheable } from './CacheableDataSource';
import {
  SendgridIntegration,
  TwilioIntegration,
  SegmentIntegration,
} from '../modules/twilio/sqldb';
import {
  getSendgridIntegrationByMarketplaceId,
  getTwilioIntegrationByMarketplaceId,
  getSegmentIntegrationByMarketplaceId,
} from '../modules/twilio/sqldb/queries';
import { EncryptedKey } from '../modules/common/sqldb';
import { getEncryptedKey } from '../modules/common/sqldb/queries';

type Maybe<T> = T | null | undefined;

export default class SqlDbSource extends CacheableDataSource {
  public knex: Knex;

  constructor(knex: Knex) {
    super();
    this.knex = knex;
  }

  @cacheable()
  async user(id?: number): Promise<Maybe<User> | null> {
    return id ? getUserAndPermissions(this.knex, { id }) : null;
  }

  @cacheable()
  async apiUser(kid?: string): Promise<Maybe<User>> {
    return new Promise((resolve) =>
      (async () => {
        if (!kid) {
          return null;
        }
        const user = await getApiUserAndPermissions(this.knex, { kid });
        if (user) {
          user.profiles = [];
        }
        return user ?? null;
      })().then(resolve),
    );
  }

  @cacheable()
  async profile(id?: number): Promise<Maybe<Profile>> {
    return id ? getProfileAndPermissions(this.knex, { id }) : null;
  }

  @cacheable()
  async organization(id?: number): Promise<Maybe<Organization>> {
    return id ? getOrganization(this.knex, { id }) : null;
  }

  @cacheable()
  async organizations(ids: string[]): Promise<Organization[]> {
    return ids.length ? getOrganizationsById(this.knex, { ids }) : [];
  }

  @cacheable()
  async geoperimeter(id?: number): Promise<Maybe<Geoperimeter>> {
    return id ? getGeoperimeter(this.knex, { id }) : null;
  }

  @cacheable()
  async marketplace(id?: number): Promise<Maybe<Marketplace>> {
    return id ? getMarketplace(this.knex, { id }) : null;
  }

  @cacheable()
  async marketplaceGroup(id?: number): Promise<Maybe<MarketplaceGroup>> {
    return id ? getMarketplaceGroup(this.knex, { id }) : null;
  }

  @cacheable()
  async marketplacesByGroup(groupId?: number): Promise<Maybe<Marketplace[]>> {
    return groupId ? getMarketplacesByGroup(this.knex, { groupId }) : null;
  }

  @cacheable()
  async role(id?: number): Promise<Maybe<Role>> {
    return id ? getRole(this.knex, { id }) : null;
  }

  @cacheable()
  async roles(scope: RoleScope, resourceId?: number): Promise<Role[]> {
    return getRoles(this.knex, { scope, resourceId });
  }

  @cacheable()
  async procedureBaseDef(id?: number): Promise<Maybe<ProcedureBaseDefinition>> {
    return id ? getProcedureBaseDefinition(this.knex, { id }) : null;
  }

  @cacheable()
  async procedureDef(id?: number): Promise<Maybe<ProcedureDefinition>> {
    return id ? getProcedureDefinition(this.knex, { id }) : null;
  }

  @cacheable()
  async procedureProfile(id?: number): Promise<Maybe<ProcedureProfile>> {
    return id ? getProcedureProfile(this.knex, { id }) : null;
  }

  @cacheable()
  async procedureBaseDefGroup(
    id?: number,
  ): Promise<Maybe<ProcedureBaseDefinitionGroup>> {
    return id ? getProcedureBaseDefGroup(this.knex, { id }) : null;
  }

  @cacheable()
  async procedureBaseDefTag(
    procedureBaseDefinitionId?: number,
    tag?: string,
  ): Promise<Maybe<ProcedureBaseDefinitionTag>> {
    return tag && procedureBaseDefinitionId
      ? getProcedureBaseDefinitionTag(this.knex, {
          procedureBaseDefinitionId,
          tag,
        })
      : null;
  }

  @cacheable()
  async availability(id?: number): Promise<Maybe<Availability>> {
    return id ? getAvailability(this.knex, { id }) : null;
  }

  @cacheable()
  async clientProfile(id?: number): Promise<Maybe<ClientProfile>> {
    return id ? getClientProfile(this.knex, { id }) : null;
  }

  @cacheable()
  async organizationsByGroup(groupId?: number): Promise<Organization[]> {
    return groupId ? getOrganizationsByGroup(this.knex, { groupId }) : [];
  }

  @cacheable()
  async appointment(id?: number): Promise<Maybe<Appointment>> {
    return id ? getAppointment(this.knex, { id }) : null;
  }

  @cacheable()
  async appointmentParticipant(
    id?: number,
  ): Promise<Maybe<AppointmentParticipant>> {
    return id ? getAppointmentParticipant(this.knex, { id }) : null;
  }

  @cacheable()
  async appointmentRequest(id?: number): Promise<Maybe<AppointmentRequest>> {
    return id ? getAppointmentRequest(this.knex, { id }) : null;
  }

  @cacheable()
  async emrInstance(id?: number): Promise<Maybe<EmrInstance>> {
    return id ? getEmrInstance(this.knex, { id }) : null;
  }

  @cacheable()
  async patient(id?: number): Promise<Maybe<Patient>> {
    return id ? getPatient(this.knex, { id }) : null;
  }

  @cacheable()
  async formTemplate(id?: number): Promise<Maybe<FormTemplate>> {
    return id ? getFormTemplate(this.knex, { id }) : null;
  }

  @cacheable()
  async form(id?: number): Promise<Maybe<Form>> {
    return id ? getForm(this.knex, { id }) : null;
  }

  @cacheable()
  async observation(id?: number): Promise<Maybe<Observation>> {
    return id ? getObservation(this.knex, { id }) : null;
  }

  @cacheable()
  async vitalSign(id?: number): Promise<Maybe<VitalSign>> {
    return id ? getVitalSign(this.knex, { id }) : null;
  }

  @cacheable()
  async order(id?: number): Promise<Maybe<Order>> {
    return id ? getOrder(this.knex, { id }) : null;
  }

  @cacheable()
  async medication(id?: number): Promise<Maybe<Medication>> {
    return id ? getMedication(this.knex, { id }) : null;
  }

  @cacheable()
  async document(id?: number): Promise<Maybe<Document>> {
    return id ? getDocument(this.knex, { id }) : null;
  }

  @cacheable()
  async documentSignature(id?: number): Promise<Maybe<DocumentSignature>> {
    return id ? getDocumentSignature(this.knex, { id }) : null;
  }

  @cacheable()
  async allergyIntolerance(id?: number): Promise<Maybe<AllergyIntolerance>> {
    return id ? getAllergyIntolerance(this.knex, { id }) : null;
  }

  @cacheable()
  async clinicalProcedure(id?: number): Promise<Maybe<ClinicalProcedure>> {
    return id ? getClinicalProcedure(this.knex, { id }) : null;
  }

  @cacheable()
  async condition(id?: number): Promise<Maybe<Condition>> {
    return id ? getCondition(this.knex, { id }) : null;
  }

  @cacheable()
  async immunization(id?: number): Promise<Maybe<Immunization>> {
    return id ? getImmunization(this.knex, { id }) : null;
  }

  @cacheable()
  async lab(id?: number): Promise<Maybe<Lab>> {
    return id ? getLab(this.knex, { id }) : null;
  }

  @cacheable()
  async medicationStatement(id?: number): Promise<Maybe<MedicationStatement>> {
    return id ? getMedicationStatement(this.knex, { id }) : null;
  }

  @cacheable()
  async availabilityCoverage(
    id?: number,
  ): Promise<Maybe<AvailabilityCoverage>> {
    return id ? getAvailabilityCoverage(this.knex, { id }) : null;
  }

  @cacheable()
  async organizationAvailabilityCoverages(
    id?: number,
  ): Promise<Maybe<AvailabilityCoverage[]>> {
    return id ? getOrganizationAvailabilityCoverages(this.knex, { id }) : null;
  }

  @cacheable()
  async paymentAccount(id?: number): Promise<Maybe<PaymentAccount>> {
    return id ? getPaymentAccount(this.knex, { id }) : null;
  }

  @cacheable()
  async marketplaceUser(id?: number): Promise<Maybe<MarketplaceUser>> {
    return id ? getMarketplaceUser(this.knex, { id }) : null;
  }

  @cacheable()
  async paymentInstrument(id?: number): Promise<Maybe<PaymentInstrument>> {
    return id ? getPaymentInstrument(this.knex, { id }) : null;
  }

  @cacheable()
  async checkout(id?: number): Promise<Maybe<Checkout>> {
    return id ? getCheckout(this.knex, { id }) : null;
  }

  @cacheable()
  async payment(id?: number): Promise<Maybe<Payment>> {
    return id ? getPayment(this.knex, { id }) : null;
  }

  @cacheable()
  async payout(id?: number): Promise<Maybe<Payout>> {
    return id ? getPayout(this.knex, { id }) : null;
  }

  @cacheable()
  async qualiphyExam(id?: number): Promise<Maybe<QualiphyExam>> {
    return id ? getQualiphyExam(this.knex, { id }) : null;
  }

  @cacheable()
  async qualiphyIntegrationForOrg(
    id?: number,
  ): Promise<Maybe<QualiphyIntegration>> {
    return id ? getQualiphyIntegrationByOrgId(this.knex, { id }) : null;
  }

  @cacheable()
  async qualiphyInvitation(id?: number): Promise<Maybe<QualiphyInvitation>> {
    return id ? getQualiphyInvitation(this.knex, { id }) : null;
  }

  @cacheable()
  async formAttachments(id?: number): Promise<FormAttachment[]> {
    return id ? getFormAttachments(this.knex, { id }) : [];
  }

  @cacheable()
  async package(id?: number): Promise<Maybe<Package>> {
    return id ? getPackage(this.knex, { id }) : null;
  }

  @cacheable()
  async packageItemDefinition(
    id?: number,
  ): Promise<Maybe<PackageItemDefinition>> {
    return id ? getPackageItemDefinition(this.knex, { id }) : null;
  }

  @cacheable()
  async membershipDefinition(
    id?: number,
  ): Promise<Maybe<MembershipDefinition>> {
    return id ? getMembershipDefinition(this.knex, { id }) : null;
  }

  @cacheable()
  async discount(id?: number): Promise<Maybe<Discount>> {
    return id ? getDiscount(this.knex, { id }) : null;
  }

  @cacheable()
  async membership(id?: number): Promise<Maybe<Membership>> {
    return id ? getMembership(this.knex, { id }) : null;
  }

  @cacheable()
  async packageItem(id?: number): Promise<Maybe<PackageItem>> {
    return id ? getPackageItem(this.knex, { id }) : null;
  }

  @cacheable()
  async attentiveIntegrationForMarketplace(
    id?: number,
  ): Promise<Maybe<AttentiveIntegration>> {
    return id
      ? getAttentiveIntegrationByMarketplaceId(this.knex, { id })
      : null;
  }

  @cacheable()
  async sendgridIntegrationForMarketplace(
    id?: number,
  ): Promise<Maybe<SendgridIntegration>> {
    return id ? getSendgridIntegrationByMarketplaceId(this.knex, { id }) : null;
  }

  @cacheable()
  async twilioIntegrationForMarketplace(
    id?: number,
  ): Promise<Maybe<TwilioIntegration>> {
    return id ? getTwilioIntegrationByMarketplaceId(this.knex, { id }) : null;
  }

  @cacheable()
  async segmentIntegrationForMarketplace(
    id?: number,
  ): Promise<Maybe<SegmentIntegration>> {
    return id ? getSegmentIntegrationByMarketplaceId(this.knex, { id }) : null;
  }

  @cacheable(60 * 60)
  async appointmentsByStatusMetrics(params: AppointmentMetricsParams) {
    return getAppointmentsByStatusMetrics(params);
  }

  @cacheable(60 * 60)
  async appointmentsByLocationTrendMetrics(params: AppointmentMetricsParams) {
    return getAppointmentLocationTrendMetrics(params);
  }

  @cacheable(60 * 60)
  async appointmentsByProcedureTrendMetrics(params: AppointmentMetricsParams) {
    return getAppointmentProcedureTrendMetrics(params);
  }

  @cacheable(60 * 60)
  async appointmentRequestsByMarketplaceMetrics(params: RequestMetricsParams) {
    return getAppointmentRequestByMarketplaceMetrics(params);
  }

  @cacheable(60 * 60)
  async appointmentRequestsByStatusMetrics(params: RequestMetricsParams) {
    return getAppointmentRequestByStatusMetrics(params);
  }

  @cacheable(60 * 60)
  async userMetrics({ organizationIds }: { organizationIds: string[] }) {
    return getUserMetrics({ knex: this.knex, organizationIds });
  }

  @cacheable(60 * 60)
  async membershipMetrics({ marketplaceIds }: { marketplaceIds: string[] }) {
    return getMembershipMetrics({ knex: this.knex, marketplaceIds });
  }

  @cacheable()
  async formNote(id?: number): Promise<Maybe<FormNote>> {
    return id ? getFormNote(this.knex, { id }) : null;
  }

  @cacheable()
  async encryptedKey(id?: number): Promise<Maybe<EncryptedKey>> {
    return id ? getEncryptedKey(this.knex, { id }) : null;
  }
}
