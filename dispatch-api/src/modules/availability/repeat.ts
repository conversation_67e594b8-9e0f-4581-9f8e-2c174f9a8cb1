import dayjs from '../common/dayjs';
import { weekdays } from './common';
import { Availability } from './sqldb';

export interface TimeRange {
  start: Date;
  end: Date;
}

const overlaps = (a: TimeRange, b: TimeRange): boolean =>
  a.end > b.start && a.start < b.end;

const setDate = (dt: dayjs.Dayjs, datePart: dayjs.Dayjs) =>
  dt.set({
    month: datePart.month(),
    date: datePart.date(),
    year: datePart.year(),
  });

export function between(
  availability: Availability,
  start: Date,
  end: Date,
): TimeRange[] {
  const cutOff = availability.repeatRule?.until;
  if (cutOff && dayjs(cutOff).isBefore(dayjs(new Date()))) {
    return [];
  }

  if (!availability.repeatRule) {
    return overlaps(availability, { start, end }) ? [availability] : [];
  }

  const { tzid } = availability;
  const byWeekday = availability.repeatRule?.byWeekday() ?? [];

  // capture recurring availabilities that start and end on different days
  const dayAdjust = dayjs(availability.end)
    .tz(tzid)
    .diff(dayjs(availability.start).tz(tzid), 'days');

  const results: TimeRange[] = [];
  let from = dayjs(start).tz(tzid).subtract(dayAdjust, 'days');
  const until = dayjs(end).tz(tzid);

  while (from < until) {
    if (byWeekday.includes(weekdays[from.day()])) {
      const result = {
        start: setDate(dayjs(availability.start).tz(tzid), from)
          .tz(tzid)
          .toDate(),
        end: setDate(dayjs(availability.end).tz(tzid), from)
          .add(dayAdjust, 'days')
          .tz(tzid)
          .toDate(),
      };
      if (overlaps(result, { start, end })) {
        results.push(result);
      }
    }
    from = from.add(1, 'day');
  }

  return results;
}
