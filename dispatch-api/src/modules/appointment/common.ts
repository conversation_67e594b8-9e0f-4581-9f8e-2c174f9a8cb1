import { ApolloError, ForbiddenError } from 'apollo-server';
import { filter, find, groupBy, property } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { ClientProfile } from '../client-profile/sqldb';
import { getClientProfile } from '../client-profile/sqldb/queries';
import dayjs from '../common/dayjs';
import { Marketplace } from '../marketplace/sqldb';
import MarketplaceUser from '../marketplace/sqldb/MarketplaceUser';
import { Profile } from '../profile/sqldb';
import { RoleScope } from '../role/sqldb/Role';
import { authorize } from '../user/authorize';
import { User } from '../user/sqldb';
import { AppointmentParticipant, AppointmentRequest } from './sqldb';
import {
  AppointmentFields,
  AppointmentParticipantFields,
  AppointmentRequestFields,
  ParticipantType,
} from './sqldb/types';

export function appointmentDuration(
  appointment: Partial<AppointmentFields>,
): number | null {
  if (appointment.start && appointment.end) {
    return dayjs(appointment.end).diff(appointment.start, 'minute');
  }

  return null;
}

interface ValidateAppointmentOptions {
  sqldb: SqlDbSource;
  allowOverlap?: boolean;
  update?: boolean;
}

export async function validateAppointment(
  appointment: AppointmentFields,
  options: ValidateAppointmentOptions,
): Promise<void> {
  const { sqldb, update } = options;
  const duration = appointmentDuration(appointment) ?? 0;

  if (duration <= 0) {
    throw new ApolloError(
      'Invalid appointment duration',
      'appointment:duration',
    );
  }

  if (appointment.procedureBaseDefs || !update) {
    const procedureBaseDefs = appointment.procedureBaseDefs ?? [];

    if (!procedureBaseDefs.length || procedureBaseDefs.some((d) => !d)) {
      throw new ApolloError(
        'Invalid procedure definition',
        'appointment:procedure-base-defs',
      );
    }

    // check if procedure base defs belong to the same marketplace

    const marketplaceIds = [
      ...new Set(procedureBaseDefs.map((def) => def.marketplaceId)),
    ];

    if (marketplaceIds.length > 1) {
      throw new ApolloError(
        'Procedure definitions must belong to the same marketplace',
        'appointment:marketplace',
      );
    }
  }

  if (appointment.participants || !update) {
    const participants = appointment.participants ?? [];

    const practitioners = await Promise.all(
      filter(participants, {
        type: ParticipantType.PRACTITIONER,
      }).map(({ profileId }) => sqldb.profile(profileId)),
    );

    // check if practitioners belong to the same organization

    const organizationIds = [
      ...new Set(practitioners.map((profile) => profile?.organizationId)),
    ];

    if (
      !practitioners.length ||
      practitioners.some((d) => !d) ||
      organizationIds.length > 1 ||
      !organizationIds[0]
    ) {
      throw new ApolloError('Invalid practitioner', 'appointment:practitioner');
    }

    const patients = await Promise.all(
      filter(participants, { type: ParticipantType.PATIENT }).map(
        ({ clientProfileId }) =>
          clientProfileId
            ? getClientProfile(sqldb.knex, { id: clientProfileId })
                .joinRelated('organizations')
                .where('organizations.id', organizationIds[0] as number)
            : null,
      ),
    );

    if (!patients.length || patients.some((p) => !p)) {
      throw new ApolloError('Invalid patient', 'appointment:patient');
    }
  }

  // TODO: check if each practitioner supports all procedure base defs
  // TODO: check if each practitioner is already busy (if !allowOverlap)
}

export function authorizeAppointment(
  user: User,
  profile?: Profile | null,
): boolean {
  return (
    !!profile &&
    (authorize(user, 'profiles:full') ||
      authorize(user, 'organization.profiles:full', {
        scope: RoleScope.ORGANIZATION,
        resourceId: profile.organizationId,
      }) ||
      (profile.userId === user.id &&
        authorize(user, 'organization.profiles:self', {
          scope: RoleScope.ORGANIZATION,
          resourceId: profile.organizationId,
        }))) &&
    (authorize(user, 'appointments:full') ||
      authorize(user, 'organization.appointments:full', {
        scope: RoleScope.ORGANIZATION,
        resourceId: profile.organizationId,
      }) ||
      (profile.userId === user.id &&
        authorize(user, 'organization.appointments:self', {
          scope: RoleScope.ORGANIZATION,
          resourceId: profile.organizationId,
        })))
  );
}

interface AuthorizeClientProfileParams {
  sqldb: SqlDbSource;
  full?: boolean;
}

export async function authorizeClientProfile(
  user: User,
  clientProfile: ClientProfile | null | undefined,
  { sqldb, full }: AuthorizeClientProfileParams,
): Promise<boolean> {
  if (!clientProfile) {
    return false;
  }

  const rootPerms = ['appointments:full'];
  const marketplacePerms = ['marketplace.appointments:full'];
  const organizationPerms = [
    'organization.appointments:full',
    'organization.appointments:self',
  ];

  if (!full) {
    rootPerms.push('appointments:list');
    marketplacePerms.push('marketplace.appointments:list');
    organizationPerms.push('organization.appointments:list');
  }

  if (authorize(user, rootPerms)) {
    return true;
  }

  let marketplaces: Marketplace[] = [];

  const organizations =
    clientProfile.organizations ||
    (await clientProfile.$relatedQuery('organizations', sqldb.knex));

  if (organizations.length) {
    if (
      organizations.some((organization) =>
        authorize(user, organizationPerms, {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        }),
      )
    ) {
      return true;
    }

    marketplaces = await Marketplace.query(sqldb.knex)
      .withGraphJoined('organizations')
      .whereIn('organizations.id', organizations.map(property('id')));
  } else {
    const marketplaceUser = await MarketplaceUser.query(sqldb.knex)
      .findOne({
        clientProfileId: clientProfile.id,
      })
      .withGraphJoined('marketplaceGroup.marketplaces');

    marketplaces = marketplaceUser?.marketplaceGroup?.marketplaces ?? [];
  }

  return marketplaces.some((marketplace) =>
    authorize(user, marketplacePerms, {
      scope: RoleScope.MARKETPLACE,
      resourceId: marketplace.id,
    }),
  );
}

interface AuthorizeAppointmentParticipantsOptions {
  sqldb: SqlDbSource;
}

export async function authorizeAppointmentParticipants(
  user: User,
  // participants: AppointmentParticipantInput[],
  participants: Partial<AppointmentParticipantFields>[],
  { sqldb }: AuthorizeAppointmentParticipantsOptions,
): Promise<void> {
  const participantsByType = groupBy(participants, 'type');

  const practitioners = await Promise.all(
    (participantsByType[ParticipantType.PRACTITIONER] ?? []).map(
      ({ profileId }) => sqldb.profile(profileId),
    ),
  );

  if (practitioners.some((p) => !p)) {
    throw new ApolloError('Invalid practitioner', 'appointment:practitioner');
  }

  if (
    practitioners.length > 0 &&
    !practitioners.some((profile) => authorizeAppointment(user, profile))
  ) {
    throw new ForbiddenError('Not authorized');
  }

  const patients = await Promise.all(
    (participantsByType[ParticipantType.PATIENT] ?? []).map(
      ({ clientProfileId }) => sqldb.clientProfile(clientProfileId),
    ),
  );

  if (patients.some((p) => !p)) {
    throw new ApolloError('Invalid patient', 'appointment:patient');
  }

  for (const clientProfile of patients) {
    if (!(await authorizeClientProfile(user, clientProfile, { sqldb }))) {
      throw new ForbiddenError('Not authorized');
    }
  }
}

interface AuthorizeAppointmentRequestOptions {
  sqldb: SqlDbSource;
}

export async function authorizeAppointmentRequestRecord(
  user: User,
  appointmentRequest: AppointmentRequest,
  { sqldb }: AuthorizeAppointmentRequestOptions,
): Promise<void> {
  await authorizeAppointmentRequest(
    user,
    {
      marketplaceId: appointmentRequest.marketplaceId,
      clientProfileIds: (appointmentRequest.clientProfiles ?? []).map(
        property('id'),
      ),
      constraints: (appointmentRequest.constraints ?? []).map((constraint) => ({
        organizationIds: constraint.organizations?.map(property('id')),
        profileIds: constraint.profiles?.map(property('id')),
        timeRanges: constraint.timeRanges ?? [],
      })),
    },
    { sqldb },
  );
}

export async function authorizeAppointmentRequest(
  user: User,
  appointmentRequest: Pick<
    AppointmentRequestFields,
    'marketplaceId' | 'clientProfileIds' | 'constraints'
  >,
  { sqldb }: AuthorizeAppointmentRequestOptions,
): Promise<void> {
  const marketplace = await sqldb.marketplace(appointmentRequest.marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'appointment-request:marketplace',
    );
  }

  const marketplaceAuthorized =
    authorize(user, 'marketplaces:full') ||
    authorize(user, 'marketplace.appointments:full', {
      scope: RoleScope.MARKETPLACE,
      resourceId: marketplace.id,
    });

  const clients = (appointmentRequest.clientProfileIds ?? []).map(
    (clientProfileId) => ({
      type: ParticipantType.PATIENT,
      clientProfileId,
    }),
  );

  // authorize all possible appointment candidates

  for (const constraint of appointmentRequest.constraints ?? []) {
    let orgAuthorized = marketplaceAuthorized;

    if (!marketplaceAuthorized && constraint.organizationIds?.length) {
      orgAuthorized = constraint.organizationIds.every(
        (organizationId) =>
          authorize(user, 'appointments:full') ||
          authorize(user, 'organization.appointments:full', {
            scope: RoleScope.ORGANIZATION,
            resourceId: organizationId,
          }),
      );
    }

    if (!orgAuthorized) {
      throw new ForbiddenError(
        'Not authorized for organization (appointment-request)',
      );
    }

    const practitioners = (constraint.profileIds ?? []).map((profileId) => ({
      type: ParticipantType.PRACTITIONER,
      profileId,
    }));

    await authorizeAppointmentParticipants(
      user,
      [...clients, ...practitioners],
      { sqldb },
    );
  }
}

interface ValidateAppointmentRequestOptions {
  sqldb: SqlDbSource;
  update?: boolean;
}

export async function validateAppointmentRequest(
  appointmentRequest: Omit<AppointmentRequestFields, 'status'>,
  options: ValidateAppointmentRequestOptions,
) {
  const { sqldb, update } = options;

  const marketplace = await sqldb.marketplace(appointmentRequest.marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'appointment-request:marketplace',
    );
  }

  const orgIds = new Set<number>();
  const profileIds = new Set<number>();
  const constraints = appointmentRequest.constraints ?? [];

  constraints.forEach((constraint) => {
    constraint.organizationIds?.forEach((id) => orgIds.add(id));
    constraint.profileIds?.forEach((id) => profileIds.add(id));

    if (
      !constraint.timeRanges.every(
        ({ start, end }) => (dayjs(end).diff(start, 'minute') ?? 0) >= 0,
      )
    ) {
      throw new ApolloError(
        'Invalid appointment time range',
        'appointment-request:time-range',
      );
    }
  });

  for (const orgId of orgIds) {
    if (!(await sqldb.organization(orgId))) {
      throw new ApolloError(
        'Invalid organization',
        'appointment-request:organization',
      );
    }
  }

  for (const profileId of profileIds) {
    if (!(await sqldb.profile(profileId))) {
      throw new ApolloError(
        'Invalid profile id',
        'appointment-request:profile',
      );
    }
  }

  const procedureBaseDefs = await Promise.all(
    (appointmentRequest.procedureBaseDefIds ?? []).map((baseDefId) =>
      sqldb.procedureBaseDef(baseDefId),
    ),
  );

  if (
    procedureBaseDefs.some(
      (def) => !def || def.marketplaceId !== marketplace.id,
    )
  ) {
    throw new ApolloError(
      'Invalid procedure base definition',
      'appointment-request:procedure-base-def',
    );
  }

  if (appointmentRequest.clientProfileIds || !update) {
    const clientProfileIds = appointmentRequest.clientProfileIds ?? [];

    if (clientProfileIds.length !== 1) {
      throw new ApolloError(
        'Appointment requests are currently restricted to one client profile',
        'appointment-request:client-profiles',
      );
    }

    const clientProfiles = await Promise.all(
      clientProfileIds.map((id) => sqldb.clientProfile(id)),
    );

    if (clientProfiles.some((id) => !id)) {
      throw new ApolloError(
        'Invalid client profile id',
        'appointment-request:client-profile',
      );
    }
  }
}

interface MarketplaceUserFromAppointmentParams {
  sqldb: SqlDbSource;
  appointment: AppointmentFields;
}

export async function marketplaceUserFromAppointment(
  params: MarketplaceUserFromAppointmentParams,
): Promise<MarketplaceUser | undefined> {
  const { sqldb, appointment } = params;

  const patient = find(appointment.participants ?? [], {
    type: ParticipantType.PATIENT,
  });

  const clientProfile = await sqldb.clientProfile(patient?.clientProfileId);
  let marketplaceUser = clientProfile?.marketplaceUser;

  if (!marketplaceUser) {
    const marketplace = await sqldb.marketplace(
      appointment.procedureBaseDefs?.[0]?.marketplaceId,
    );

    marketplaceUser = find(clientProfile?.marketplaceUsers ?? [], {
      groupId: marketplace?.groupId,
    });
  }

  return marketplaceUser;
}

export async function practitionerFromAppointment({
  appointment,
  sqldb,
}: MarketplaceUserFromAppointmentParams) {
  const practitioner = find(appointment.participants, {
    type: ParticipantType.PRACTITIONER,
  }) as AppointmentParticipant | undefined;

  const profile =
    practitioner?.profile ?? (await sqldb.profile(practitioner?.profileId));

  return profile;
}

export async function clientProfileFromAppointment({
  appointment,
  sqldb,
}: MarketplaceUserFromAppointmentParams) {
  const patient = find(appointment.participants, {
    type: ParticipantType.PATIENT,
  }) as AppointmentParticipant | undefined;

  const clientProfile =
    patient?.clientProfile ??
    (await sqldb.clientProfile(patient?.clientProfileId));

  return clientProfile;
}

export async function organizationFromAppointment({
  appointment,
  sqldb,
}: MarketplaceUserFromAppointmentParams) {
  const practitioner = find(appointment.participants, {
    type: ParticipantType.PRACTITIONER,
  }) as AppointmentParticipant | undefined;

  const profile =
    practitioner?.profile ?? (await sqldb.profile(practitioner?.profileId));

  return (
    profile?.organization ?? (await sqldb.organization(profile?.organizationId))
  );
}

export async function marketplaceFromAppointment({
  appointment,
  sqldb,
}: MarketplaceUserFromAppointmentParams) {
  const baseDef = appointment.procedureBaseDefs?.[0];
  return (
    baseDef?.marketplace ?? (await sqldb.marketplace(baseDef?.marketplaceId))
  );
}
