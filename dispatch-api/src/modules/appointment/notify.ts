import { capitalize, filter, uniqBy } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { Profile } from '../profile/sqldb';
import { send } from '../push/fcm';
import { CustomNotificationSound } from '../push/sounds';
import { casualDateTimeRange, summarizeProcedures } from './format';
import { Appointment } from './sqldb';
import { AppointmentStatus, ParticipantType } from './sqldb/types';
import sendText from '../twilio/text';
import { getMarketplaceMessagingServiceSid } from '../twilio/integration';
import dayjs from 'dayjs';

interface NotifyAppointmentPracitionersParams {
  sqldb: SqlDbSource;
  appointment: Appointment;
  isUpdate?: boolean;
}

export async function notifyAppointmentPracitioners({
  sqldb,
  appointment,
  isUpdate = false,
}: NotifyAppointmentPracitionersParams): Promise<void> {
  if (appointment.end < new Date()) {
    return;
  }

  const profiles = await Promise.all(
    filter(appointment.participants ?? [], {
      type: ParticipantType.PRACTITIONER,
    }).map((p) => sqldb.profile(p.profileId)),
  );

  const userProfiles = uniqBy(
    profiles.filter((p) => p?.userId) as Profile[],
    'userId',
  );

  const status =
    isUpdate && appointment.status === AppointmentStatus.BOOKED
      ? 'Updated'
      : capitalize(appointment.status.toLowerCase());

  const procedures = summarizeProcedures(appointment);
  const title = `${status} - ${procedures}`;

  const sound =
    status === AppointmentStatus.PENDING
      ? CustomNotificationSound.NEW_APPOINTMENT_PENDING
      : CustomNotificationSound.NEW_APPOINTMENT_BOOKED;

  let sid: string | undefined;

  if (appointment.status === AppointmentStatus.PENDING) {
    const marketplaceId = appointment.procedureBaseDefs?.find((r) =>
      Boolean(r.id),
    )?.marketplaceId;

    const marketplace = await sqldb.marketplace(marketplaceId);

    if (marketplace) {
      sid = await getMarketplaceMessagingServiceSid({
        sqldb,
        marketplaceId: marketplace?.id,
      });
    }
  }

  for (const profile of userProfiles) {
    const parts = [casualDateTimeRange(appointment, profile.tzid)];
    const numAlternateTimes = (appointment.alternateTimes?.length ?? 1) - 1;

    if (
      appointment.status === AppointmentStatus.PENDING &&
      numAlternateTimes > 0
    ) {
      parts.push(
        `and ${numAlternateTimes} alternate appointment`,
        numAlternateTimes === 1 ? 'time' : 'times',
      );
    }

    const organization =
      profile.organization ??
      (await sqldb.organization(profile.organizationId));

    const body = parts.join(' ');

    // Guaranteed to be pending due to sid check
    if (
      sid &&
      organization?.enablePractitionerSms &&
      profile.allowSmsNotifications &&
      profile.phone
    ) {
      sendText({
        to: profile.phone,
        messagingServiceSid: sid,
        body: `NomadMD: You have a new appointment request for ${dayjs(
          appointment.start,
        )
          .tz(profile.tzid)
          .format(
            'dddd, MMMM D @ hh:mm A',
          )}. Please open the mobile app to accept or decline.`,
      });
    }

    await send({
      sqldb,
      userId: profile.userId as number,
      message: {
        notification: {
          title,
          body,
        },
        data: {
          appointmentId: String(appointment.id),
        },
        apns: {
          payload: {
            aps: {
              sound,
            },
          },
        },
        android: {
          notification: {
            sound,
            tag: `appointmentId:${appointment.id}`,
          },
        },
      },
    });
  }
}
