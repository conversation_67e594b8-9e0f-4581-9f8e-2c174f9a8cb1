import { ApolloError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { User } from '../../user/sqldb';
import { authorizeAppointmentRequest } from '../common';
import { createAppointmentRequest } from '../create-appointment-request';
import { AppointmentRequest } from '../sqldb';
import CreateAppointmentRequestInput from './CreateAppointmentRequestInput';

@Resolver()
export default class CreateAppointmentRequestResolver {
  @Mutation(() => AppointmentRequest, {
    nullable: true,
  })
  async createAppointmentRequest(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: CreateAppointmentRequestInput,
  ): Promise<AppointmentRequest | null> {
    const marketplace = await sqldb.marketplace(intFromID(input.marketplaceId));

    if (!marketplace) {
      throw new ApolloError(
        'Invalid marketplace',
        'appointment-request:marketplace',
      );
    }

    const {
      location,
      notes,
      clientProfileIds,
      procedureBaseDefIds,
      constraints,
      gratuity,
      marketplaceUserId,
    } = input;

    const appointmentRequest = {
      marketplaceId: marketplace.id,
      location,
      notes,
      clientProfileIds: clientProfileIds.map((id) => intFromID(id) as number),
      procedureBaseDefIds: procedureBaseDefIds.map(
        (id) => intFromID(id) as number,
      ),
      constraints: constraints.map((constraint) => ({
        organizationIds: constraint.organizationIds?.map(
          (id) => intFromID(id) as number,
        ),
        profileIds: constraint.profileIds?.map((id) => intFromID(id) as number),
        timeRanges: constraint.timeRanges,
      })),
    };

    await authorizeAppointmentRequest(user, appointmentRequest, { sqldb });
    const paymentMethod = input.paymentMethod?.toPaymentMethod();

    return createAppointmentRequest({
      sqldb,
      appointmentRequest,
      paymentMethod,
      gratuity,
      marketplaceUserId: intFromID(marketplaceUserId),
    });
  }
}
