import { Field, ID, InputType, Int } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';
import PaymentMethodInput from '../../payment/resolvers/PaymentMethodInput';
import AppointmentConstraintInput from './AppointmentConstraintInput';
import AppointmentRequestInput from './AppointmentRequestInput';

@InputType()
export default class CreateAppointmentRequestInput extends AppointmentRequestInput {
  @Field(() => ID)
  @IsNumberID()
  marketplaceId!: string;

  @Field({ nullable: false })
  location!: string;

  @Field(() => [ID], { nullable: false })
  clientProfileIds!: string[];

  @Field(() => [ID], { nullable: false })
  procedureBaseDefIds!: string[];

  @Field(() => [AppointmentConstraintInput], { nullable: false })
  constraints!: AppointmentConstraintInput[];

  @Field(() => PaymentMethodInput, { nullable: true })
  paymentMethod?: PaymentMethodInput;

  @Field(() => Int, { nullable: true })
  gratuity?: number;

  @Field(() => ID, { nullable: true })
  marketplaceUserId?: string;
}
