import AcceptAppointment<PERSON>esolver from './AcceptAppointmentResolver';
import AppointmentConstraintResolver from './AppointmentConstraintResolver';
import AppointmentParticipantResolver from './AppointmentParticipantResolver';
import AppointmentRequestResolver from './AppointmentRequestResolver';
import AppointmentResolver from './AppointmentResolver';
import ApproveAppointmentRequestResolver from './ApproveAppointmentRequestResolver';
import ArchiveAppointmentRequestResolver from './ArchiveAppointmentRequestResolver';
import ArchiveAppointmentResolver from './ArchiveAppointmentResolver';
import CancelAppointmentRequestResolver from './CancelAppointmentRequestResolver';
import CancelAppointmentResolver from './CancelAppointmentResolver';
import CompleteAppointmentResolver from './CompleteAppointmentResolver';
import CreateAppointmentRequestResolver from './CreateAppointmentRequestResolver';
import CreateAppointmentResolver from './CreateAppointmentResolver';
import DeclineAppointmentResolver from './DeclineAppointmentResolver';
import PreviewCheckoutItemsResolver from './PreviewCheckoutItemsResolver';
import ResendQualiphyExamInviteResolver from './ResendQualiphyExamInviteResolver';
import ResetAppointmentResolver from './ResetAppointmentResolver';
import SendQualiphyExamInviteResolver from './SendQualiphyExamInviteResolver';
import StartAppointmentResolver from './StartAppointmentResolver';
import UpdateAppointmentRequestResolver from './UpdateAppointmentRequestResolver';
import UpdateAppointmentResolver from './UpdateAppointmentResolver';

export default [
  AppointmentResolver,
  AppointmentParticipantResolver,
  CreateAppointmentResolver,
  CancelAppointmentResolver,
  UpdateAppointmentResolver,
  AppointmentRequestResolver,
  AppointmentConstraintResolver,
  CreateAppointmentRequestResolver,
  ApproveAppointmentRequestResolver,
  CancelAppointmentRequestResolver,
  AcceptAppointmentResolver,
  DeclineAppointmentResolver,
  ArchiveAppointmentResolver,
  ArchiveAppointmentRequestResolver,
  StartAppointmentResolver,
  CompleteAppointmentResolver,
  ResetAppointmentResolver,
  UpdateAppointmentRequestResolver,
  ResendQualiphyExamInviteResolver,
  SendQualiphyExamInviteResolver,
  PreviewCheckoutItemsResolver,
] as const;
