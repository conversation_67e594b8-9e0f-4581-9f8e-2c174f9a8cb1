import { <PERSON>, <PERSON> } from 'class-validator';
import { Field, InputType, Int } from 'type-graphql';
import ReportFilterInput from './ReportFilterInput';
import ReportSortInput from './ReportSortInput';

@InputType()
export default class ReportPageInput {
  @Field(() => Int)
  @Min(1)
  @Max(1000)
  limit = 50;

  @Field(() => Int)
  @Min(0)
  offset = 0;

  @Field(() => [ReportSortInput], { nullable: true })
  sort?: ReportSortInput[];

  @Field(() => ReportFilterInput, { nullable: true })
  filter?: ReportFilterInput;
}
