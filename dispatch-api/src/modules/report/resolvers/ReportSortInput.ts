import { Field, InputType, registerEnumType } from 'type-graphql';
import { SortDirection } from '../../common/sqldb/types';
import { ReportSortField } from '../sqldb/types';

registerEnumType(ReportSortField, {
  name: 'ReportSortField',
});

@InputType()
export default class ReportSortInput {
  @Field(() => ReportSortField)
  field!: ReportSortField;

  @Field(() => SortDirection)
  direction!: SortDirection;
}
