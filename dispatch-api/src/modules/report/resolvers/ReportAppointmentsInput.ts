import { ArrayMaxSize, ArrayMinSize, IsDate } from 'class-validator';
import { Field, ID, InputType } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';

@InputType()
export default class ReportAppointmentsInput {
  @Field(() => ID)
  @IsNumberID()
  organizationId!: string;

  @Field(() => [Date])
  @ArrayMinSize(2)
  @ArrayMaxSize(2)
  @IsDate({ each: true })
  dateRange!: [Date, Date];
}
