import { Field, InputType } from 'type-graphql';
import { FilterDateInput, FilterStringInput } from '../../common/resolvers';
import FilterIntegerInput from '../../common/resolvers/FilterIntegerInput';
import { ReportType } from '../sqldb/types';

@InputType()
export default class ReportFilterInput {
  @Field(() => FilterStringInput, { nullable: true })
  filename?: FilterStringInput;

  @Field(() => [ReportType], { nullable: true })
  type?: ReportType[];

  @Field(() => FilterIntegerInput, { nullable: true })
  organizationId?: FilterIntegerInput;

  @Field(() => FilterDateInput, { nullable: true })
  createdAt?: FilterDateInput;

  @Field(() => FilterDateInput, { nullable: true })
  updatedAt?: FilterDateInput;
}
