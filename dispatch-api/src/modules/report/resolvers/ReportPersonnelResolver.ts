import { ApolloError, ForbiddenError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { generatePersonnelReport } from '../generate-personnel-report';
import { Report } from '../sqldb';
import ReportPersonnelInput from './ReportPersonnelInput';

@Resolver()
export default class ReportPersonnelResolver {
  @Mutation(() => Report)
  async reportPersonnel(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: ReportPersonnelInput,
  ): Promise<Report> {
    const { organizationId, profileIds, dateRange } = input;

    const orgId = intFromID(organizationId);
    if (!orgId) {
      throw new ApolloError('Invalid organization ID', 'report:organizationId');
    }

    if (
      !authorize(user, 'reports:full') &&
      !authorize(user, 'organization.reports:full', {
        scope: RoleScope.ORGANIZATION,
        resourceId: orgId,
      })
    ) {
      throw new ForbiddenError('Not authorized (reportPersonnel)');
    }

    const [startDate, endDate] = dateRange;
    if (startDate > endDate) {
      throw new ApolloError(
        'Start date must be on or before end date',
        'report:invalidDateRange',
      );
    }

    // Convert profileIds to numbers if provided
    const profileIdNumbers = profileIds?.map((id) => {
      const numId = intFromID(id);
      if (!numId) {
        throw new ApolloError(`Invalid profile ID: ${id}`, 'report:profileId');
      }
      return numId;
    });

    return generatePersonnelReport({
      sqldb,
      organizationId: orgId,
      userId: user.id,
      profileIds: profileIdNumbers,
      dateRange,
    });
  }
}
