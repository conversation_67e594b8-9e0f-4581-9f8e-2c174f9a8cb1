import { ApolloError, ForbiddenError } from 'apollo-server';
import {
  Arg,
  Ctx,
  FieldResolver,
  ID,
  Query,
  Resolver,
  Root,
} from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { getConfig } from '../../../config';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { fullName } from '../../common/util';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { getPaginatedReports } from '../queries';
import { ReportPage } from '../ReportPage';
import { Report } from '../sqldb';
import ReportPageInput from './ReportPageInput';

@Resolver(() => Report)
export default class ReportResolver {
  @FieldResolver(() => String)
  downloadUrl(@Root() report: Report): string {
    return `${getConfig().api.origin}/report?id=${report.id}`;
  }

  @FieldResolver(() => ID)
  userId(@Root() report: Report): number {
    return report.userId;
  }

  @FieldResolver(() => String, { nullable: true })
  async userName(
    @CurrentUser() user: User,
    @Root() report: Report,
  ): Promise<string | null> {
    if (!report.userId || !report.organizationId) {
      return null;
    }

    const hasGlobalPermission = authorize(user, [
      'profiles:list',
      'profiles:full',
    ]);

    const hasOrgPermission = authorize(
      user,
      ['organization.profiles:list', 'organization.profiles:full'],
      {
        scope: RoleScope.ORGANIZATION,
        resourceId: report.organizationId,
      },
    );

    if (!hasGlobalPermission && !hasOrgPermission) {
      return null;
    }

    // Since there can be multiple profiles per user, we need to find the one for this organization
    const profile = report.user?.profiles?.find(
      (p) => p.organizationId === report.organizationId,
    );

    if (!profile) {
      return null;
    }

    return fullName(profile.givenName, profile.familyName) || profile.email;
  }
  @Query(() => ReportPage)
  async reports(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('page', { defaultValue: {} }) page: ReportPageInput,
  ): Promise<ReportPage> {
    // Check if user has global reports permissions
    const hasGlobalPermission = authorize(user, [
      'reports:list',
      'reports:full',
    ]);

    // If no global permission, check if user has organization-scoped permissions
    if (!hasGlobalPermission) {
      // Get user's organization IDs from their roles
      const organizationIds = new Set<number>();

      user.profiles?.forEach((profile) => {
        profile.roles?.forEach((role) => {
          if (role.scope === RoleScope.ORGANIZATION && role.resourceId) {
            organizationIds.add(role.resourceId);
          }
        });
      });

      // Check if user has organization.reports permissions for any organization
      const hasOrgPermission = Array.from(organizationIds).some((orgId) =>
        authorize(
          user,
          ['organization.reports:list', 'organization.reports:full'],
          {
            scope: RoleScope.ORGANIZATION,
            resourceId: orgId,
          },
        ),
      );

      if (!hasOrgPermission) {
        throw new ForbiddenError('Not authorized (reports)');
      }

      // Filter results to only include reports from authorized organizations
      if (page.filter) {
        if (page.filter.organizationId) {
          // If organizationId filter is already specified, validate user has access
          const requestedOrgId = page.filter.organizationId.eq;
          if (requestedOrgId && !organizationIds.has(requestedOrgId)) {
            throw new ForbiddenError('Not authorized for this organization');
          }
        } else {
          // Add organization filter to limit results to user's organizations
          page.filter.organizationId = {
            type: 'integer',
            oneOf: Array.from(organizationIds),
          };
        }
      } else {
        // Create filter to limit to user's organizations
        page.filter = {
          organizationId: {
            type: 'integer',
            oneOf: Array.from(organizationIds),
          },
        };
      }
    }

    const result = await getPaginatedReports(sqldb.knex, page);

    return {
      totalCount: result.total,
      data: result.results,
    };
  }

  @Query(() => Report, { nullable: true })
  async report(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('id', () => ID) id: string,
  ): Promise<Report | null | undefined> {
    const reportId = intFromID(id);
    if (!reportId) {
      throw new ApolloError('Invalid report ID', 'report:id');
    }

    const report = await Report.query(sqldb.knex).findById(reportId);
    if (!report) {
      return null;
    }

    if (
      !authorize(user, ['reports:list', 'reports:full']) &&
      !authorize(
        user,
        ['organization.reports:list', 'organization.reports:full'],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: report.organizationId,
        },
      )
    ) {
      throw new ForbiddenError('Not authorized (report)');
    }

    return report;
  }
}
