import { ApolloError, ForbiddenError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { generateAppointmentsReport } from '../generate-appointments-report';
import { Report } from '../sqldb';
import ReportAppointmentsInput from './ReportAppointmentsInput';

@Resolver()
export default class ReportAppointmentsResolver {
  @Mutation(() => Report)
  async reportAppointments(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: ReportAppointmentsInput,
  ): Promise<Report> {
    const { organizationId, dateRange } = input;

    const orgId = intFromID(organizationId);
    if (!orgId) {
      throw new ApolloError('Invalid organization ID', 'report:organizationId');
    }

    if (
      !authorize(user, 'reports:full') &&
      !authorize(user, 'organization.reports:full', {
        scope: RoleScope.ORGANIZATION,
        resourceId: orgId,
      })
    ) {
      throw new ForbiddenError('Not authorized (reportAppointments)');
    }

    const [startDate, endDate] = dateRange;
    if (startDate > endDate) {
      throw new ApolloError(
        'Start date must be on orbefore end date',
        'report:invalidDateRange',
      );
    }

    return generateAppointmentsReport({
      sqldb,
      organizationId: orgId,
      userId: user.id,
      dateRange,
    });
  }
}
