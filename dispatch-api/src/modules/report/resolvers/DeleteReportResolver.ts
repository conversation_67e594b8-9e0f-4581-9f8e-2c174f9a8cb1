import { ApolloError, ForbiddenError } from 'apollo-server';
import { Arg, Ctx, ID, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { deleteReport } from '../delete-report';
import { Report } from '../sqldb';

@Resolver()
export default class DeleteReportResolver {
  @Mutation(() => Boolean)
  async deleteReport(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('id', () => ID) id: string,
  ): Promise<boolean> {
    const reportId = intFromID(id);
    if (!reportId) {
      throw new ApolloError('Invalid report ID', 'report:id');
    }

    const existingReport = await Report.query(sqldb.knex).findById(reportId);
    if (!existingReport) {
      throw new ApolloError('Report not found', 'report:not-found');
    }

    if (
      !authorize(user, 'reports:full') &&
      !authorize(user, 'organization.reports:full', {
        scope: RoleScope.ORGANIZATION,
        resourceId: existingReport.organizationId,
      })
    ) {
      throw new ForbiddenError('Not authorized (deleteReport)');
    }

    return deleteReport({
      sqldb,
      id: reportId,
    });
  }
}
