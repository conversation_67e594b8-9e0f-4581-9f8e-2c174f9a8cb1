import { K<PERSON> } from 'knex';
import { Page, QueryBuilder } from 'objection';
import { createFilter } from '../common/filter';
import { Report } from './sqldb';
import ReportPageInput from './resolvers/ReportPageInput';

export function getPaginatedReports(
  knex: Knex,
  page: ReportPageInput,
): QueryBuilder<Report, Page<Report>> {
  let query = Report.query(knex).withGraphFetched('user.profiles');

  if (page.filter) {
    const filters: Record<string, any> = {};

    if (page.filter.filename) {
      filters.filename = { ...page.filter.filename, type: 'string' };
    }

    if (page.filter.type?.length) {
      filters.type = { oneOf: page.filter.type, type: 'string' };
    }

    if (page.filter.organizationId) {
      filters.organizationId = {
        ...page.filter.organizationId,
        type: 'integer',
      };
    }

    if (page.filter.createdAt) {
      filters.createdAt = { ...page.filter.createdAt, type: 'date' };
    }

    if (page.filter.updatedAt) {
      filters.updatedAt = { ...page.filter.updatedAt, type: 'date' };
    }

    query = query.modify(createFilter(filters));
  }

  if (page.sort?.length) {
    page.sort.forEach((sort) => {
      query = query.orderBy(sort.field, sort.direction);
    });
  } else {
    query = query.orderBy('createdAt', 'desc');
  }

  return query.page(page.offset / page.limit, page.limit);
}
