import dayjs from 'dayjs';
import { Knex } from 'knex';
import { format } from 'fast-csv';
import { SqlDbSource } from '../../datasources';
import {
  AppointmentStatus,
  ParticipationStatus,
} from '../appointment/sqldb/types';
import { getReportObjectPath } from '../remote-storage/buckets';
import { putObject } from '../remote-storage/s3';
import { createReport } from './create-report';
import { ReportType, ReportStatus } from './sqldb/types';
import { Report } from './sqldb';
import { fullName } from '../common/util';
import { FormType } from '../emr/sqldb/types';
import { AvailabilityType } from '../availability/sqldb/Availability';

interface GeneratePersonnelReportParams {
  sqldb: SqlDbSource;
  organizationId: number;
  userId: number;
  profileIds?: number[];
  dateRange: [Date, Date];
}

interface PersonnelReportRow {
  profile_id: number | null;
  personnel: string;
  title: string;
  appointments_total: number;
  'appointment-status_pending': number;
  'appointment-status_booked': number;
  'appointment-status_completed': number;
  'appointment-status_cancelled': number;
  'appointment-time_average': string;
  productivity_score: string;
  'appointment-revenue_total': string;
  'appointment-revenue_average': string;
  'appointment-request_accept': number;
  'appointment-request_decline': number;
  availability_days: number;
  availability_hours: number;
  'appointment-intervention_locked': number;
  appointment_intervention_open: number;
  'appointment-assessment_locked': number;
  appointment_assessment_open: number;
  'appointment-client_new': number;
  'appointment-client_repeat': number;
}

export async function generatePersonnelReport({
  sqldb,
  organizationId,
  userId,
  profileIds,
  dateRange,
}: GeneratePersonnelReportParams): Promise<Report> {
  const [startDate, endDate] = dateRange;

  // Generate filename with date range and timestamp
  const startDateStr = dayjs(startDate).format('YYYY-MM-DD');
  const endDateStr = dayjs(endDate).format('YYYY-MM-DD');
  const timestamp = dayjs().format('HHmmss');

  let dateStr = `${startDateStr}-to-${endDateStr}`;
  if (startDateStr === endDateStr) {
    dateStr = startDateStr;
  }

  const filename = `personnel-${dateStr}-${timestamp}.csv`;

  const report = await createReport({
    sqldb,
    organizationId,
    userId,
    report: {
      type: ReportType.PERSONNEL,
      filename,
      status: ReportStatus.PROCESSING,
    },
  });

  try {
    const personnelData = await getPersonnelForReport(sqldb.knex, {
      organizationId,
      profileIds,
      startDate,
      endDate,
    });

    const csvData = personnelData.map(transformPersonnelToCSV);
    const csvContent = await generateCSVContent(csvData);

    const { bucket, key } = getReportObjectPath({
      organizationId,
      filename,
    });

    await putObject({
      bucket,
      key,
      body: csvContent,
      contentType: 'text/csv',
    });

    const filepath = `s3://${bucket}/${key}`;

    const updatedReport = await Report.query(sqldb.knex)
      .findById(report.id)
      .patch({
        filepath,
        status: ReportStatus.COMPLETED,
      })
      .returning('*')
      .first();

    return updatedReport || report;
  } catch (error) {
    await Report.query(sqldb.knex).findById(report.id).patch({
      status: ReportStatus.FAILED,
    });

    throw error;
  }
}

interface GetPersonnelForReportParams {
  organizationId: number;
  profileIds?: number[];
  startDate: Date;
  endDate: Date;
}

// Helper function to get availability statistics
async function getAvailabilityStats(
  knex: Knex,
  {
    organizationId,
    profileIds,
    startDate,
    endDate,
  }: GetPersonnelForReportParams,
) {
  return knex
    .select([
      'profiles.id as profileId',
      knex.raw(`
        COUNT(DISTINCT DATE(availabilities.start)) as availability_days
      `),
      knex.raw(`
        COALESCE(SUM(availabilities.duration) / 60.0, 0) as availability_hours
      `),
    ])
    .from('profiles')
    .leftJoin('availabilities', function () {
      this.on('profiles.id', '=', 'availabilities.profileId').andOn(
        'availabilities.type',
        '=',
        knex.raw(`'${AvailabilityType.AVAILABLE}'`),
      );
    })
    .where('profiles.organizationId', organizationId)
    .whereNull('profiles.deleted_at')
    .modify((query) => {
      if (profileIds && profileIds.length > 0) {
        query.whereIn('profiles.id', profileIds);
      }
    })
    .modify((query) => {
      // Filter availability by date range
      query.where(function () {
        this.whereNull('availabilities.start').orWhere(function () {
          this.where('availabilities.start', '>=', startDate).where(
            'availabilities.start',
            '<=',
            endDate,
          );
        });
      });
    })
    .groupBy('profiles.id');
}

// Helper function to get form statistics (chart notes)
async function getFormStats(
  knex: Knex,
  {
    organizationId,
    profileIds,
    startDate,
    endDate,
  }: GetPersonnelForReportParams,
) {
  return knex
    .select([
      'profiles.id as profileId',
      knex.raw(`
        COUNT(CASE WHEN forms.type = '${FormType.INTERVENTION}' AND forms.locked_by_id IS NOT NULL THEN 1 END) as appointment_intervention_locked
      `),
      knex.raw(`
        COUNT(CASE WHEN forms.type = '${FormType.INTERVENTION}' AND forms.locked_by_id IS NULL THEN 1 END) as appointment_intervention_open
      `),
      knex.raw(`
        COUNT(CASE WHEN forms.type = '${FormType.ASSESSMENT}' AND forms.locked_by_id IS NOT NULL THEN 1 END) as appointment_assessment_locked
      `),
      knex.raw(`
        COUNT(CASE WHEN forms.type = '${FormType.ASSESSMENT}' AND forms.locked_by_id IS NULL THEN 1 END) as appointment_assessment_open
      `),
    ])
    .from('profiles')
    .leftJoin(
      'appointment_participants as practitioner_participants',
      function () {
        this.on(
          'profiles.id',
          '=',
          'practitioner_participants.profileId',
        ).andOn(
          'practitioner_participants.type',
          '=',
          knex.raw("'practitioner'"),
        );
      },
    )
    .leftJoin(
      'appointments',
      'practitioner_participants.appointmentId',
      'appointments.id',
    )
    .leftJoin('appointment_participants as patient_participants', function () {
      this.on(
        'appointments.id',
        '=',
        'patient_participants.appointmentId',
      ).andOn('patient_participants.type', '=', knex.raw("'patient'"));
    })
    .leftJoin('forms', function () {
      this.on(
        'patient_participants.id',
        '=',
        'forms.appointmentParticipantId',
      ).andOnNull('forms.deleted_at');
    })
    .where('profiles.organizationId', organizationId)
    .whereNull('profiles.deleted_at')
    .modify((query) => {
      if (profileIds && profileIds.length > 0) {
        query.whereIn('profiles.id', profileIds);
      }
    })
    .modify((query) => {
      // Filter appointments by date range for form stats
      query.where(function () {
        this.whereNull('appointments.start').orWhere(function () {
          this.where('appointments.start', '>=', startDate).where(
            'appointments.start',
            '<=',
            endDate,
          );
        });
      });
    })
    .groupBy('profiles.id');
}

// Helper function to get client statistics (new vs repeat)
async function getClientStats(
  knex: Knex,
  {
    organizationId,
    profileIds,
    startDate,
    endDate,
  }: GetPersonnelForReportParams,
) {
  return knex
    .select([
      'profiles.id as profileId',
      knex.raw(`
        COUNT(CASE
          WHEN client_first_appointments.first_appointment_id = appointments.id
          THEN 1
        END) as appointment_client_new
      `),
      knex.raw(`
        COUNT(CASE
          WHEN client_first_appointments.first_appointment_id != appointments.id
          AND appointments.id IS NOT NULL
          THEN 1
        END) as appointment_client_repeat
      `),
    ])
    .from('profiles')
    .leftJoin(
      'appointment_participants as practitioner_participants',
      function () {
        this.on(
          'profiles.id',
          '=',
          'practitioner_participants.profile_id',
        ).andOn(
          'practitioner_participants.type',
          '=',
          knex.raw("'practitioner'"),
        );
      },
    )
    .leftJoin(
      'appointments',
      'practitioner_participants.appointment_id',
      'appointments.id',
    )
    .leftJoin('appointment_participants as patient_participants', function () {
      this.on(
        'appointments.id',
        '=',
        'patient_participants.appointment_id',
      ).andOn('patient_participants.type', '=', knex.raw("'patient'"));
    })
    .leftJoin(
      knex.raw(`
        (
          SELECT
            client_participants.client_profile_id,
            MIN(client_appointments.id) as first_appointment_id
          FROM appointment_participants client_participants
          JOIN appointments client_appointments ON client_participants.appointment_id = client_appointments.id
          WHERE client_participants.type = 'patient'
          AND client_appointments.deleted_at IS NULL
          GROUP BY client_participants.client_profile_id
        ) as client_first_appointments
      `),
      function () {
        this.on(
          'patient_participants.client_profile_id',
          '=',
          'client_first_appointments.client_profile_id',
        );
      },
    )
    .where('profiles.organizationId', organizationId)
    .whereNull('profiles.deleted_at')
    .where(function () {
      this.whereNull('appointments.deleted_at').orWhereNull('appointments.id');
    })
    .modify((query) => {
      if (profileIds && profileIds.length > 0) {
        query.whereIn('profiles.id', profileIds);
      }
    })
    .modify((query) => {
      // Filter appointments by date range for client stats
      query.where(function () {
        this.whereNull('appointments.start').orWhere(function () {
          this.where('appointments.start', '>=', startDate).where(
            'appointments.start',
            '<=',
            endDate,
          );
        });
      });
    })
    .groupBy('profiles.id');
}

interface PersonnelWithStats {
  id: number;
  userId: number | null;
  givenName: string;
  familyName: string;
  email: string;
  title: string;
  appointments_total: number;
  appointments_pending: number;
  appointments_booked: number;
  appointments_completed: number;
  appointments_cancelled: number;
  duration_average_minutes: number | null;
  time_average_minutes: number | null;
  appointment_revenue_total_cents: number;
  appointment_revenue_average_cents: number;
  appointment_request_accept: number;
  appointment_request_decline: number;
  availability_days: number;
  availability_hours: number;
  appointment_intervention_locked: number;
  appointment_intervention_open: number;
  appointment_assessment_locked: number;
  appointment_assessment_open: number;
  appointment_client_new: number;
  appointment_client_repeat: number;
}

async function getPersonnelForReport(
  knex: Knex,
  {
    organizationId,
    profileIds,
    startDate,
    endDate,
  }: GetPersonnelForReportParams,
): Promise<PersonnelWithStats[]> {
  // Get base personnel data with appointment stats
  const result = await knex
    .select([
      'profiles.id',
      'profiles.user_id as userId',
      'profiles.given_name as givenName',
      'profiles.family_name as familyName',
      'profiles.email',
      'profiles.title',
      knex.raw('COUNT(appointments.id) as appointments_total'),
      knex.raw(`
        COUNT(CASE WHEN appointments.status = '${AppointmentStatus.PENDING}' THEN 1 END) as appointments_pending
      `),
      knex.raw(`
        COUNT(CASE WHEN appointments.status = '${AppointmentStatus.BOOKED}' THEN 1 END) as appointments_booked
      `),
      knex.raw(`
        COUNT(CASE WHEN appointments.status = '${AppointmentStatus.COMPLETED}' THEN 1 END) as appointments_completed
      `),
      knex.raw(`
        COUNT(CASE WHEN appointments.status = '${AppointmentStatus.CANCELLED}' AND appointments.deleted_at IS NULL THEN 1 END) as appointments_cancelled
      `),
      knex.raw(`
        AVG(CASE WHEN appointments.status = '${AppointmentStatus.COMPLETED}' AND appointments.end IS NOT NULL AND appointments.start IS NOT NULL
            THEN EXTRACT(EPOCH FROM (appointments.end - appointments.start)) / 60
            END) as duration_average_minutes
      `),
      knex.raw(`
        AVG(CASE WHEN appointments.status = '${AppointmentStatus.COMPLETED}' AND appointments.completed_at IS NOT NULL AND appointments.started_at IS NOT NULL
            THEN EXTRACT(EPOCH FROM (appointments.completed_at - appointments.started_at)) / 60
            END) as time_average_minute
      `),
      knex.raw(
        `COALESCE(SUM(CASE WHEN appointments.status IN ('${AppointmentStatus.BOOKED}', '${AppointmentStatus.COMPLETED}') THEN checkouts.total END),0) as appointment_revenue_total_cents`,
      ),
      knex.raw(
        `COALESCE(AVG(CASE WHEN appointments.status IN ('${AppointmentStatus.BOOKED}', '${AppointmentStatus.COMPLETED}') THEN checkouts.total END),0) as appointment_revenue_average_cents`,
      ),
      knex.raw(
        `COUNT(CASE WHEN appointment_participants.status = '${ParticipationStatus.ACCEPTED}' THEN 1 END) as appointment_request_accept`,
      ),
      knex.raw(
        `COUNT(CASE WHEN appointment_participants.status = '${ParticipationStatus.DECLINED}' THEN 1 END) as appointment_request_decline`,
      ),
    ])
    .from('profiles')
    .leftJoin(
      'appointment_participants',
      'profiles.id',
      'appointment_participants.profileId',
    )
    .leftJoin(
      'appointments',
      'appointment_participants.appointmentId',
      'appointments.id',
    )
    .leftJoin('checkouts', 'appointments.checkout_id', 'checkouts.id')
    .where('profiles.organizationId', organizationId)
    .whereNull('profiles.deleted_at')
    .where(function () {
      this.whereNull('appointments.deleted_at').orWhereNull('appointments.id');
    })
    .modify((query) => {
      if (profileIds && profileIds.length > 0) {
        query.whereIn('profiles.id', profileIds);
      }
    })
    .modify((query) => {
      // Filter appointments by date range
      query.where(function () {
        this.whereNull('appointments.start').orWhere(function () {
          this.where('appointments.start', '>=', startDate).where(
            'appointments.start',
            '<=',
            endDate,
          );
        });
      });
    })
    .groupBy(
      'profiles.id',
      'profiles.user_id',
      'profiles.given_name',
      'profiles.family_name',
      'profiles.email',
      'profiles.title',
    )
    .orderBy('profiles.id');

  // Get availability data
  const availabilityData = await getAvailabilityStats(knex, {
    organizationId,
    profileIds,
    startDate,
    endDate,
  });

  // Get form data (chart notes)
  const formData = await getFormStats(knex, {
    organizationId,
    profileIds,
    startDate,
    endDate,
  });

  // Get client data (new vs repeat)
  const clientData = await getClientStats(knex, {
    organizationId,
    profileIds,
    startDate,
    endDate,
  });

  // Create lookup maps for additional data
  const availabilityMap = new Map(
    availabilityData.map((item: any) => [item.profileId, item]),
  );
  const formMap = new Map(formData.map((item: any) => [item.profileId, item]));
  const clientMap = new Map(
    clientData.map((item: any) => [item.profileId, item]),
  );

  return result.map((row: any) => {
    const availability = availabilityMap.get(row.id) || {
      availability_days: 0,
      availability_hours: 0,
    };
    const forms = formMap.get(row.id) || {
      appointment_intervention_locked: 0,
      appointment_intervention_open: 0,
      appointment_assessment_locked: 0,
      appointment_assessment_open: 0,
    };
    const clients = clientMap.get(row.id) || {
      appointment_client_new: 0,
      appointment_client_repeat: 0,
    };

    return {
      id: row.id,
      userId: row.userId,
      givenName: row.givenName || '',
      familyName: row.familyName || '',
      email: row.email || '',
      title: row.title || '',
      appointments_total: parseInt(row.appointmentsTotal) || 0,
      appointments_pending: parseInt(row.appointmentsPending) || 0,
      appointments_booked: parseInt(row.appointmentsBooked) || 0,
      appointments_completed: parseInt(row.appointmentsCompleted) || 0,
      appointments_cancelled: parseInt(row.appointmentsCancelled) || 0,
      duration_average_minutes: row.durationAverageMinutes
        ? parseFloat(row.durationAverageMinutes)
        : null,
      time_average_minutes: row.timeAverageMinutes
        ? parseFloat(row.timeAverageMinutes)
        : null,
      appointment_revenue_total_cents:
        parseInt(row.appointmentRevenueTotalCents) || 0,
      appointment_revenue_average_cents: row.appointmentRevenueAverageCents
        ? parseFloat(row.appointmentRevenueAverageCents)
        : 0,
      appointment_request_accept: parseInt(row.appointmentRequestAccept) || 0,
      appointment_request_decline: parseInt(row.appointmentRequestDecline) || 0,
      availability_days: parseInt((availability as any).availabilityDays) || 0,
      availability_hours:
        parseFloat((availability as any).availabilityHours) || 0,
      appointment_intervention_locked:
        parseInt((forms as any).appointmentInterventionLocked) || 0,
      appointment_intervention_open:
        parseInt((forms as any).appointmentInterventionOpen) || 0,
      appointment_assessment_locked:
        parseInt((forms as any).appointmentAssessmentLocked) || 0,
      appointment_assessment_open:
        parseInt((forms as any).appointmentAssessmentOpen) || 0,
      appointment_client_new:
        parseInt((clients as any).appointmentClientNew) || 0,
      appointment_client_repeat:
        parseInt((clients as any).appointmentClientRepeat) || 0,
    };
  });
}

function transformPersonnelToCSV(
  personnel: PersonnelWithStats,
): PersonnelReportRow {
  const averageTime = personnel.time_average_minutes
    ? personnel.time_average_minutes.toFixed(2)
    : '';

  const productivityScore =
    personnel.time_average_minutes &&
    personnel.duration_average_minutes &&
    personnel.duration_average_minutes > 0
      ? (
          personnel.time_average_minutes / personnel.duration_average_minutes
        ).toFixed(2)
      : '';

  const name =
    fullName(personnel.givenName, personnel.familyName) || personnel.email;

  const revenueTotal = (
    personnel.appointment_revenue_total_cents / 100
  ).toFixed(2);
  const revenueAverage = personnel.appointment_revenue_average_cents
    ? (personnel.appointment_revenue_average_cents / 100).toFixed(2)
    : '';

  return {
    profile_id: personnel.id,
    personnel: name,
    title: personnel.title,
    appointments_total: personnel.appointments_total,
    'appointment-status_pending': personnel.appointments_pending,
    'appointment-status_booked': personnel.appointments_booked,
    'appointment-status_completed': personnel.appointments_completed,
    'appointment-status_cancelled': personnel.appointments_cancelled,
    'appointment-time_average': averageTime,
    productivity_score: productivityScore,
    'appointment-revenue_total': revenueTotal,
    'appointment-revenue_average': revenueAverage,
    'appointment-request_accept': personnel.appointment_request_accept,
    'appointment-request_decline': personnel.appointment_request_decline,
    availability_days: personnel.availability_days,
    availability_hours: personnel.availability_hours,
    'appointment-intervention_locked':
      personnel.appointment_intervention_locked,
    appointment_intervention_open: personnel.appointment_intervention_open,
    'appointment-assessment_locked': personnel.appointment_assessment_locked,
    appointment_assessment_open: personnel.appointment_assessment_open,
    'appointment-client_new': personnel.appointment_client_new,
    'appointment-client_repeat': personnel.appointment_client_repeat,
  };
}

async function generateCSVContent(data: PersonnelReportRow[]): Promise<string> {
  return new Promise((resolve, reject) => {
    const csvRows: string[] = [];

    const csvStream = format({ headers: true })
      .on('data', (row: string) => csvRows.push(row))
      .on('end', () => resolve(csvRows.join('')))
      .on('error', reject);

    data.forEach((row) => csvStream.write(row));
    csvStream.end();
  });
}
