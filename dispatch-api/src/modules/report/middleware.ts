import { Express } from 'express';
import { CustomRequest } from '../../api/context';
import { SqlDbSource } from '../../datasources';
import { intFromID } from '../common/type-graphql';
import { fromUri } from '../remote-storage/path';
import { getObjectSignedUrl } from '../remote-storage/s3';
import { RoleScope } from '../role/sqldb/Role';
import { authorize } from '../user/authorize';
import { Report } from './sqldb';

export default (app: Express): void => {
  app.get('/report', async (req, res) => {
    const customReq = req as CustomRequest;
    const sqldb = new SqlDbSource(customReq.context.knex);
    const user = await customReq.context.getUser();

    if (!user) {
      res.status(401).end();
      return;
    }

    const reportId = req.query.id as string;
    const reportIdInt = intFromID(reportId);

    if (!reportIdInt) {
      res.status(400).end();
      return;
    }

    const report = await Report.query(sqldb.knex).findById(reportIdInt);

    if (!report) {
      res.status(404).end();
      return;
    }

    if (
      !authorize(user, ['reports:list', 'reports:full']) &&
      !authorize(
        user,
        ['organization.reports:list', 'organization.reports:full'],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: report.organizationId,
        },
      )
    ) {
      res.status(403).end();
      return;
    }

    if (!report.filepath || !report.filename) {
      res.status(404).end();
      return;
    }

    try {
      const url = await getObjectSignedUrl({
        ...fromUri(report.filepath),
        filename: report.filename,
      });
      res.redirect(url);
    } catch (error) {
      console.error('Error generating signed URL for report:', error);
      res.status(500).end();
      return;
    }
  });
};
