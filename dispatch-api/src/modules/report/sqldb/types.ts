export enum ReportType {
  APPOINTMENTS = 'appointments',
  PERSONNEL = 'personnel',
}

export enum ReportStatus {
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export enum ReportSortField {
  ID = 'id',
  TYPE = 'type',
  FILENAME = 'filename',
  STATUS = 'status',
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
}

export interface ReportFields {
  readonly id: number;
  type: ReportType;
  filename: string | null;
  filepath: string | null;
  status: ReportStatus;
  organizationId: number;
  userId: number;
}
