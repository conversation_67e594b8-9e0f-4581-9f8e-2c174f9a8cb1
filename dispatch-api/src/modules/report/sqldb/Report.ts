import { Model, RelationMappings } from 'objection';
import { Field, ID, ObjectType, registerEnumType } from 'type-graphql';
import { TimestampModel } from '../../common/sqldb';
import { Organization } from '../../organization/sqldb';
import { User } from '../../user/sqldb';
import { ReportFields, ReportType, ReportStatus } from './types';

registerEnumType(ReportType, {
  name: 'ReportType',
});

registerEnumType(ReportStatus, {
  name: 'ReportStatus',
});

@ObjectType()
export default class Report extends TimestampModel implements ReportFields {
  @Field(() => ID)
  readonly id!: number;

  @Field(() => ReportType)
  type!: ReportType;

  @Field(() => String, { nullable: true })
  filename!: string | null;

  filepath!: string | null;

  @Field(() => ReportStatus)
  status!: ReportStatus;

  @Field(() => ID)
  organizationId!: number;

  @Field(() => ID)
  userId!: number;

  organization?: Organization;

  @Field(() => User)
  user?: User;

  static tableName = 'reports';

  static relationMappings = (): RelationMappings => ({
    organization: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('../../organization/sqldb').Organization,
      join: {
        from: 'reports.organizationId',
        to: 'organizations.id',
      },
    },
    user: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('../../user/sqldb').User,
      join: {
        from: 'reports.userId',
        to: 'users.id',
      },
    },
  });
}
