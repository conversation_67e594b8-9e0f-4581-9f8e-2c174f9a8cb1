import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { Report } from './sqldb';

interface DeleteReportParams {
  sqldb: SqlDbSource;
  id: number;
}

export async function deleteReport({
  sqldb,
  id,
}: DeleteReportParams): Promise<boolean> {
  // Check if report exists before deletion
  const existingReport = await Report.query(sqldb.knex).findById(id);
  if (!existingReport) {
    throw new ApolloError('Report not found', 'delete-report:not-found');
  }

  const numDeleted = await Report.query(sqldb.knex).deleteById(id);
  return numDeleted > 0;
}
