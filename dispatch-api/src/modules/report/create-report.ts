import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { Report } from './sqldb';
import { ReportType, ReportStatus } from './sqldb/types';

interface CreateReportParams {
  sqldb: SqlDbSource;
  organizationId: number;
  userId: number;
  report: {
    type: ReportType;
    filename?: string;
    filepath?: string;
    status: ReportStatus;
  };
}

export async function createReport({
  sqldb,
  organizationId,
  userId,
  report,
}: CreateReportParams): Promise<Report> {
  // Validate that the organization exists
  const organization = await sqldb.organization(organizationId);

  if (!organization) {
    throw new ApolloError('Invalid organization', 'create-report:organization');
  }

  const { type, filename, filepath, status } = report;

  // Create the report record
  return Report.query(sqldb.knex).insertAndFetch({
    type,
    filename: filename || null,
    filepath: filepath || null,
    status,
    organizationId,
    userId,
  });
}
