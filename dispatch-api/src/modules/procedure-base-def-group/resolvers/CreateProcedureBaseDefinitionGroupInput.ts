import { IsHexColor, Length, MaxLength, ValidateIf } from 'class-validator';
import { Field, ID, InputType } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';
import { ProcedureGroupType } from '../sqldb/types';

@InputType()
export default class CreateProcedureBaseDefinitionGroupInput {
  @Field(() => ID)
  @IsNumberID()
  marketplaceId!: string;

  @Field()
  @MaxLength(64)
  name!: string;

  @Field(() => ProcedureGroupType)
  type!: ProcedureGroupType;

  @Field({ nullable: true })
  @Length(1, 50)
  thumbnailToken?: string;

  @Field({ nullable: true })
  @Length(1, 50)
  bannerToken?: string;

  @Field({ nullable: true })
  description?: string;

  @Field(() => String, { nullable: true })
  @ValidateIf((o) => o.bgcolor)
  @IsHexColor()
  bgcolor?: string;

  @Field(() => String, { nullable: true })
  @ValidateIf((o) => o.fontColor)
  @IsHexColor()
  fontColor?: string;
}
