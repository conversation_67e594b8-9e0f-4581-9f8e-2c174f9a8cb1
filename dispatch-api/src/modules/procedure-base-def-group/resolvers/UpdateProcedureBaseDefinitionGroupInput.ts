import {
  ArrayUnique,
  IsHexColor,
  Length,
  MaxLength,
  ValidateIf,
} from 'class-validator';
import { Field, ID, InputType } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';

@InputType()
export default class UpdateProcedureBaseDefinitionGroupInput {
  @Field(() => ID)
  @IsNumberID()
  id!: string;

  @Field({ nullable: true })
  @MaxLength(64)
  name?: string;

  @Field({ nullable: true })
  @Length(1, 50)
  thumbnailToken?: string;

  @Field({ nullable: true })
  @Length(1, 50)
  bannerToken?: string;

  @Field(() => [ID], { nullable: true })
  @IsNumberID({ each: true })
  @ArrayUnique()
  groupIds?: string[];

  @Field(() => [ID], { nullable: true })
  @IsNumberID({ each: true })
  @ArrayUnique()
  baseDefinitionIds?: string[];

  @Field(() => [ID], { nullable: true })
  @IsNumberID({ each: true })
  @ArrayUnique()
  featuredBaseDefIds?: string[];

  @Field({ nullable: true })
  description?: string;

  @Field(() => String, { nullable: true })
  @ValidateIf((o) => o.bgcolor)
  @IsHexColor()
  bgcolor?: string;

  @Field(() => String, { nullable: true })
  @ValidateIf((o) => o.fontColor)
  @IsHexColor()
  fontColor?: string;
}
