import { ApolloError, UserInputError } from 'apollo-server';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import validator from 'validator';
import { getConfig } from '../../config';
import { SqlDbSource } from '../../datasources';
import { render } from '../email/notification/template';
import { send } from '../email/sendgrid';
import { User } from './sqldb';
import { getUser, getUserByEmail } from './sqldb/queries';

export function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 10);
}

const userSecret = (user: User) =>
  `${getConfig().auth.secret}${user.passwordHash}${user.email}`;

interface ResetJwtPayload {
  id: number;
  action: 'reset-password';
}

interface RequestResetPasswordParams {
  sqldb: SqlDbSource;
  email: string;
}

export async function requestResetPassword(
  params: RequestResetPasswordParams,
): Promise<boolean> {
  const { sqldb, email: emailInput } = params;

  const email = validator.normalizeEmail(emailInput, {
    gmail_remove_subaddress: false,
  });

  if (!email) {
    throw new UserInputError('Invalid email', { invalidArgs: ['email'] });
  }

  const user = await getUserByEmail(sqldb.knex, { email });

  if (user) {
    const payload: ResetJwtPayload = {
      id: user.id,
      action: 'reset-password',
    };

    const token = jwt.sign(payload, userSecret(user), {
      expiresIn: '2h',
    });

    await send({
      to: email,
      subject: 'Your password reset link',
      template: render({
        title: 'We received a request to reset your NomadMD password',
        messages: [
          {
            text: 'To reset your password, click the button below.',
          },
        ],
        action: {
          text: 'Reset password',
          url: `${
            getConfig().email.webUrlOrigin
          }/reset-password?token=${token}`,
        },
      }),
    });
  }

  return true;
}

interface ResetPasswordParams {
  sqldb: SqlDbSource;
  token: string;
  password: string;
}

export async function resetPassword(
  params: ResetPasswordParams,
): Promise<boolean> {
  const { sqldb, token, password } = params;

  const decoded = jwt.decode(token) as ResetJwtPayload | null;
  const id = decoded?.id;
  const action = decoded?.action;

  if (action !== 'reset-password') {
    throw new ApolloError('Invalid token', 'reset-password:token');
  }

  const user = id && (await getUser(sqldb.knex, { id }));

  if (!user) {
    throw new ApolloError('Invalid token', 'reset-password:token');
  }

  try {
    jwt.verify(token, userSecret(user));
  } catch (err) {
    throw new ApolloError('Invalid token', 'reset-password:token');
  }

  if (await bcrypt.compare(password, user.passwordHash)) {
    throw new ApolloError(
      'The new password must be different from your current password',
      'reset-password:password',
    );
  }

  await User.query(sqldb.knex)
    .patch({ passwordHash: await hashPassword(password) })
    .findById(user.id);

  await send({
    to: user.email,
    from: `NomadMD <<EMAIL>>`,
    subject: 'Your password was updated',
    template: render({
      messages: [
        {
          text: `Please contact us if you didn't make this change.`,
        },
      ],
      action: {
        text: 'View my account',
        url: `${getConfig().email.webUrlOrigin}/signin`,
      },
    }),
  });

  return true;
}
