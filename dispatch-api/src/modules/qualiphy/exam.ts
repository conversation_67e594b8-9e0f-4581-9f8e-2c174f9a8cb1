import fetch from 'node-fetch';
import { SqlDbSource } from '../../datasources';
import { qualiphyApiOrigin } from './integration';
import { QualiphyExam } from './sqldb';

interface ExamRequestBody {
  api_key: string;
}

interface QualiphyExamResponse {
  id: number;
  title: string;
}

async function getQualiphyExams(apiKey: string) {
  const url = `${qualiphyApiOrigin}/api/exam_list`;

  const body: ExamRequestBody = {
    api_key: apiKey,
  };

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (data.exams) {
      return data.exams as QualiphyExamResponse[];
    }

    return [];
  } catch (err) {
    console.error(err);
    return [];
  }
}

interface UpsertExamProcedureParams {
  sqldb: SqlDbSource;
  procedureIds?: string[];
  exam: {
    id: number;
    refills?: number;
    expiresAfter?: number;
  };
}

export async function updateQualiphyExam(
  params: UpsertExamProcedureParams,
): Promise<QualiphyExam | null> {
  const { sqldb, exam, procedureIds } = params;

  const procedureDefinitions = procedureIds?.map((id) => ({ id: Number(id) }));

  return QualiphyExam.query(sqldb.knex).upsertGraphAndFetch(
    {
      ...exam,
      ...(procedureDefinitions && { procedureDefinitions }),
    },
    {
      relate: ['procedureDefinitions'],
      unrelate: ['procedureDefinitions'],
    },
  );
}

interface RefreshQualiphyExamsParams {
  sqldb: SqlDbSource;
  organizationId: number;
}

export async function refreshQualiphyExams(params: RefreshQualiphyExamsParams) {
  const { sqldb, organizationId } = params;
  const qualiphy = await sqldb.qualiphyIntegrationForOrg(organizationId);

  if (!qualiphy?.encryptedApiKey) {
    return [];
  }

  const fetchExistingExams = qualiphy
    .$relatedQuery('exams', sqldb.knex)
    .orderBy('index', 'asc')
    .withGraphFetched('procedureDefinitions');

  const [existingExams, fetchedExams] = await Promise.all([
    fetchExistingExams,
    getQualiphyExams(qualiphy.encryptedApiKey.decrypt()),
  ]);

  const existingIds = existingExams.map((e) => e.qualiphyId);
  const fetchedIds = fetchedExams.map((e) => e.id);
  const archivedIds = existingIds.filter((id) => !fetchedIds.includes(id));

  if (archivedIds.length) {
    await QualiphyExam.query(sqldb.knex)
      .whereIn('qualiphyId', archivedIds)
      .patch({ archived: true });
  }

  // Update the existing records without needing to query again
  for (let i = 0; i < existingExams.length; i++) {
    const exam = existingExams[i];
    if (!fetchedIds.includes(exam.qualiphyId) && !exam.archived) {
      exam.archived = true;
    }
  }

  const existingLength = existingExams.length;

  let newExams: QualiphyExam[] = [];

  const records = fetchedExams
    .filter((e) => !existingIds.includes(e.id))
    .map((exam, index) => ({
      qualiphyId: exam.id,
      qualiphyIntegrationId: qualiphy.id,
      refills: 99,
      expiresAfter: 180,
      index: existingLength + index,
      title: exam.title,
      archived: false,
    }));

  if (records.length) {
    newExams = await QualiphyExam.query(sqldb.knex).insertGraph(records);
  }

  return [...existingExams, ...newExams];
}
