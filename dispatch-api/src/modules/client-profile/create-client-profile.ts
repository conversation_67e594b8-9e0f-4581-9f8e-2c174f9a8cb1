import { ApolloError } from 'apollo-server';
import { pick } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { fullName, parsePhoneNumber } from '../common/util';
import { Organization } from '../organization/sqldb';
import { ClientProfile } from './sqldb';
import { ClientProfileFields } from './sqldb/types';

interface CreateClientProfileParams {
  sqldb: SqlDbSource;
  organizationId?: number | null;
  clientProfile: Partial<ClientProfileFields>;
}

export async function createClientProfile(
  params: CreateClientProfileParams,
): Promise<ClientProfile> {
  const { sqldb, organizationId } = params;

  const clientProfile = pick(params.clientProfile, [
    'email',
    'givenName',
    'familyName',
    'phone',
    'address',
    'dob',
    'tzid',
    'sexAssignedAtBirth',
  ]);

  const organizations: Partial<Organization>[] = [];
  let emrInstanceId;

  if (organizationId) {
    const organization = await sqldb.organization(organizationId);

    if (!organization) {
      throw new ApolloError(
        'Invalid organization',
        'create-client-profile:organization',
      );
    }

    organizations.push({ id: organization.id });
    emrInstanceId = organization.emrInstanceId;
  }

  if (!fullName(clientProfile.givenName, clientProfile.familyName).length) {
    throw new ApolloError('Name is required', 'create-client-profile:name');
  }

  const phone = clientProfile.phone && parsePhoneNumber(clientProfile.phone);

  if (!phone) {
    throw new ApolloError(
      'Invalid phone number',
      'create-client-profile:phone',
    );
  }

  return ClientProfile.query(sqldb.knex).insertGraphAndFetch(
    {
      ...clientProfile,
      phone,
      organizations,
      ...(emrInstanceId && {
        patient: { emrInstanceId },
      }),
    },
    {
      relate: ['organizations'],
    },
  );
}
