import { ForbiddenError } from 'apollo-server';
import { AnyQueryBuilder, Page } from 'objection';
import {
  Arg,
  Ctx,
  FieldResolver,
  ID,
  Query,
  Resolver,
  Root,
} from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { authorizeClientProfile } from '../../appointment/common';
import { Appointment } from '../../appointment/sqldb';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { authorizeEmrOrganization } from '../../emr/common';
import { Patient } from '../../emr/sqldb';
import { getMarketplaces } from '../../marketplace/sqldb/queries';
import { Organization } from '../../organization/sqldb';
import { getOrganizations } from '../../organization/sqldb/queries';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { ClientProfilePage } from '../ClientProfilePage';
import { ClientProfile } from '../sqldb';
import { getPaginatedClientProfiles } from '../sqldb/queries';
import ClientProfilePageInput from './ClientProfilePageInput';
import { MarketplaceUser } from '../../marketplace/sqldb';
import { authorizeMarketplaceUser } from '../../marketplace/common';

@Resolver(() => ClientProfile)
export default class ClientProfileResolver {
  @FieldResolver(() => [Appointment])
  async appointments(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() clientProfile: ClientProfile,
  ): Promise<Appointment[]> {
    if (await authorizeClientProfile(user, clientProfile, { sqldb })) {
      return (
        clientProfile.appointments ||
        clientProfile
          .$relatedQuery('appointments', sqldb.knex)
          .withGraphFetched(
            '[participants.[responses, profile(withArchived), clientProfile(withArchived)], procedureBaseDefs(withArchived)]',
          )
      );
    }

    return [];
  }

  @FieldResolver(() => Patient, { nullable: true })
  async patient(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() clientProfile: ClientProfile,
  ): Promise<Patient | null> {
    const patient =
      clientProfile.patient ||
      (await clientProfile.$relatedQuery('patient', sqldb.knex));

    if (
      patient &&
      (await authorizeEmrOrganization(user, patient.emrInstanceId, { sqldb }))
    ) {
      return patient;
    }

    return null;
  }

  @FieldResolver(() => String)
  async membership(
    @CurrentUser() user: User,
    @Root() { marketplaceUser, marketplaceUsers = [] }: ClientProfile,
  ): Promise<string> {
    return (
      [marketplaceUser, ...marketplaceUsers]
        .map((u) => u?.membership)
        .find(Boolean) ?? ''
    );
  }

  @FieldResolver(() => [Organization])
  async organizations(
    @CurrentUser() user: User,
    @Root() clientProfile: ClientProfile,
  ): Promise<Organization[]> {
    return clientProfile.organizations ?? [];
  }

  @FieldResolver(() => [MarketplaceUser])
  async marketplaceUsers(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() clientProfile: ClientProfile,
    @Arg('groupId', () => ID, { nullable: true }) groupId?: string,
  ): Promise<MarketplaceUser[]> {
    const marketplaceUser =
      clientProfile.marketplaceUser ||
      (await clientProfile.$relatedQuery('marketplaceUser', sqldb.knex));

    const marketplaceUsers =
      clientProfile.marketplaceUsers ||
      (await clientProfile.$relatedQuery('marketplaceUsers', sqldb.knex));

    const users = [marketplaceUser, ...marketplaceUsers].filter(
      (u) =>
        Boolean(u) &&
        (!groupId || u.groupId === intFromID(groupId)) &&
        authorizeMarketplaceUser(user, u.groupId, { sqldb }),
    );

    return MarketplaceUser.fetchGraph(
      users,
      '[memberships, packageItems, paymentInstruments]',
      {
        transaction: sqldb.knex,
      },
    );
  }

  @FieldResolver(() => ID, { nullable: true })
  async marketplaceUserId(
    @Root() clientProfile: ClientProfile,
  ): Promise<number | undefined> {
    return clientProfile.marketplaceUser?.id;
  }

  @Query(() => ClientProfile, { nullable: true })
  async clientProfile(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Arg('id', () => ID) id: string,
  ): Promise<ClientProfile | null> {
    const clientProfileId = intFromID(id) as number;
    const clientProfile =
      await dataSources.sqldb.clientProfile(clientProfileId);

    if (
      !clientProfile ||
      !(await authorizeClientProfile(user, clientProfile, {
        sqldb: dataSources.sqldb,
      }))
    ) {
      throw new ForbiddenError('Not authorized (clientProfile)');
    }

    return clientProfile;
  }

  @Query(() => ClientProfilePage)
  async clientProfiles(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('page', { defaultValue: {} }) page: ClientProfilePageInput,
  ): Promise<ClientProfilePage> {
    let result: Page<ClientProfile> = { total: 0, results: [] };
    const subqueries: AnyQueryBuilder[] = [];

    const isRoot = authorize(user, ['appointments:full', 'appointments:list']);

    if (!isRoot) {
      const organizations = await getOrganizations(sqldb.knex);
      const marketplaces = await getMarketplaces(sqldb.knex);

      const authorizedOrgs = organizations.filter((organization) =>
        authorize(
          user,
          [
            'organization.appointments:full',
            'organization.appointments:list',
            'organization.appointments:self',
          ],
          {
            scope: RoleScope.ORGANIZATION,
            resourceId: organization.id,
          },
        ),
      );

      if (authorizedOrgs.length) {
        subqueries.push(
          ClientProfile.relatedQuery('organizations').whereIn(
            'organizations.id',
            authorizedOrgs.map((org) => org.id),
          ),
        );
      }

      const authorizedGroups = marketplaces.filter(({ id: marketplaceId }) =>
        authorize(
          user,
          ['marketplace.appointments:full', 'marketplace.appointments:list'],
          {
            scope: RoleScope.MARKETPLACE,
            resourceId: marketplaceId,
          },
        ),
      );

      if (authorizedGroups.length) {
        subqueries.push(
          ClientProfile.relatedQuery('marketplaceUser').whereIn(
            'marketplaceUser.groupId',
            authorizedGroups.map((m) => m.groupId),
          ),
        );
      }
    }

    if (isRoot || subqueries.length) {
      let query = getPaginatedClientProfiles(sqldb.knex, page).withGraphJoined(
        'marketplaceUser',
      );

      if (subqueries.length) {
        query = query.where((or) => {
          subqueries.forEach((subquery, index) => {
            or = index ? or.orWhereExists(subquery) : or.whereExists(subquery);
          });
        });
      }

      result = await query;
    }

    return {
      totalCount: result.total,
      data: result.results,
    };
  }
}
