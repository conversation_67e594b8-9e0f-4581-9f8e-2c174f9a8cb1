import { ForbiddenError } from 'apollo-server';
import { omit } from 'lodash';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { createClientProfile } from '../create-client-profile';
import { ClientProfile } from '../sqldb';
import CreateClientProfileInput from './CreateClientProfileInput';

@Resolver()
export default class CreateClientProfileResolver {
  @Mutation(() => ClientProfile, { nullable: true })
  async createClientProfile(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Arg('input') input: CreateClientProfileInput,
  ): Promise<ClientProfile | null> {
    const organizationId = intFromID(input.organizationId) as number;

    if (
      !authorize(user, ['appointments:full']) &&
      !authorize(
        user,
        ['organization.appointments:full', 'organization.appointments:self'],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: organizationId,
        },
      )
    ) {
      throw new ForbiddenError('Not authorized (createClientProfile)');
    }

    const clientProfile = omit(input, 'organizationId');

    return createClientProfile({
      sqldb: dataSources.sqldb,
      organizationId,
      clientProfile,
    });
  }
}
