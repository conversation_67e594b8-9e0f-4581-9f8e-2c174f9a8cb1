import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ISO8601,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>In,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { Field, InputType } from 'type-graphql';
import { ClientProfile } from '../sqldb';

@InputType()
export default class ClientProfileInput implements Partial<ClientProfile> {
  @Field({ nullable: true })
  @IsEmail()
  email?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  givenName?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  familyName?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  @IsPhoneNumber('US')
  phone?: string;

  @Field({ nullable: true })
  @MaxLength(1024)
  internalNotes?: string;

  @Field({ nullable: true })
  @MaxLength(255)
  address?: string;

  @Field({ nullable: true, description: 'yyyy-mm-dd' })
  @MaxLength(10)
  @IsISO8601({ strict: true })
  dob?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  tzid?: string;

  @Field({ nullable: true })
  @IsIn(['male', 'female', 'unknown'], {
    message: 'Must be one of: male, female, unknown',
  })
  sexAssignedAtBirth?: string;
}
