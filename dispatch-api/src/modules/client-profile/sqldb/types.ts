import {
  FilterNumberFields,
  FilterStringFields,
  SortDirection,
} from '../../common/sqldb/types';

export interface ClientProfileFields {
  email: string;
  givenName: string;
  familyName: string;
  internalNotes?: string;
  phone: string;
  address: string;
  dob: string;
  tzid: string;
  sexAssignedAtBirth?: string;
}

export enum ClientProfileSortField {
  ID = 'id',
  EMAIL = 'email',
  GIVENNAME = 'givenName',
  FAMILYNAME = 'familyName',
  FULLNAME = 'fullName',
  PHONE = 'phone',
  ADDRESS = 'address',
  DOB = 'dob',
  CREATEDAT = 'createdAt',
  UPDATEDAT = 'updatedAt',
}

export interface ClientProfileSortFields {
  field: ClientProfileSortField;
  direction: SortDirection;
}

export interface ClientProfileFilterFields {
  id?: FilterNumberFields;
  email?: FilterStringFields;
  givenName?: FilterStringFields;
  familyName?: FilterStringFields;
  fullName?: FilterStringFields;
  phone?: FilterStringFields;
  address?: FilterStringFields;
  organization?: FilterNumberFields;
  marketplaceGroup?: FilterNumberFields;
}
