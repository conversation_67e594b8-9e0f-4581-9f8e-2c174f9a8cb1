import { Model, Pojo, RelationMappings } from 'objection';
import { Field, ID, ObjectType, registerEnumType } from 'type-graphql';
import { Appointment, AppointmentRequest } from '../../appointment/sqldb';
import { ArchivableModel, BaseModel } from '../../common/sqldb';
import { Patient } from '../../emr/sqldb';
import MarketplaceUser from '../../marketplace/sqldb/MarketplaceUser';
import { Organization } from '../../organization/sqldb';
import { ClientProfileFields, ClientProfileSortField } from './types';

registerEnumType(ClientProfileSortField, {
  name: 'ClientProfileSortFields',
});

@ObjectType()
export default class ClientProfile
  extends ArchivableModel
  implements ClientProfileFields
{
  @Field(() => ID)
  readonly id!: number;

  @Field()
  email!: string;

  @Field()
  givenName!: string;

  @Field()
  familyName!: string;

  @Field({ nullable: true })
  internalNotes?: string;

  @Field()
  phone!: string;

  @Field()
  address!: string;

  @Field()
  dob!: string;

  @Field()
  tzid!: string;

  @Field({ nullable: true })
  sexAssignedAtBirth?: string;

  appointments?: Appointment[];
  appointmentRequests?: AppointmentRequest[];
  organizations?: Organization[];
  patient?: Patient;
  marketplaceUser?: MarketplaceUser;
  marketplaceUsers?: MarketplaceUser[];

  static tableName = 'clientProfiles';

  static relationMappings = (): RelationMappings => ({
    appointments: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../appointment/sqldb').Appointment,
      join: {
        from: 'clientProfiles.id',
        through: {
          from: 'appointmentParticipants.clientProfileId',
          to: 'appointmentParticipants.appointmentId',
        },
        to: 'appointments.id',
      },
    },
    appointmentRequests: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../appointment/sqldb').AppointmentRequest,
      join: {
        from: 'clientProfiles.id',
        through: {
          from: 'appointmentRequestsClientProfiles.clientProfileId',
          to: 'appointmentRequestsClientProfiles.requestId',
        },
        to: 'appointmentRequests.id',
      },
    },
    organizations: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../organization/sqldb').Organization,
      join: {
        from: 'clientProfiles.id',
        through: {
          from: 'organizationsClientProfiles.clientProfileId',
          to: 'organizationsClientProfiles.organizationId',
        },
        to: 'organizations.id',
      },
    },
    patient: {
      relation: Model.HasOneRelation,
      modelClass: require('../../emr/sqldb').Patient,
      join: {
        from: 'clientProfiles.id',
        to: 'patients.clientProfileId',
      },
    },
    marketplaceUser: {
      relation: Model.HasOneRelation,
      modelClass: require('../../marketplace/sqldb').MarketplaceUser,
      join: {
        from: 'clientProfiles.id',
        to: 'marketplaceUsers.clientProfileId',
      },
    },
    marketplaceUsers: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../marketplace/sqldb').MarketplaceUser,
      join: {
        from: 'clientProfiles.id',
        through: {
          from: 'marketplaceUsersClientProfiles.clientProfileId',
          to: 'marketplaceUsersClientProfiles.marketplaceUserId',
        },
        to: 'marketplaceUsers.id',
      },
    },
  });

  $parseDatabaseJson(json: Pojo): Pojo {
    json = super.$parseDatabaseJson(json);
    BaseModel.applyDefault(json, 'email', '');
    BaseModel.applyDefault(json, 'givenName', '');
    BaseModel.applyDefault(json, 'familyName', '');
    BaseModel.applyDefault(json, 'internalNotes', '');
    BaseModel.applyDefault(json, 'phone', '');
    BaseModel.applyDefault(json, 'address', '');
    BaseModel.applyDefault(json, 'dob', '');
    BaseModel.applyDefault(json, 'tzid', '');
    BaseModel.applyDefault(json, 'sexAssignedAtBirth', null);
    return json;
  }
}
