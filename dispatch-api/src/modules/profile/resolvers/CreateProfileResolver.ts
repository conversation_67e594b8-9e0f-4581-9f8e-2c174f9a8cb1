import { ApolloError, ForbiddenError } from 'apollo-server';
import { pick } from 'lodash';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { guardPrivilegeElevation } from '../../role/authorize-role';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import createProfile from '../create-profile';
import { Profile } from '../sqldb';
import CreateProfileInput from './CreateProfileInput';

@Resolver()
export default class CreateProfileResolver {
  @Mutation(() => Profile)
  async createProfile(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Arg('input', () => CreateProfileInput) input: CreateProfileInput,
  ): Promise<Profile> {
    const organizationId = intFromID(input.organizationId) as number;
    const role = await dataSources.sqldb.role(intFromID(input.roleId));

    if (!role) {
      throw new ApolloError('Invalid role', 'create-profile:role');
    }

    if (
      !authorize(user, ['profiles:full']) &&
      !authorize(
        user,
        ['organization.profiles:full', 'organization.profiles:create'],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: role.organizationId,
          authorizeRole: (userRole) =>
            userRole.permissionsSet.has('organization.roles:full') ||
            guardPrivilegeElevation(role, userRole, { throws: true }),
        },
      )
    ) {
      throw new ForbiddenError('Not authorized (createProfile)');
    }

    const { sendInvitation } = input;

    const profile = pick(input, [
      'email',
      'givenName',
      'familyName',
      'title',
      'phone',
      'address',
      'tzid',
      'color',
      'allowSmsNotifications',
    ]);

    return createProfile({
      sqldb: dataSources.sqldb,
      organizationId,
      roleId: role.id,
      profile,
      sendInvitation,
    });
  }
}
