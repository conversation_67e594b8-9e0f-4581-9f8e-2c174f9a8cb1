import {
  <PERSON>Date,
  IsEmail,
  IsHexColor,
  IsPhoneNumber,
  Max<PERSON>ength,
} from 'class-validator';
import { Field, InputType } from 'type-graphql';
import { IsTimeZone } from '../../common/type-graphql';
import { Profile } from '../sqldb';

@InputType()
export default class ProfileInput implements Partial<Profile> {
  @Field({ nullable: true })
  @IsEmail()
  email?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  givenName?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  familyName?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  title?: string;

  @Field({ nullable: true })
  @MaxLength(7)
  @IsHexColor()
  color?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  @IsPhoneNumber('US')
  phone?: string;

  @Field({ nullable: true })
  @MaxLength(255)
  address?: string;

  @Field({ nullable: true })
  @IsTimeZone()
  tzid?: string;

  @Field(() => Date, { nullable: true })
  @IsDate()
  availableUntil?: Date;

  @Field(() => Boolean, { nullable: true })
  allowSmsNotifications?: boolean;
}
