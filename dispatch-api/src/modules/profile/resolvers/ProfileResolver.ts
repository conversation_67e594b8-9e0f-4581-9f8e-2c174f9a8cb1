import { ForbiddenError } from 'apollo-server';
import { Page } from 'objection';
import {
  Arg,
  Ctx,
  FieldResolver,
  ID,
  Query,
  Resolver,
  Root,
} from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { AppointmentPage } from '../../appointment/AppointmentPage';
import AppointmentPageInput from '../../appointment/resolvers/AppointmentPageInput';
import { Appointment } from '../../appointment/sqldb';
import { getPaginatedAppointments } from '../../appointment/sqldb/queries';
import { ParticipantType } from '../../appointment/sqldb/types';
import Availability from '../../availability/sqldb/Availability';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { Organization } from '../../organization/sqldb';
import { ProcedureProfile } from '../../procedure/sqldb';
import { Role } from '../../role/sqldb';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { Profile } from '../sqldb';
import { getProfiles } from '../sqldb/queries';

const authorizeAvailability = (user: User, profile: Profile): boolean =>
  authorize(user, ['availability:full', 'availability:list']) ||
  authorize(
    user,
    ['organization.availability:full', 'organization.availability:list'],
    {
      scope: RoleScope.ORGANIZATION,
      resourceId: profile.organizationId,
    },
  );

@Resolver(() => Profile)
export default class ProfileResolver {
  @FieldResolver(() => Organization, { nullable: true })
  async organization(
    @Ctx() { dataSources }: ResolverContext,
    @Root() profile: Profile,
  ): Promise<Organization | null | undefined> {
    return (
      profile.organization ||
      dataSources.sqldb.organization(profile.organizationId)
    );
  }

  @FieldResolver(() => [Role])
  async roles(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() profile: Profile,
  ): Promise<Role[]> {
    if (
      profile.userId !== user.id &&
      !authorize(user, ['roles:full', 'roles:list']) &&
      !authorize(user, ['organization.roles:full', 'organization.roles:list'], {
        scope: RoleScope.ORGANIZATION,
        resourceId: profile.organizationId,
      })
    ) {
      return [];
    }

    return (
      profile.roles ||
      (await dataSources.sqldb.profile(profile.id))?.roles ||
      []
    );
  }

  @FieldResolver(() => [ProcedureProfile])
  async procedureProfiles(
    @Ctx() { dataSources }: ResolverContext,
    @Root() profile: Profile,
  ): Promise<ProcedureProfile[]> {
    if (profile.procedureProfiles) {
      return profile.procedureProfiles;
    }

    return profile
      .$relatedQuery('procedureProfiles', dataSources.sqldb.knex)
      .withGraphFetched('procedureDefs');
  }

  @FieldResolver(() => [Availability])
  async availabilities(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() profile: Profile,
  ): Promise<Availability[]> {
    if (profile.userId !== user.id && !authorizeAvailability(user, profile)) {
      return [];
    }

    const now = new Date();

    if (profile.availabilities) {
      return profile.availabilities.filter((a) => a.repeat() || a.end > now);
    }

    return profile
      .$relatedQuery('availabilities', sqldb.knex)
      .withGraphJoined('repeatRule')
      .modify('afterNow');
  }

  @FieldResolver(() => AppointmentPage)
  async appointments(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() profile: Profile,
    @Arg('page', { defaultValue: {} }) page: AppointmentPageInput,
  ): Promise<AppointmentPage> {
    let result: Page<Appointment> = { total: 0, results: [] };

    if (
      profile.userId === user.id ||
      authorize(user, ['appointments:full', 'appointments:list']) ||
      authorize(
        user,
        [
          'organization.appointments:full',
          'organization.appointments:list',
          'organization.appointments:self',
        ],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: profile.organizationId,
        },
      )
    ) {
      result = await getPaginatedAppointments(sqldb.knex, page)
        .whereExists(
          Appointment.relatedQuery('participants')
            .where('type', ParticipantType.PRACTITIONER)
            .where('profileId', profile.id),
        )
        .withGraphFetched(
          '[participants, procedureBaseDefs(withArchived), checkout]',
        );
    }

    return {
      totalCount: result.total,
      data: result.results,
    };
  }

  @FieldResolver(() => Date, { nullable: true })
  availableUntil(
    @CurrentUser() user: User,
    @Root() profile: Profile,
  ): Date | null {
    if (profile.userId !== user.id && !authorizeAvailability(user, profile)) {
      return null;
    }

    return profile.availableUntil && profile.availableUntil > new Date()
      ? profile.availableUntil
      : null;
  }

  @Query(() => [Profile])
  async profiles(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
  ): Promise<Profile[]> {
    if (!authorize(user, ['profiles:full', 'profiles:list'])) {
      throw new ForbiddenError('Not authorized (profiles)');
    }

    return getProfiles(dataSources.sqldb.knex).withGraphJoined('organization');
  }

  @Query(() => Profile, { nullable: true })
  async profile(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Arg('id', () => ID) id: string,
  ): Promise<Profile | null | undefined> {
    const profileId = intFromID(id) as number;
    const profile = await dataSources.sqldb.profile(profileId);

    if (
      profile?.userId !== user.id &&
      !authorize(user, ['profiles:full', 'profiles:list']) &&
      !authorize(
        user,
        ['organization.profiles:full', 'organization.profiles:list'],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: profile?.organizationId,
        },
      )
    ) {
      throw new ForbiddenError('Not authorized (profile)');
    }

    return profile;
  }
}
