// changed to require because it was causing some issues with esbuilds compiles
const Hashids = require('hashids/cjs');
import { Model, Pojo, RelationMappings } from 'objection';
import { Field, ID, ObjectType } from 'type-graphql';
import { Appointment } from '../../appointment/sqldb';
import { Availability } from '../../availability/sqldb';
import { ArchivableModel, BaseModel } from '../../common/sqldb';
import { Organization } from '../../organization/sqldb';
import { ProcedureProfile } from '../../procedure/sqldb';
import { Role } from '../../role/sqldb';
import { ProfileFields } from './types';

const hashids = new Hashids('dR!P');

@ObjectType()
export default class Profile extends ArchivableModel implements ProfileFields {
  @Field(() => ID)
  readonly id!: number;

  @Field(() => ID, { nullable: true })
  userId?: number | null;

  organizationId!: number;

  @Field()
  email!: string;

  @Field()
  givenName!: string;

  @Field()
  familyName!: string;

  @Field()
  title!: string;

  @Field()
  phone!: string;

  @Field()
  address!: string;

  @Field()
  tzid!: string;

  @Field()
  color!: string;

  @Field(() => Boolean)
  allowSmsNotifications!: boolean;

  availableUntil?: Date;
  lastCreatedAt?: Date;

  @Field()
  get pid(): string {
    return hashids.encode(this.id);
  }

  organization?: Organization;
  roles?: Role[];
  procedureProfiles?: ProcedureProfile[];
  availabilities?: Availability[];
  appointments?: Appointment[];

  static tableName = 'profiles';

  static relationMappings = (): RelationMappings => ({
    organization: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('../../organization/sqldb').Organization,
      join: {
        from: 'profiles.organizationId',
        to: 'organizations.id',
      },
    },
    roles: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../role/sqldb').Role,
      join: {
        from: 'profiles.id',
        through: {
          from: 'profilesRoles.profileId',
          to: 'profilesRoles.roleId',
        },
        to: 'roles.id',
      },
    },
    procedureProfiles: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../procedure/sqldb').ProcedureProfile,
      join: {
        from: 'profiles.id',
        through: {
          from: 'procedureProfilesUserProfiles.profileId',
          to: 'procedureProfilesUserProfiles.procedureProfileId',
        },
        to: 'procedureProfiles.id',
      },
    },
    availabilities: {
      relation: Model.HasManyRelation,
      modelClass: require('../../availability/sqldb').Availability,
      join: {
        from: 'profiles.id',
        to: 'availabilities.profileId',
      },
    },
    appointments: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../appointment/sqldb').Appointment,
      join: {
        from: 'profiles.id',
        through: {
          from: 'appointmentParticipants.profileId',
          to: 'appointmentParticipants.appointmentId',
        },
        to: 'appointments.id',
      },
    },
  });

  $parseDatabaseJson(json: Pojo): Pojo {
    json = super.$parseDatabaseJson(json);
    BaseModel.toDate(json, 'availableUntil');
    BaseModel.toDate(json, 'lastCreatedAt');
    BaseModel.applyDefault(json, 'email', '');
    BaseModel.applyDefault(json, 'givenName', '');
    BaseModel.applyDefault(json, 'familyName', '');
    BaseModel.applyDefault(json, 'title', '');
    BaseModel.applyDefault(json, 'color', '#40c4ff');
    BaseModel.applyDefault(json, 'phone', '');
    BaseModel.applyDefault(json, 'address', '');
    BaseModel.applyDefault(json, 'tzid', '');
    BaseModel.applyDefault(json, 'allowSmsNotifications', true);
    return json;
  }
}
