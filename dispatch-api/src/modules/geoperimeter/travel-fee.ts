import { ApolloError } from 'apollo-server';
import { isPointInPolygon, isPointWithinRadius } from 'geolib';
import { min } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { getGeoperimetersByOrganization } from './sqldb/queries';
import { GeoperimeterType } from './sqldb/types';

interface Coordinates {
  lat: number;
  lng: number;
}

interface CalculateTravelFeeParams {
  sqldb: SqlDbSource;
  organizationId: number;
  lat: number;
  lng: number;
}

export async function calculateTravelFee(params: CalculateTravelFeeParams) {
  const { sqldb, organizationId, lat, lng } = params;

  const org = await sqldb.organization(organizationId);

  if (!org) {
    throw new ApolloError(
      'Organization not found',
      'calculate-travel-fee:organization',
    );
  }

  const geoperimeters = await getGeoperimetersByOrganization(sqldb.knex, {
    id: org.id,
  });

  const relevantGeoperimeters = geoperimeters.filter((geo) => {
    if (
      geo.type === GeoperimeterType.CIRCLE &&
      geo.lat != null &&
      geo.lng != null &&
      geo.radius != null
    ) {
      return isPointWithinRadius(
        { lat, lng },
        { lat: geo.lat, lng: geo.lng },
        geo.radius,
      );
    }

    if (geo.type === GeoperimeterType.POLYGON && geo.paths) {
      try {
        const polygon = JSON.parse(geo.paths) as Coordinates[];
        return isPointInPolygon({ lat, lng }, polygon);
      } catch {
        // invalid polygon
        return false;
      }
    }

    return false;
  });

  return min(relevantGeoperimeters.map((geo) => geo.travelFee ?? 0)) ?? 0;
}
