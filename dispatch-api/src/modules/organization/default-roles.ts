import { RoleType, RoleScope } from '../role/sqldb/Role';
import { RoleSpecification } from '../role/specification';

export const ORGANIZATION_OWNER_ROLE_BASE = 'organization.owner.base';
export const ORGANIZATION_OWNER_ROLE = 'organization.owner';

export const defaultOrganizationRoles: RoleSpecification[] = [
  {
    inherits: ORGANIZATION_OWNER_ROLE_BASE,
    name: ORGANIZATION_OWNER_ROLE,
    scope: RoleScope.ORGANIZATION,
    type: RoleType.SYSTEM,
    permissions: [],
  },
  {
    name: 'organization.admin',
    scope: RoleScope.ORGANIZATION,
    type: RoleType.CUSTOM,
    permissions: [
      'organization.medications:full',
      'organization.medications:list',
      'organization.orders:full',
      'organization.orders:list',
      'organization.emr:full',
      'organization.appointments:self',
      'organization.appointments:full',
      'organization.appointments:list',
      'organization:update',
      'organization.profiles:create',
      'organization.profiles:list',
      'organization.profiles:full',
      'organization.profiles:self',
      'organization.roles:list',
      'organization.roles:full',
      'organization.procedure-defs:full',
      'organization.marketplaces:list',
      'organization.availability:list',
      'organization.availability:full',
      'organization.availability:self',
    ],
  },
  {
    name: 'organization.dispatch',
    scope: RoleScope.ORGANIZATION,
    type: RoleType.CUSTOM,
    permissions: [
      'organization.emr:full',
      'organization.profiles:self',
      'organization.profiles:list',
      'organization.profiles:full',
      'organization.orders:list',
      'organization.orders:full',
      'organization.medications:list',
      'organization.marketplaces:list',
      'organization.availability:self',
      'organization.availability:list',
      'organization.availability:full',
      'organization.appointments:self',
      'organization.appointments:list',
      'organization.appointments:full',
    ],
  },
  {
    name: 'organization.practitioner',
    scope: RoleScope.ORGANIZATION,
    type: RoleType.CUSTOM,
    permissions: [
      'organization.profiles:self',
      'organization.availability:self',
      'organization.appointments:self',
      'organization.medications:list',
      'organization.orders:list',
      'organization.marketplaces:list',
    ],
  },
];
