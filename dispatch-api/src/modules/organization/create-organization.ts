import { ApolloError } from 'apollo-server';
import { find } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { sendInvitation } from '../invitation';
import { getProfilesByOrganization } from '../profile/sqldb/queries';
import { rolesFromSpecification } from '../role/specification';
import {
  defaultOrganizationRoles,
  ORGANIZATION_OWNER_ROLE,
} from './default-roles';
import { Organization } from './sqldb';
import { OrganizationFields } from './sqldb/types';
import { parsePhoneNumber } from '../common/util';
import validator from 'validator';

interface CreateOrganizationParams {
  sqldb: SqlDbSource;
  organization: OrganizationFields;
  ownerEmails: string[];
  emrInstanceId?: number;
}

export default async function createOrganization(
  params: CreateOrganizationParams,
): Promise<Organization | null | undefined> {
  const { sqldb, organization, ownerEmails, emrInstanceId } = params;
  const {
    name,
    tzid,
    slackWebhookUrl = '',
    googleReviewsUrl = '',
    address = '',
    providesAtClinic = false,
    state,
    enablePractitionerSms = false,
    enableReceiptSending = false,
  } = organization;

  const email = organization.email
    ? (validator.normalizeEmail(organization?.email, {
        gmail_remove_subaddress: false,
      }) as string)
    : undefined;

  const phone = organization.phone
    ? (parsePhoneNumber(organization.phone) as string)
    : undefined;

  if (emrInstanceId) {
    if (!(await sqldb.emrInstance(emrInstanceId))) {
      throw new ApolloError(
        'Invalid EMR instance id',
        'create-organization:emr-instance',
      );
    }
  }

  if (providesAtClinic && !address) {
    throw new ApolloError(
      'Cannot enable clinic services without an address',
      'create-organization:address',
    );
  }

  try {
    const roles = await rolesFromSpecification({
      sqldb,
      specification: defaultOrganizationRoles,
    });

    const ownerRole = find(roles, { name: ORGANIZATION_OWNER_ROLE });

    if (!ownerRole) {
      throw new Error(`Missing system role '${ORGANIZATION_OWNER_ROLE}'`);
    }

    ownerRole['#id'] = 'ownerRole';

    const { id } = await sqldb.knex.transaction(async (trx) =>
      Organization.query(trx).insertGraph(
        {
          name,
          tzid,
          slackWebhookUrl,
          googleReviewsUrl,
          roles,
          phone,
          email,
          address,
          providesAtClinic,
          state,
          enablePractitionerSms,
          enableReceiptSending,
          profiles: ownerEmails.map((email) => ({
            email,
            roles: [{ '#ref': 'ownerRole' }],
            tzid,
          })),
          ...(emrInstanceId && { emrInstanceId }),
        },
        {
          allowRefs: true,
        },
      ),
    );

    const profiles = await getProfilesByOrganization(sqldb.knex, { orgId: id });

    profiles.map((profile) =>
      sendInvitation({
        sqldb,
        expiresIn: '2d',
        profile,
      }),
    );

    return sqldb.organization(id);
  } catch (err) {
    console.log(err);
    throw new ApolloError(
      'Error creating the organization record',
      'organization:create',
    );
  }
}
