import { Model, RelationMappings } from 'objection';
import { Field, ID, ObjectType, registerEnumType } from 'type-graphql';
import AvailabilityCoverageRange from '../../availability/AvailabilityCoverageRange';
import AvailabilityCoverage from '../../availability/sqldb/AvailabilityCoverage';
import { ClientProfile } from '../../client-profile/sqldb';
import { ArchivableModel, EncryptedKey } from '../../common/sqldb';
import { StateCode } from '../../common/sqldb/types';
import { EmrInstance } from '../../emr/sqldb';
import { Geoperimeter } from '../../geoperimeter/sqldb';
import { Medication } from '../../inventory/sqldb';
import { Marketplace } from '../../marketplace/sqldb';
import { PaymentAccount } from '../../payment/sqldb';
import { ProcedureDefinition, ProcedureProfile } from '../../procedure/sqldb';
import { Profile } from '../../profile/sqldb';
import QualiphyIntegration from '../../qualiphy/sqldb/QualiphyIntegration';
import { Role } from '../../role/sqldb';
import { OrganizationFields } from './types';

registerEnumType(StateCode, {
  name: 'StateCode',
});

@ObjectType()
export default class Organization
  extends ArchivableModel
  implements OrganizationFields
{
  @Field(() => ID)
  readonly id!: number;

  @Field()
  name!: string;

  @Field()
  tzid!: string;

  @Field()
  providesAtClinic!: boolean;

  @Field()
  enablePractitionerSms!: boolean;

  @Field()
  enableReceiptSending!: boolean;

  @Field(() => ID, { nullable: true })
  emrInstanceId?: number;

  @Field({ nullable: true })
  phone?: string;

  @Field({ nullable: true })
  email?: string;

  @Field(() => ID, { nullable: true })
  paymentAccountId?: number | null;

  @Field({ nullable: true })
  googleReviewsUrl?: string;

  @Field({ nullable: true })
  address?: string;

  @Field(() => StateCode, { nullable: true })
  state?: StateCode;

  slackWebhookUrl?: string;

  profiles?: Profile[];
  marketplaces?: Marketplace[];
  roles?: Role[];
  geoperimeters?: Geoperimeter[];
  procedureDefs?: ProcedureDefinition[];
  procedureProfiles?: ProcedureProfile[];
  clientProfiles?: ClientProfile[];
  medications?: Medication[];
  emrInstance?: EmrInstance;
  availabilityCoverages?: AvailabilityCoverage[];
  availabilityCoverageRanges?: AvailabilityCoverageRange[];
  paymentAccounts?: PaymentAccount[];
  qualiphy?: QualiphyIntegration;

  birdeyeApiKey?: string; // deprecated - will be removed
  birdeyeApiKeyId?: number;
  encryptedBirdeyeApiKey?: EncryptedKey;
  birdeyeBusinessId?: string;

  static tableName = 'organizations';

  static relationMappings = (): RelationMappings => ({
    profiles: {
      relation: Model.HasManyRelation,
      modelClass: require('../../profile/sqldb').Profile,
      join: {
        from: 'organizations.id',
        to: 'profiles.organizationId',
      },
    },
    marketplaces: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../marketplace/sqldb').Marketplace,
      join: {
        from: 'organizations.id',
        through: {
          from: 'marketplacesOrganizations.organizationId',
          to: 'marketplacesOrganizations.marketplaceId',
        },
        to: 'marketplaces.id',
      },
    },
    roles: {
      relation: Model.HasManyRelation,
      modelClass: require('../../role/sqldb').Role,
      join: {
        from: 'organizations.id',
        to: 'roles.organizationId',
      },
    },
    procedureDefs: {
      relation: Model.HasManyRelation,
      modelClass: require('../../procedure/sqldb').ProcedureDefinition,
      join: {
        from: 'organizations.id',
        to: 'procedureDefinitions.organizationId',
      },
    },
    procedureProfiles: {
      relation: Model.HasManyRelation,
      modelClass: require('../../procedure/sqldb').ProcedureProfile,
      join: {
        from: 'organizations.id',
        to: 'procedureProfiles.organizationId',
      },
    },
    clientProfiles: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../client-profile/sqldb').ClientProfile,
      join: {
        from: 'organizations.id',
        through: {
          from: 'organizationsClientProfiles.organizationId',
          to: 'organizationsClientProfiles.clientProfileId',
        },
        to: 'clientProfiles.id',
      },
    },
    medications: {
      relation: Model.HasManyRelation,
      modelClass: require('../../inventory/sqldb').Medication,
      join: {
        from: 'organizations.id',
        to: 'medications.organizationId',
      },
    },
    emrInstance: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('../../emr/sqldb').EmrInstance,
      join: {
        from: 'organizations.emrInstanceId',
        to: 'emrInstances.id',
      },
    },
    geoperimeters: {
      relation: Model.HasManyRelation,
      modelClass: require('../../geoperimeter/sqldb').Geoperimeter,
      join: {
        from: 'organizations.id',
        to: 'geoperimeters.organizationId',
      },
    },
    availabilityCoverages: {
      relation: Model.HasManyRelation,
      modelClass: require('../../availability/sqldb').AvailabilityCoverage,
      join: {
        from: 'organizations.id',
        to: 'availabilityCoverages.organizationId',
      },
    },
    paymentAccounts: {
      relation: Model.ManyToManyRelation,
      modelClass: require('../../payment/sqldb').PaymentAccount,
      join: {
        from: 'organizations.id',
        through: {
          from: 'organizationsPaymentAccounts.organizationId',
          to: 'organizationsPaymentAccounts.paymentAccountId',
        },
        to: 'paymentAccounts.id',
      },
    },
    qualiphy: {
      relation: Model.HasOneRelation,
      modelClass: require('../../qualiphy/sqldb').QualiphyIntegration,
      join: {
        from: 'organizations.id',
        to: 'qualiphyIntegrations.organizationId',
      },
    },
    encryptedBirdeyeApiKey: {
      relation: Model.BelongsToOneRelation,
      modelClass: require('../../common/sqldb').EncryptedKey,
      join: {
        from: 'organizations.birdeyeApiKeyId',
        to: 'encryptedKeys.id',
      },
    },
  });
}
