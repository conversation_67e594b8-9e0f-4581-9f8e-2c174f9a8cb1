import { ForbiddenError, UserInputError } from 'apollo-server';
import { find } from 'lodash';
import { Page } from 'objection';
import {
  Arg,
  Ctx,
  FieldResolver,
  ID,
  Query,
  Resolver,
  Root,
} from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { AppointmentPage } from '../../appointment/AppointmentPage';
import AppointmentPageInput from '../../appointment/resolvers/AppointmentPageInput';
import { Appointment } from '../../appointment/sqldb';
import { getPaginatedAppointments } from '../../appointment/sqldb/queries';
import { getPaginatedCoverageRanges } from '../../availability/availability-coverage';
import { authorizeAvailabilityCoverage } from '../../availability/common';
import AvailabilityCoverageRangeInput from '../../availability/resolvers/AvailabilityCoverageRangeInput';
import {
  AvailabilityCoverage,
  AvailabilityCoverageRange,
} from '../../availability/sqldb/';
import { ClientProfilePage } from '../../client-profile/ClientProfilePage';
import ClientProfilePageInput from '../../client-profile/resolvers/ClientProfilePageInput';
import { ClientProfile } from '../../client-profile/sqldb';
import { getPaginatedClientProfiles } from '../../client-profile/sqldb/queries';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { EmrInstance } from '../../emr/sqldb';
import { Geoperimeter } from '../../geoperimeter/sqldb';
import { getGeoperimetersByOrganization } from '../../geoperimeter/sqldb/queries';
import { Medication } from '../../inventory/sqldb';
import { Marketplace } from '../../marketplace/sqldb';
import { getMarketplacesByOrganization } from '../../marketplace/sqldb/queries';
import { PaymentAccount } from '../../payment/sqldb';
import { ProcedureDefinition, ProcedureProfile } from '../../procedure/sqldb';
import {
  getProcedureDefsByOrganization,
  getProcedureProfilesByOrganization,
} from '../../procedure/sqldb/queries';
import { Profile } from '../../profile/sqldb';
import {
  getProfilesByOrganization,
  getProfilesByUser,
} from '../../profile/sqldb/queries';
import { refreshQualiphyExams } from '../../qualiphy/exam';
import QualiphyIntegration from '../../qualiphy/sqldb/QualiphyIntegration';
import { Role } from '../../role/sqldb';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { Organization } from '../sqldb';
import { getOrganizations } from '../sqldb/queries';

@Resolver(() => Organization)
export default class OrganizationResolver {
  @FieldResolver(() => [Profile])
  async profiles(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<Profile[]> {
    let query;

    if (
      authorize(user, ['profiles:full', 'profiles:list']) ||
      authorize(
        user,
        ['organization.profiles:full', 'organization.profiles:list'],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        },
      )
    ) {
      query = getProfilesByOrganization(sqldb.knex, {
        orgId: organization.id,
      });
    } else if (
      authorize(user, 'organization.appointments:self', {
        scope: RoleScope.ORGANIZATION,
        resourceId: organization.id,
      })
    ) {
      query = getProfilesByUser(sqldb.knex, {
        userId: user.id,
      }).where({ organizationId: organization.id });
    } else {
      return [];
    }

    const profiles = await query.withGraphFetched(
      '[roles.[permissions, inheritedPermissions], procedureProfiles.procedureDefs.baseDefinitions, availabilities(afterNow).repeatRule]',
    );

    return profiles;
  }

  @FieldResolver(() => [Geoperimeter])
  async geoperimeters(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<Geoperimeter[]> {
    if (
      !authorize(user, 'organizations:full') &&
      !authorize(user, 'organization:update', {
        scope: RoleScope.ORGANIZATION,
        resourceId: organization.id,
      })
    ) {
      return [];
    }
    return (
      organization.geoperimeters ||
      (await getGeoperimetersByOrganization(dataSources.sqldb.knex, {
        id: organization.id,
      }))
    );
  }

  @FieldResolver(() => [AvailabilityCoverage])
  async availabilityCoverages(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<AvailabilityCoverage[]> {
    if (authorizeAvailabilityCoverage(user, organization.id)) {
      return (
        (await sqldb.organizationAvailabilityCoverages(organization.id)) ?? []
      );
    }

    return [];
  }

  @FieldResolver(() => [AvailabilityCoverageRange])
  async availabilityCoverageRanges(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() organization: Organization,
    @Arg('input') input: AvailabilityCoverageRangeInput,
  ): Promise<AvailabilityCoverageRange[]> {
    const { dateRange } = input;
    let profileId = intFromID(input.profileId);

    if (
      !authorize(user, ['availability:full', 'availability:list']) &&
      !authorize(
        user,
        ['organization.availability:full', 'organization.availability:list'],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        },
      )
    ) {
      const userProfileId = find(user.profiles ?? [], {
        organizationId: organization.id,
      })?.id;

      if (profileId && profileId !== userProfileId) {
        throw new UserInputError(
          'Invalid profileId (organization/availabilityCoverageRanges)',
        );
      }

      if (
        !userProfileId ||
        !authorize(user, 'organization.availability:self', {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        })
      ) {
        return [];
      }

      profileId = userProfileId;
    }

    const [from, to] = dateRange;
    const maxTo = +from + (31 * 24 + 1) * 60 * 60 * 1000;
    const cappedTo = new Date(Math.min(+to, maxTo));

    return await getPaginatedCoverageRanges({
      sqldb,
      organizationId: organization.id,
      profileId,
      dateRange: [from, cappedTo],
    });
  }

  @FieldResolver(() => [Role])
  async roles(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<Role[]> {
    if (
      !authorize(user, ['roles:full', 'roles:list']) &&
      !authorize(user, ['organization.roles:full', 'organization.roles:list'], {
        scope: RoleScope.ORGANIZATION,
        resourceId: organization.id,
      })
    ) {
      return [];
    }

    return (
      organization.roles ||
      dataSources.sqldb.roles(RoleScope.ORGANIZATION, organization.id)
    );
  }

  @FieldResolver(() => [ProcedureDefinition])
  async procedureDefs(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<ProcedureDefinition[]> {
    return (
      organization.procedureDefs ||
      getProcedureDefsByOrganization(dataSources.sqldb.knex, {
        orgId: organization.id,
      })
    );
  }

  @FieldResolver(() => [ProcedureProfile])
  async procedureProfiles(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<ProcedureProfile[]> {
    return (
      organization.procedureProfiles ||
      getProcedureProfilesByOrganization(dataSources.sqldb.knex, {
        orgId: organization.id,
      }).withGraphFetched('procedureDefs.baseDefinitions')
    );
  }

  @FieldResolver(() => [Marketplace])
  async marketplaces(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<Marketplace[]> {
    if (
      !authorize(user, ['marketplaces:full', 'marketplaces:list']) &&
      !authorize(user, 'organization.marketplaces:list', {
        scope: RoleScope.ORGANIZATION,
        resourceId: organization.id,
      })
    ) {
      // throw new ForbiddenError('Not authorized (organization/marketplaces)');
      return [];
    }

    return (
      organization.marketplaces ||
      getMarketplacesByOrganization(dataSources.sqldb.knex, {
        orgId: organization.id,
      })
    );
  }

  @FieldResolver(() => AppointmentPage)
  async appointments(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() organization: Organization,
    @Arg('page', { defaultValue: {} }) page: AppointmentPageInput,
  ): Promise<AppointmentPage> {
    const paginatedQuery = getPaginatedAppointments(
      sqldb.knex,
      page,
    ).withGraphFetched(
      '[participants, procedureBaseDefs(withArchived), checkout]',
    );

    let result: Page<Appointment> = { total: 0, results: [] };

    if (
      authorize(user, ['appointments:full', 'appointments:list']) ||
      authorize(
        user,
        ['organization.appointments:full', 'organization.appointments:list'],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        },
      )
    ) {
      result = await paginatedQuery.whereExists(
        Appointment.relatedQuery('participants')
          .joinRelated('profile(withArchived)')
          .where('profile.organizationId', organization.id),
      );
    } else if (
      authorize(user, 'organization.appointments:self', {
        scope: RoleScope.ORGANIZATION,
        resourceId: organization.id,
      })
    ) {
      result = await paginatedQuery.whereExists(
        Appointment.relatedQuery('participants')
          .joinRelated('profile(withArchived)')
          .where('profile.organizationId', organization.id)
          .where('profile.userId', user.id),
      );
    }

    return {
      totalCount: result.total,
      data: result.results,
    };
  }

  @FieldResolver(() => ClientProfilePage)
  async clientProfiles(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() organization: Organization,
    @Arg('page', { defaultValue: {} }) page: ClientProfilePageInput,
  ): Promise<ClientProfilePage> {
    let result: Page<ClientProfile> = { total: 0, results: [] };

    if (
      authorize(user, ['appointments:full', 'appointments:list']) ||
      authorize(
        user,
        [
          'organization.appointments:full',
          'organization.appointments:list',
          'organization.appointments:self',
        ],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        },
      )
    ) {
      result = await getPaginatedClientProfiles(sqldb.knex, page).whereExists(
        ClientProfile.relatedQuery('organizations').where(
          'organizations.id',
          organization.id,
        ),
      );
    }

    return {
      totalCount: result.total,
      data: result.results,
    };
  }

  @FieldResolver(() => [Medication])
  async medications(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<Medication[]> {
    if (
      authorize(user, ['medications:full', 'medications:list']) ||
      authorize(
        user,
        ['organization.medications:full', 'organization.medications:list'],
        {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        },
      )
    ) {
      return (
        organization.medications ||
        organization.$relatedQuery('medications', dataSources.sqldb.knex)
      );
    }

    return [];
  }

  @FieldResolver(() => EmrInstance, { nullable: true })
  async emrInstance(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<EmrInstance | null> {
    if (
      organization.emrInstanceId &&
      (authorize(user, 'emr:full') ||
        authorize(user, 'organization.emr:full', {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        }))
    ) {
      return (
        organization.emrInstance ||
        organization.$relatedQuery('emrInstance', dataSources.sqldb.knex)
      );
    }

    return null;
  }

  @FieldResolver(() => String, { nullable: true })
  slackWebhookUrl(
    @CurrentUser() user: User,
    @Root() organization: Organization,
  ): string | null {
    if (
      organization.slackWebhookUrl &&
      (authorize(user, ['organizations:full']) ||
        authorize(user, ['organization:update'], {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        }))
    ) {
      return organization.slackWebhookUrl;
    }
    return null;
  }

  // This field is needed to retrieve qualiphy status without running the full qualiphy field below
  @FieldResolver(() => Boolean)
  async qualiphyEnabled(
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<boolean> {
    const qualiphy =
      organization.qualiphy ||
      (await sqldb.qualiphyIntegrationForOrg(organization.id));

    return Boolean(qualiphy?.enabled) && Boolean(qualiphy?.encryptedApiKey);
  }

  @FieldResolver(() => QualiphyIntegration, { nullable: true })
  async qualiphy(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<QualiphyIntegration | null | undefined> {
    if (
      !authorize(user, ['organizations:full']) &&
      !authorize(user, 'organization:update', {
        scope: RoleScope.ORGANIZATION,
        resourceId: organization.id,
      })
    ) {
      return null;
    }

    const qualiphy =
      organization.qualiphy ||
      (await sqldb.qualiphyIntegrationForOrg(organization.id));

    if (qualiphy) {
      qualiphy.exams = await refreshQualiphyExams({
        sqldb,
        organizationId: organization.id,
      });
    }

    return qualiphy;
  }

  @FieldResolver(() => [PaymentAccount])
  async paymentAccounts(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Root() organization: Organization,
  ): Promise<PaymentAccount[]> {
    if (
      authorize(user, ['payments:full']) ||
      authorize(user, ['organization.payments:full'], {
        scope: RoleScope.ORGANIZATION,
        resourceId: organization.id,
      })
    ) {
      return (
        organization.paymentAccounts ||
        organization
          .$relatedQuery('paymentAccounts', sqldb.knex)
          .withGraphJoined('finix')
      );
    }

    return [];
  }

  @Query(() => [Organization])
  async organizations(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
  ): Promise<Organization[]> {
    const organizations = await getOrganizations(
      dataSources.sqldb.knex,
    ).withGraphJoined('marketplaces');

    if (authorize(user, ['organizations:full', 'organizations:list'])) {
      return organizations;
    }

    return organizations.filter(
      (organization) =>
        authorize(user, [], {
          scope: RoleScope.ORGANIZATION,
          resourceId: organization.id,
        }) ||
        (organization.marketplaces ?? []).some((marketplace) =>
          authorize(
            user,
            [
              'marketplace.organizations:list',
              'marketplace.organizations:full',
            ],
            {
              scope: RoleScope.MARKETPLACE,
              resourceId: marketplace.id,
            },
          ),
        ),
    );
  }

  @Query(() => Organization, { nullable: true })
  async organization(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Arg('id', () => ID) id: string,
  ): Promise<Organization | null | undefined> {
    const organizationId = intFromID(id) as number;
    const organization = await dataSources.sqldb.organization(organizationId);

    if (
      !authorize(user, ['organizations:full', 'organizations:list']) &&
      !find(user.profiles, { organizationId })
    ) {
      throw new ForbiddenError('Not authorized (organization)');
    }

    return organization;
  }
}
