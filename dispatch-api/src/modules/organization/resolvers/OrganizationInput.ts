import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>N<PERSON><PERSON>,
  IsUrl,
  Length,
  MaxLength,
} from 'class-validator';
import { Field, InputType } from 'type-graphql';
import { StateCode } from '../../common/sqldb/types';
import { IsTimeZone } from '../../common/type-graphql';
import { Organization } from '../sqldb';

@InputType()
export default class OrganizationInput implements Partial<Organization> {
  @Field({ nullable: true })
  @Length(3, 100)
  name?: string;

  @Field({ nullable: true })
  @IsTimeZone()
  tzid?: string;

  @Field({ nullable: true })
  @MaxLength(255)
  @IsUrl({
    protocols: ['https'],
    require_valid_protocol: true,
    require_protocol: true,
    require_host: true,
  })
  slackWebhookUrl?: string;

  @Field({ nullable: true })
  @Length(32)
  qualiphyApiKey?: string;

  @Field({ nullable: true })
  @IsUrl({
    protocols: ['https'],
    require_valid_protocol: true,
    require_protocol: true,
    require_host: true,
  })
  googleReviewsUrl?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  @IsPhoneNumber('US')
  phone?: string;

  @Field({ nullable: true })
  @IsEmail()
  email?: string;

  @Field({ nullable: true })
  address?: string;

  @Field({ nullable: true })
  providesAtClinic?: boolean;

  @Field({ nullable: true })
  enablePractitionerSms?: boolean;

  @Field({ nullable: true })
  enableReceiptSending?: boolean;

  @Field(() => StateCode, { nullable: true })
  state?: StateCode;
}
