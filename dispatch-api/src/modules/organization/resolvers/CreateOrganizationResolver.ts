import { ForbiddenError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import createOrganization from '../create-organization';
import { Organization } from '../sqldb';
import CreateOrganizationInput from './CreateOrganizationInput';

@Resolver()
export default class CreateOrganizationResolver {
  @Mutation(() => Organization, { nullable: true })
  async createOrganization(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: CreateOrganizationInput,
  ): Promise<Organization | null | undefined> {
    if (!authorize(user, 'organizations:create')) {
      throw new ForbiddenError('Not authorized (createOrganization)');
    }

    const {
      name,
      state,
      tzid,
      ownerEmails,
      slackWebhookUrl,
      phone,
      googleReviewsUrl,
      email,
      address,
      providesAtClinic,
      enablePractitionerSms,
    } = input;

    const emrInstanceId = intFromID(input.emrInstanceId);

    if (emrInstanceId && !authorize(user, 'emr:full')) {
      throw new ForbiddenError(
        'Not authorized to assign an EMR instance (createOrganization)',
      );
    }

    return createOrganization({
      sqldb,
      organization: {
        name,
        tzid,
        state,
        slackWebhookUrl,
        phone,
        email,
        googleReviewsUrl,
        address,
        providesAtClinic,
        enablePractitionerSms,
      },
      ownerEmails,
      emrInstanceId,
    });
  }
}
