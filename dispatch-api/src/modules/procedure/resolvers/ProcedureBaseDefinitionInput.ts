import { Length, MaxLength, Min } from 'class-validator';
import { Field, InputType } from 'type-graphql';

@InputType()
export default class ProcedureBaseDefinitionInput {
  @Field()
  @Length(3, 100)
  name!: string;

  @Field()
  @MaxLength(1024)
  description!: string;

  @Field()
  @Min(0)
  duration!: number;

  @Field({ nullable: true })
  @MaxLength(200)
  tagline?: string;

  @Field({ nullable: true })
  @MaxLength(1024)
  ingredients?: string;

  @Field({ nullable: true })
  @MaxLength(100)
  category?: string;

  @Field({ nullable: true })
  @Length(1, 50)
  thumbnailToken?: string;

  @Field({ nullable: true })
  points?: number;
}
