import dayjs from 'dayjs';
import { find, flatten, max, property, sortBy, sumBy } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { acceptAppointment } from '../appointment/accept-appointment';
import { createAppointment } from '../appointment/create-appointment';
import { Appointment, AppointmentConstraint } from '../appointment/sqldb';
import { getAppointmentRequest } from '../appointment/sqldb/queries';
import {
  AppointmentRequestStatus,
  AppointmentStatus,
  AppointmentTimeRangeFields,
  ParticipantType,
  ParticipationStatus,
} from '../appointment/sqldb/types';
import SlackLogger from '../logger/SlackLogger';
import { ProcedureBaseDefinition } from '../procedure/sqldb';
import { AppointmentCandidate } from './AppointmentCandidate';
import StartTimeSet from './StartTimeSet';
import { getCandidates } from './strategies/default';
import { AppointmentCandidateFields } from './types';

const getDuration = (baseDefs: ProcedureBaseDefinition[]) =>
  sumBy(baseDefs ?? [], (def) => def.duration) || 15;

interface AppointmentCandidatesParams {
  sqldb: SqlDbSource;
  constraint: AppointmentConstraint;
}

export async function appointmentCandidates(
  params: AppointmentCandidatesParams,
): Promise<AppointmentCandidate[]> {
  const { sqldb, constraint } = params;

  const appointmentRequest = await sqldb.appointmentRequest(
    constraint.requestId,
  );

  const marketplace = await sqldb.marketplace(
    appointmentRequest?.marketplaceId,
  );

  if (appointmentRequest) {
    const { procedureBaseDefs = [] } = appointmentRequest;
    const duration = getDuration(procedureBaseDefs ?? []);

    return await getCandidates({
      sqldb,
      duration,
      procedureBaseDefs,
      constraint,
      minStartTime: dayjs().add(10, 'minutes').toDate(),
      rankBy: !marketplace?.requirePractitionerApproval
        ? 'fairness'
        : 'available',
    });
  }

  return [];
}

interface DispatchAppointentRequestParams {
  sqldb: SqlDbSource;
  appointmentRequestId: number;
}

export async function dispatchAppointentRequest(
  params: DispatchAppointentRequestParams,
): Promise<void> {
  const { sqldb, appointmentRequestId } = params;

  const appointmentRequest = await getAppointmentRequest(sqldb.knex, {
    id: appointmentRequestId,
  });

  if (!appointmentRequest) {
    throw new Error(`Invalid appointment request: ${appointmentRequestId}`);
  }

  const marketplace = await sqldb.marketplace(appointmentRequest.marketplaceId);

  if (!marketplace) {
    throw new Error(`Invalid marketplace: ${appointmentRequest.marketplaceId}`);
  }

  if (
    marketplace.requireDispatchApproval &&
    appointmentRequest.status === AppointmentRequestStatus.PENDING_APPROVAL
  ) {
    throw new Error(
      `Appointment request requires dispatch approval: ${appointmentRequestId}`,
    );
  }

  if (appointmentRequest.status !== AppointmentRequestStatus.PENDING) {
    throw new Error(
      `Appointment request is not pending: ${appointmentRequestId} (${appointmentRequest.status})`,
    );
  }

  const { procedureBaseDefs = [], clientProfiles = [] } = appointmentRequest;

  const constraints = await sqldb.knex.transaction(async (transaction) =>
    AppointmentConstraint.fetchGraph(
      appointmentRequest.constraints ?? [],
      'appointments(withArchived).participants',
      { transaction },
    ),
  );

  const duration = getDuration(procedureBaseDefs ?? []);
  let constraintCandidates: AppointmentCandidate[] = [];

  const allAppointments = flatten(
    constraints?.map((c) => c.appointments ?? []) ?? [],
  );

  const isDuplicateTimeRange = (
    profileId: number,
    timeRanges: AppointmentTimeRangeFields[],
  ): boolean => {
    // this function determines if we already covered all specified timeRanges
    // for this profile as part of other constraint/appointments

    const appointments = allAppointments.filter(
      (a) => a.participants?.some((p) => p.profileId === profileId),
    );

    const times = new StartTimeSet();

    if (appointments.length) {
      timeRanges.forEach(({ start, end }) => times.addRange(start, end));

      appointments.forEach(
        (appt) =>
          find(constraints, {
            id: appt.constraintId,
          })?.timeRanges?.forEach(({ start, end }) =>
            times.deleteRange(start, end),
          ),
      );
    }

    return Boolean(appointments.length && !times.toArray().length);
  };

  for (const constraint of constraints ?? []) {
    const candidates = await appointmentCandidates({ sqldb, constraint });

    if (candidates.length) {
      const appointments = constraint.appointments ?? [];
      const bookedIds = getProfileIds(appointments);
      const pendingIds = getProfileIds(
        appointments.filter(
          ({ status, deletedAt }) =>
            !deletedAt && status === AppointmentStatus.PENDING,
        ),
      );

      // an existing `pending` appointment will determine the max ranked candidate to consider

      const maxRank = max(
        candidates
          .filter((candidate) => pendingIds.has(candidate.profileId))
          .map((c) => c.rank),
      );

      const nextCandidates = candidates.filter(
        (candidate) =>
          !bookedIds.has(candidate.profileId) &&
          (!maxRank || candidate.rank <= maxRank) &&
          !isDuplicateTimeRange(
            candidate.profileId,
            constraint.timeRanges ?? [],
          ),
      );

      if (nextCandidates.length || pendingIds.size) {
        constraintCandidates = nextCandidates;
        break;
      }
    }
  }

  if (constraintCandidates.length) {
    const isStartTimeAvailable = (candidate: AppointmentCandidateFields) =>
      candidate.start &&
      candidate.availableTimes?.map((dt) => +dt).includes(+candidate.start);

    // sort by rank (asc), then by availability (yes > no)
    const orderedCandidates = sortBy(constraintCandidates, [
      'rank',
      (candidate) => (isStartTimeAvailable(candidate) ? 0 : 1),
    ]);

    let rank = 0;
    let pendingAppointments = [];

    for (const candidate of orderedCandidates) {
      if (rank && candidate.rank > rank) {
        break;
      }

      if (!candidate.start) {
        break;
      }

      try {
        const appointment = await createAppointment({
          sqldb,
          appointment: {
            start: candidate.start,
            end: dayjs(candidate.start).add(duration, 'minutes').toDate(),
            location: appointmentRequest.location,
          },
          procedureBaseDefIds: procedureBaseDefs.map((def) => def.id),
          participants: [
            ...clientProfiles.map(({ id }) => ({
              type: ParticipantType.PATIENT,
              status: ParticipationStatus.ACCEPTED,
              id,
            })),
            {
              type: ParticipantType.PRACTITIONER,
              status: ParticipationStatus.NEEDS_ACTION,
              id: candidate.profileId,
            },
          ],
          constraintId: candidate.constraintId,
          alternateTimes: candidate.reservableTimes?.map((start) => ({
            start,
          })),
          createdBy: 1,
        });

        if (appointment) {
          rank = candidate.rank;

          if (
            !marketplace.requirePractitionerApproval &&
            isStartTimeAvailable(candidate)
          ) {
            if (
              await acceptAppointment({
                sqldb,
                appointmentId: appointment.id,
                profileId: candidate.profileId,
              })
            ) {
              pendingAppointments = [];
              break;
            } else {
              pendingAppointments.push(appointment);
            }
          }
        }
      } catch (err) {
        console.log(err);
      }
    }

    if (pendingAppointments.length) {
      new SlackLogger({ sqldb, marketplaceId: marketplace.id }).send({
        content: `Dispatched ${pendingAppointments.length} pending appointment(s) for appointment request (id:${appointmentRequest.id})`,
      });
    }
  } else {
    // unable to fulfill appointment request
    new SlackLogger({ sqldb, marketplaceId: marketplace.id }).send({
      content: [
        `‼️ Unable to dispatch appointment request (id:${appointmentRequest.id}) No candidates match the provided constraints.`,
        appointmentRequest.toString(),
      ].join('\n'),
    });
  }
}

const getProfileIds = (appointments: Appointment[]) =>
  new Set(
    flatten(
      appointments.map(
        (appointment) => appointment.participants?.map(property('profileId')),
      ),
    ).filter(Boolean) as number[],
  );
