import { difference, find, flattenDeep, last, property, sortBy } from 'lodash';
import { SqlDbSource } from '../../../datasources';
import { AppointmentConstraint } from '../../appointment/sqldb';
import { AppointmentStatus } from '../../appointment/sqldb/types';
import { ProcedureBaseDefinition } from '../../procedure/sqldb';
import { Profile } from '../../profile/sqldb';
import { AppointmentCandidate } from '../AppointmentCandidate';
import { createAvailabilitySetForProfile, reservableTimes } from '../util';

interface AppointmentCandidatesParams {
  sqldb: SqlDbSource;
  procedureBaseDefs: ProcedureBaseDefinition[];
  duration: number;
  constraint: AppointmentConstraint;
  minStartTime?: Date;
  rankBy?: 'available' | 'fairness';
}

export async function getCandidates(
  params: AppointmentCandidatesParams,
): Promise<AppointmentCandidate[]> {
  const {
    sqldb,
    procedureBaseDefs,
    constraint,
    duration,
    minStartTime = new Date(),
    rankBy = 'fairness',
  } = params;

  if (
    !procedureBaseDefs.length ||
    !constraint.timeRanges?.length ||
    duration <= 0
  ) {
    return [];
  }

  const marketplaceId = procedureBaseDefs[0].marketplaceId;
  const baseDefIds = procedureBaseDefs.map(({ id }) => id);

  const profiles = await sqldb.knex.transaction(async (transaction) => {
    const query = Profile.query(transaction)
      .withGraphJoined('procedureProfiles.procedureDefs.baseDefinitions')
      .where(
        'procedureProfiles:procedureDefs:baseDefinitions.marketplaceId',
        marketplaceId,
      )
      .whereIn(
        'procedureProfiles:procedureDefs:baseDefinitions.id',
        baseDefIds,
      );

    if (constraint.organizations?.length) {
      query.whereIn(
        'profiles.organizationId',
        constraint.organizations.map(property('id')),
      );
    }

    if (constraint.profiles?.length) {
      query.whereIn('profiles.id', constraint.profiles.map(property('id')));
    }

    const profilesResult = await query;

    // only consider profiles that support every requested procedure

    const profiles = profilesResult.filter(
      (profile) =>
        difference(
          baseDefIds,
          flattenDeep<number | undefined>(
            profile.procedureProfiles?.map(
              (p) =>
                p.procedureDefs?.map(
                  (def) => def.baseDefinitions?.map((baseDef) => baseDef.id),
                ),
            ),
          ),
        ).length === 0,
    );

    if (profiles?.length) {
      return Profile.fetchGraph(
        profiles,
        'appointments(filterAppointments, withArchived)',
        {
          transaction,
        },
      ).modifiers({
        filterAppointments(builder) {
          builder
            .where('end', '>=', minStartTime)
            .orWhere('constraintId', constraint.id);
        },
      });
    }

    return [];
  });

  const profilesAvailability = profiles.length
    ? await Profile.query(sqldb.knex)
        .withGraphJoined('availabilities.repeatRule')
        .whereIn('profiles.id', profiles.map(property('id')))
        .where((builder) =>
          builder
            .whereNotNull('availabilities:repeatRule.availabilityId')
            .orWhere('end', '>=', minStartTime),
        )
    : [];

  interface ProfileStatsRecord {
    profile: Profile;
    reservableTimes: Date[];
    availableTimes: Date[];
    numPendingAppointments: number;
    lastAppointmentCreatedAt?: Date | null;
  }

  const profileStats: Record<number, ProfileStatsRecord> = {};

  profiles.forEach((profile) => {
    profile.$setRelated(
      'availabilities',
      find(profilesAvailability, { id: profile.id })?.availabilities,
    );

    let times = reservableTimes(constraint.timeRanges ?? [], {
      appointments: profile.appointments ?? [],
      availabilities: profile.availabilities ?? [],
      duration,
    }).toArray();

    if (minStartTime) {
      times = times.filter((dt) => dt >= minStartTime);
    }

    if (
      times.length ||
      find(profile.appointments ?? [], { constraintId: constraint.id })
    ) {
      const result: ProfileStatsRecord = {
        profile,
        reservableTimes: times,
        availableTimes: [],
        numPendingAppointments: (profile.appointments ?? []).filter(
          (a) => a.status === AppointmentStatus.PENDING && !a.deletedAt,
        ).length,
        lastAppointmentCreatedAt: null,
      };

      const availTimes = times.length
        ? createAvailabilitySetForProfile(profile, {
            from: times[0],
            until: new Date(+(last(times) as Date) + duration * 1000),
            duration,
          })
        : null;

      result.availableTimes = times.filter((dt) => availTimes?.has(dt));
      profileStats[profile.id] = result;
    }
  });

  const ids = Object.entries(profileStats)
    .filter(([, stats]) => stats.reservableTimes.length)
    .map(([id]) => id);

  const profilesLastAppointment = await Profile.query(sqldb.knex)
    .whereIn('id', ids)
    .select(
      'profiles.*',
      Profile.relatedQuery('appointments')
        .modify('withArchived')
        .whereNotNull('constraintId')
        .max('createdAt')
        .as('lastCreatedAt'),
    );

  profilesLastAppointment.forEach(({ id, lastCreatedAt }) => {
    profileStats[id].lastAppointmentCreatedAt = lastCreatedAt;
  });

  // console.log(profileStats);

  // STRATEGY:
  //   sort profiles:
  //     - has matching availability (available - blocked - appointments) (yes > no)
  //     - has any pending appointment (no > yes)
  //     - last appointment created time (including declined) (ascending)
  //     - id

  const ranked = sortBy(Object.values(profileStats), [
    (stats) => (stats.availableTimes.length ? 0 : 1),
    (stats) => stats.numPendingAppointments,
    (stats) => +(stats.lastAppointmentCreatedAt || 0),
    (stats) => stats.profile.id,
  ]);

  const defaultRank = ranked.filter((s) => s.availableTimes.length).length + 1;

  return ranked.map((stats, index) => {
    const candidate = new AppointmentCandidate();

    candidate.constraintId = constraint.id;
    candidate.profile = stats.profile;
    candidate.profileId = stats.profile.id;
    candidate.organizationId = stats.profile.organizationId;
    candidate.start = stats.availableTimes[0] ?? stats.reservableTimes[0];
    candidate.reservableTimes = stats.reservableTimes;
    candidate.availableTimes = stats.availableTimes;

    if (rankBy === 'available') {
      candidate.rank = stats.availableTimes.length ? 1 : 2;
    } else {
      // 'fairness' ranking
      candidate.rank = stats.availableTimes.length ? index + 1 : defaultRank;
    }

    return candidate;
  });
}
