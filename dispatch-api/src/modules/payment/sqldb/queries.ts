import { K<PERSON> } from 'knex';
import { find, pick } from 'lodash';
import { QueryBuilder, fn, raw, ref } from 'objection';
import { ParticipantType } from '../../appointment/sqldb/types';
import { createFilter, createStringFilter } from '../../common/filter';
import { FilterStringFields, SortDirection } from '../../common/sqldb/types';
import { Organization } from '../../organization/sqldb';
import Checkout from './Checkout';
import Payment from './Payment';
import PaymentAccount from './PaymentAccount';
import PaymentInstrument from './PaymentInstrument';
import Payout from './Payout';
import {
  CheckoutFilterFields,
  CheckoutSortField,
  CheckoutSortFields,
  PaymentStatus,
  PayoutFilterFields,
  PayoutSortField,
  PayoutSortFields,
} from './types';

interface GetPaymentAccountParams {
  id: number;
}

export function getPaymentAccount(
  knex: Knex,
  { id }: GetPaymentAccountParams,
): QueryBuilder<PaymentAccount, PaymentAccount | undefined> {
  return PaymentAccount.query(knex)
    .findById(id)
    .withGraphFetched('[organizations, marketplaces, finix]');
}

export function getPaymentAccounts(knex: Knex): QueryBuilder<PaymentAccount> {
  return PaymentAccount.query(knex).withGraphJoined('[organizations, finix]');
}

interface GetPaymentInstrumentParams {
  id: number;
}

export function getPaymentInstrument(
  knex: Knex,
  { id }: GetPaymentInstrumentParams,
) {
  return PaymentInstrument.query(knex).findById(id);
}

interface GetCheckoutParams {
  id: number;
}

export function getCheckout(knex: Knex, { id }: GetCheckoutParams) {
  return Checkout.query(knex)
    .findById(id)
    .withGraphFetched(
      '[items, payments, payouts, paymentInstrument, appointment, appointmentRequest]',
    );
}

interface PaginatedCheckoutParams {
  offset: number;
  limit: number;
  sort?: CheckoutSortFields[];
  filter?: CheckoutFilterFields;
}

export function getPaginatedCheckouts(
  knex: Knex,
  page: PaginatedCheckoutParams,
) {
  const { offset, limit, filter } = page;

  let query = Checkout.query(knex)
    .select('checkouts.*')
    .joinRelated('marketplaceUser.clientProfile');

  const applyLimit = Math.min(limit, 100);

  // filter

  const checkoutFilters = pick(filter, [
    'id',
    'total',
    'paid',
    'balance',
    'marketplaceId',
  ]);

  if (Object.entries(checkoutFilters).length) {
    query = query.where(createFilter(checkoutFilters));
  }

  if (filter?.summary) {
    query = query.whereExists(
      Checkout.relatedQuery('items').where(
        createFilter({
          description: filter.summary,
        }),
      ),
    );
  }

  if (filter?.fullName) {
    query = query.whereExists(
      Checkout.relatedQuery('marketplaceUser')
        .joinRelated('clientProfile')
        .modify('withArchived')
        .where((builder) =>
          createStringFilter(
            builder,
            fn.concat(
              ref('clientProfile.givenName'),
              raw("' '"),
              ref('clientProfile.familyName'),
            ),
            filter.fullName as FilterStringFields,
          ),
        ),
    );
  }

  // sort

  const sort = [...(page.sort ?? [])];

  if (!find(sort, { field: CheckoutSortField.ID })) {
    sort.push({
      field: CheckoutSortField.ID,
      direction: SortDirection.DESC,
    });
  }

  if (find(sort, { field: CheckoutSortField.SUMMARY })) {
    query.select(
      Checkout.relatedQuery('items').min('description').as('summary'),
    );
  }

  if (find(sort, { field: CheckoutSortField.FULLNAME })) {
    query.select(
      Checkout.relatedQuery('marketplaceUser')
        .joinRelated('clientProfile')
        .modify('withArchived')
        .select(
          fn.concat(
            ref('clientProfile.givenName'),
            raw("' '"),
            ref('clientProfile.familyName'),
          ),
        )
        .as('fullName'),
    );
  }

  if (find(sort, { field: CheckoutSortField.MARKETPLACEID })) {
    query.select(
      Checkout.relatedQuery('marketplace')
        .modify('withArchived')
        .select('name')
        .as('marketplaceId'),
    );
  }

  query.orderBy(
    sort.map(({ field: column, direction: order }) => ({ column, order })),
  );

  return query.range(offset, offset + applyLimit - 1);
}

interface GetPaymentParams {
  id: number;
}

export function getPayment(knex: Knex, { id }: GetPaymentParams) {
  return Payment.query(knex).findById(id);
}

interface GetPayoutParams {
  id: number;
}

export function getPayout(knex: Knex, { id }: GetPayoutParams) {
  return Payout.query(knex).findById(id);
}

interface PaginatedPayoutParams {
  offset: number;
  limit: number;
  sort?: PayoutSortFields[];
  filter?: PayoutFilterFields;
}

export function getPaginatedPayouts(knex: Knex, page: PaginatedPayoutParams) {
  const { offset, limit, filter } = page;

  let query = Payout.query(knex).select('payouts.*').joinRelated('checkout');

  const applyLimit = Math.min(limit, 100);

  // filter

  const payoutFilters = pick(filter, [
    'id',
    'status',
    'description',
    'amount',
    'amountRequested',
    'amountReversed',
  ]);

  if (Object.entries(payoutFilters).length) {
    query = query.where(createFilter(payoutFilters));
  }

  if (filter?.marketplaceId) {
    query = query.where(
      createFilter({ 'checkout.marketplaceId': filter.marketplaceId }),
    );
  }

  // sort

  const sort = [...(page.sort ?? [])];

  if (!find(sort, { field: PayoutSortField.ID })) {
    sort.push({
      field: PayoutSortField.ID,
      direction: SortDirection.DESC,
    });
  }

  if (find(sort, { field: PayoutSortField.MARKETPLACEID })) {
    query.select(
      Payout.relatedQuery('checkout')
        .joinRelated('marketplace')
        .modify('withArchived')
        .select(ref('marketplace.name'))
        .as('marketplaceId'),
    );
  }

  query.orderBy(
    sort.map(({ field: column, direction: order }) => ({ column, order })),
  );

  return query.range(offset, offset + applyLimit - 1);
}

interface GetPaymentByTransferIdParams {
  transferId: string;
}

export function getPaymentByTransferId(
  knex: Knex,
  { transferId }: GetPaymentByTransferIdParams,
) {
  return Payment.query(knex)
    .modify('withArchived')
    .findOne('transactionId', transferId);
}

interface GetOrganizationFromCheckoutParams {
  checkoutId: number;
}

export async function getOrganizationFromCheckout(
  knex: Knex,
  { checkoutId }: GetOrganizationFromCheckoutParams,
): Promise<Organization | null> {
  // Get organization from appointment participants
  const appointment = await Checkout.relatedQuery('appointment', knex)
    .for(checkoutId)
    .withGraphJoined('participants.profile.organization')
    .where('participants.type', ParticipantType.PRACTITIONER)
    .first();

  return appointment?.participants?.[0]?.profile?.organization || null;
}

interface GetAcceptedPaymentsParams {
  checkoutId: number;
}

export function getAcceptedPayments(
  knex: Knex,
  { checkoutId }: GetAcceptedPaymentsParams,
) {
  return Payment.query(knex)
    .where('checkoutId', checkoutId)
    .where('status', PaymentStatus.ACCEPTED)
    .whereNotNull('transactionId')
    .orderBy('createdAt', 'asc');
}

interface GetCustomerContactFromCheckoutParams {
  checkoutId: number;
}

export function getCustomerContactFromCheckout(
  knex: Knex,
  { checkoutId }: GetCustomerContactFromCheckoutParams,
) {
  return Checkout.query(knex)
    .findById(checkoutId)
    .withGraphJoined(
      '[marketplaceUser, appointment.participants.clientProfile]',
    )
    .first()
    .then((checkout) => {
      if (!checkout) return null;

      // Start with marketplace user contact info
      let contact = {
        email: checkout.marketplaceUser?.email,
        phone: checkout.marketplaceUser?.phone,
        emailConfirmed: checkout.marketplaceUser?.emailConfirmed,
        phoneConfirmed: checkout.marketplaceUser?.phoneConfirmed,
        emailOptIn: checkout.marketplaceUser?.emailOptIn,
        phoneOptIn: checkout.marketplaceUser?.phoneOptIn,
      };

      // If we don't have both email and phone, try to get from appointment client profile
      if (!contact.email && !contact.phone) {
        const patientParticipant = checkout.appointment?.participants?.find(
          (p) => p.type === 'patient',
        );

        if (patientParticipant?.clientProfile) {
          contact = {
            email: contact.email || patientParticipant.clientProfile.email,
            phone: contact.phone || patientParticipant.clientProfile.phone,
            emailConfirmed: contact.emailConfirmed ?? true,
            phoneConfirmed: contact.phoneConfirmed ?? true,
            emailOptIn: contact.emailOptIn ?? true,
            phoneOptIn: contact.phoneOptIn ?? true,
          };
        }
      }

      return contact;
    });
}
