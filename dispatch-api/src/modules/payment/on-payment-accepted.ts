import { SqlDbSource } from '../../datasources';
import { sendReceipt } from './finix/receipt';
import { Payment } from './sqldb';

interface OnPaymentAcceptedParams {
  sqldb: SqlDbSource;
  payment: Payment;
}

export async function onPaymentAccepted({
  sqldb,
  payment,
}: OnPaymentAcceptedParams) {
  if (payment.transactionId) {
    try {
      // send asyncronously
      sendReceipt({
        sqldb,
        transferId: payment.transactionId,
        automatic: true,
      });
    } catch (error) {
      console.error(
        `Failed to send receipt for transfer ${payment.transactionId}:`,
        error,
      );
    }
  }
}
