import { sendReceipt, sendReceiptByPayment } from './receipt';
import { SqlDbSource } from '../../../datasources';
import * as queries from '../sqldb/queries';
import { Checkout, Payment } from '../sqldb';
import { PaymentStatus } from '../sqldb/types';
import { Organization } from '../../organization/sqldb';
import MarketplaceUser from '../../marketplace/sqldb/MarketplaceUser';

// Mock the dependencies
jest.mock('.', () => ({
  getFinix: jest.fn(() => ({
    fetch: jest.fn(),
  })),
}));

jest.mock('../sqldb/queries', () => ({
  getPaymentByTransferId: jest.fn(),
  getOrganizationFromCheckout: jest.fn(),
  getCustomerContactFromCheckout: jest.fn(),
  getLatestSucceededPayment: jest.fn(),
  getCheckout: jest.fn(),
  getPayment: jest.fn(),
  getSucceededPayments: jest.fn(),
}));

jest.mock('../../twilio/text', () => jest.fn());
jest.mock('../../email/sendgrid', () => ({
  marketplaceSend: jest.fn(),
}));

describe('Receipt Service', () => {
  let mockSqlDb: jest.Mocked<SqlDbSource>;
  let mockFinixFetch: jest.MockedFunction<any>;

  const setupMocks = (
    overrides: {
      payment?: Partial<Payment> | null;
      checkout?: Checkout | null;
      checkoutWithItems?: any | null;
      organization?: Partial<Organization>;
      customerContact?: Partial<MarketplaceUser> | null;
      finixResponse?: any;
      finixError?: Error;
      allPayments?: Partial<Payment>[];
    } = {},
  ) => {
    const {
      payment = {
        id: 123,
        checkoutId: 123,
        transactionId: 'TR123',
        amount: 10000,
      },
      checkout = { id: 123, marketplaceId: 456, marketplaceUserId: 789 },
      checkoutWithItems = { id: 123, items: [] },
      organization = { id: 101, enableReceiptSending: true },
      customerContact,
      finixResponse = { id: 'receipt123', status: 'created' },
      finixError,
      allPayments = [
        { id: 123, checkoutId: 123, transactionId: 'TR123', amount: 10000 },
      ],
    } = overrides;

    (queries.getPaymentByTransferId as jest.Mock).mockResolvedValue(payment);
    (queries.getLatestSucceededPayment as jest.Mock).mockResolvedValue(payment);
    (queries.getPayment as jest.Mock).mockResolvedValue(payment);
    (queries.getSucceededPayments as jest.Mock).mockResolvedValue(allPayments);
    (mockSqlDb.checkout as jest.Mock).mockResolvedValue(checkout);
    (queries.getCheckout as jest.Mock).mockResolvedValue(
      checkoutWithItems || checkout,
    );
    (queries.getOrganizationFromCheckout as jest.Mock).mockResolvedValue(
      organization,
    );
    (queries.getCustomerContactFromCheckout as jest.Mock).mockResolvedValue(
      customerContact,
    );

    if (finixError) {
      mockFinixFetch.mockRejectedValue(finixError);
    } else {
      mockFinixFetch.mockResolvedValue(finixResponse);
    }
  };

  const expectFinixCall = (
    expectedMethods: Array<{ type: string; destinations: string[] }>,
    expectedItems?: any[],
  ) => {
    const expectedBody: any = {
      entity_id: 'TR123',
      send_receipt_to_buyer: true,
      requested_delivery_methods: expectedMethods,
    };

    if (expectedItems) {
      expectedBody.items = expectedItems;
    }

    expect(mockFinixFetch).toHaveBeenCalledWith('/receipts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Finix-Version': '2022-02-01',
      },
      body: JSON.stringify(expectedBody),
    });
  };

  const expectNoFinixCall = () => {
    expect(mockFinixFetch).not.toHaveBeenCalled();
  };

  beforeEach(() => {
    jest.clearAllMocks();

    mockSqlDb = {
      knex: {} as any,
      checkout: jest.fn(),
    } as any;

    const { getFinix } = require('.');
    mockFinixFetch = jest.fn();
    getFinix.mockReturnValue({
      fetch: mockFinixFetch,
    });
  });

  describe('sendReceipt', () => {
    const runTest = async (setup: Parameters<typeof setupMocks>[0] = {}) => {
      setupMocks(setup);
      await sendReceipt({ sqldb: mockSqlDb, transferId: 'TR123' });
    };

    // Test cases that should NOT send receipts
    describe('should not send receipt', () => {
      const testCases = [
        {
          name: 'when payment not found',
          setup: { payment: null },
        },
        {
          name: 'when organization has receipt sending disabled',
          setup: { organization: { id: 789, enableReceiptSending: false } },
        },
        {
          name: 'when checkout not found',
          setup: { checkout: null },
        },
        {
          name: 'when customer contact is null',
          setup: { customerContact: null },
        },
        {
          name: 'when neither email nor phone are available',
          setup: { customerContact: {} },
        },
        {
          name: 'when email is not confirmed even if opted in',
          setup: {
            customerContact: {
              email: '<EMAIL>',
              emailConfirmed: false,
              emailOptIn: true,
            },
          },
        },
        {
          name: 'when email is confirmed but not opted in',
          setup: {
            customerContact: {
              email: '<EMAIL>',
              emailConfirmed: true,
              emailOptIn: false,
            },
          },
        },
        {
          name: 'when phone is not confirmed even if opted in',
          setup: {
            customerContact: {
              phone: '+1234567890',
              phoneConfirmed: false,
              phoneOptIn: true,
            },
          },
        },
        {
          name: 'when phone is confirmed but not opted in',
          setup: {
            customerContact: {
              phone: '+1234567890',
              phoneConfirmed: true,
              phoneOptIn: false,
            },
          },
        },
      ];

      testCases.forEach(({ name, setup }) => {
        it(name, async () => {
          await runTest(setup);
          expectNoFinixCall();
        });
      });
    });

    // Test cases that SHOULD send receipts
    describe('should send receipt', () => {
      const testCases = [
        {
          name: 'via email only when customer has confirmed email and opted in',
          customerContact: {
            email: '<EMAIL>',
            emailConfirmed: true,
            emailOptIn: true,
          },
          expectedMethods: [
            { type: 'EMAIL', destinations: ['<EMAIL>'] },
          ],
        },
        {
          name: 'via SMS only when customer has confirmed phone and opted in',
          customerContact: {
            phone: '+1234567890',
            phoneConfirmed: true,
            phoneOptIn: true,
          },
          expectedMethods: [{ type: 'SMS', destinations: ['+1234567890'] }],
        },
        {
          name: 'via both email and SMS when both are confirmed and opted in',
          customerContact: {
            email: '<EMAIL>',
            phone: '+1234567890',
            emailConfirmed: true,
            phoneConfirmed: true,
            emailOptIn: true,
            phoneOptIn: true,
          },
          expectedMethods: [
            { type: 'EMAIL', destinations: ['<EMAIL>'] },
            { type: 'SMS', destinations: ['+1234567890'] },
          ],
        },
        {
          name: 'via email only when email is valid but phone is not confirmed',
          customerContact: {
            email: '<EMAIL>',
            phone: '+1234567890',
            emailConfirmed: true,
            phoneConfirmed: false,
            emailOptIn: true,
            phoneOptIn: true,
          },
          expectedMethods: [
            { type: 'EMAIL', destinations: ['<EMAIL>'] },
          ],
        },
        {
          name: 'via SMS only when phone is valid but email is not opted in',
          customerContact: {
            email: '<EMAIL>',
            phone: '+1234567890',
            emailConfirmed: true,
            phoneConfirmed: true,
            emailOptIn: false,
            phoneOptIn: true,
          },
          expectedMethods: [{ type: 'SMS', destinations: ['+1234567890'] }],
        },
      ];

      testCases.forEach(({ name, customerContact, expectedMethods }) => {
        it(name, async () => {
          await runTest({ customerContact });
          expectFinixCall(expectedMethods);
        });
      });
    });

    it('should handle Finix API errors gracefully', async () => {
      await runTest({
        customerContact: {
          email: '<EMAIL>',
          emailConfirmed: true,
          emailOptIn: true,
        },
        finixError: new Error('Finix API error'),
      });

      expect(mockFinixFetch).toHaveBeenCalled();
    });

    it('should not include items when payment amount is not available', async () => {
      const checkoutItems = [
        {
          id: 1,
          description: 'Procedure A',
          quantity: 1,
          price: 10000,
          type: 'procedure',
        },
        {
          id: 2,
          description: 'Gratuity',
          quantity: 1,
          price: 1500,
          type: 'gratuity',
        },
      ];

      await runTest({
        customerContact: {
          email: '<EMAIL>',
          emailConfirmed: true,
          emailOptIn: true,
        },
        checkoutWithItems: { items: checkoutItems },
      });

      // sendReceipt doesn't have payment amount info, so no items should be included
      expectFinixCall([
        { type: 'EMAIL', destinations: ['<EMAIL>'] },
      ]);
    });
  });

  describe('sendReceiptByPayment', () => {
    const runReceiptByPaymentTest = async (
      setup: Parameters<typeof setupMocks>[0] = {},
      deliveryMethods?: { email?: string; phone?: string },
    ) => {
      setupMocks(setup);
      return sendReceiptByPayment({
        sqldb: mockSqlDb,
        paymentId: 123,
        deliveryMethods,
      });
    };

    describe('should not send receipt', () => {
      const testCases = [
        {
          name: 'when payment not found',
          setup: { payment: null },
          expectedMessage: 'Payment not found',
        },
        {
          name: 'when payment is not accepted',
          setup: {
            payment: {
              id: 123,
              checkoutId: 123,
              transactionId: 'TR123',
              status: PaymentStatus.PENDING,
            },
          },
          expectedMessage: 'Receipt can only be sent for accepted payments',
        },
        {
          name: 'when payment has no transfer ID',
          setup: {
            payment: {
              id: 123,
              checkoutId: 123,
              transactionId: undefined,
              status: PaymentStatus.ACCEPTED,
            },
          },
          expectedMessage: 'No transfer ID found for payment',
        },
      ];

      testCases.forEach(({ name, setup, expectedMessage }) => {
        it(name, async () => {
          const result = await runReceiptByPaymentTest(setup);
          expect(result.success).toBe(false);
          expect(result.message).toBe(expectedMessage);
          expectNoFinixCall();
        });
      });
    });

    describe('should send receipt', () => {
      it('should send receipt for specific payment', async () => {
        const result = await runReceiptByPaymentTest({
          payment: {
            id: 123,
            checkoutId: 123,
            transactionId: 'TR123',
            status: PaymentStatus.ACCEPTED,
            amount: 10000,
          },
          customerContact: {
            email: '<EMAIL>',
            emailConfirmed: true,
            emailOptIn: true,
          },
        });

        expect(result.success).toBe(true);
        expect(result.message).toBe(
          'Receipt sent successfully (ID: receipt123)',
        );
        expectFinixCall([
          { type: 'EMAIL', destinations: ['<EMAIL>'] },
        ]);
      });

      it('should generate correct payment name for second payment of multiple', async () => {
        const allPayments = [
          { id: 122, checkoutId: 123, transactionId: 'TR122', amount: 5000 },
          { id: 123, checkoutId: 123, transactionId: 'TR123', amount: 10000 },
        ];

        const result = await runReceiptByPaymentTest({
          payment: {
            id: 123,
            checkoutId: 123,
            transactionId: 'TR123',
            status: PaymentStatus.ACCEPTED,
            amount: 10000,
          },
          customerContact: {
            email: '<EMAIL>',
            emailConfirmed: true,
            emailOptIn: true,
          },
          checkoutWithItems: {
            items: [
              { description: 'Test Item' },
              { description: 'Test Item 2' },
            ],
          },
          allPayments,
        });

        expect(result.success).toBe(true);
        expectFinixCall(
          [{ type: 'EMAIL', destinations: ['<EMAIL>'] }],
          [
            {
              name: 'Payment (2 of 2)',
              description: 'Test Item, Test Item 2',
              quantity: 0,
              price_details: {
                regular_amount: 10000,
                currency: 'USD',
                price_type: 'REGULAR',
              },
            },
          ],
        );
      });
    });
  });
});
