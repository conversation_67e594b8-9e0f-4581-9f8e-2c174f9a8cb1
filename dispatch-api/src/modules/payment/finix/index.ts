import { Client, Environment } from '@finix-payments/finix';
import fetch, { Response } from 'node-fetch';
import { getConfig } from '../../../config';

interface FinixClient {
  sdk: Client;
  fetch: (
    uri: string,
    options?: {
      method?: string;
      body?: string;
      headers?: Record<string, string>;
    },
  ) => ReturnType<Response['json']>;
}

let client: FinixClient | null = null;

export const getBasePath = () =>
  getConfig().finix.test ? Environment.Sandbox : Environment.Live;

export const getProcessor = () =>
  getConfig().finix.test ? 'DUMMY_V1' : 'FINIX_V1';

export function getFinix() {
  if (!client) {
    const { username, password } = getConfig().finix;

    if (username && password) {
      const auth = Buffer.from(`${username}:${password}`).toString('base64');

      client = {
        sdk: new Client(username, password, getBasePath()),
        fetch: async (
          path: string,
          options?: {
            method?: string;
            body?: string;
            headers?: Record<string, string>;
          },
        ) =>
          (
            await fetch(path[0] === '/' ? `${getBasePath()}${path}` : path, {
              method: options?.method || 'GET',
              headers: {
                Authorization: `Basic ${auth}`,
                'Content-Type':
                  options?.headers?.['Content-Type'] ||
                  'application/vnd.api+json',
                'Finix-Version':
                  options?.headers?.['Finix-Version'] || '2022-02-01',
                ...options?.headers,
              },
              ...(options?.body && { body: options.body }),
            })
          ).json(),
      };
    }
  }

  if (!client) {
    throw new Error('Finix is not configured');
  }

  return client;
}
