import { getFinix } from '.';
import { SqlDbSource } from '../../../datasources';
import { Organization } from '../../organization/sqldb';
import { CheckoutItem } from '../sqldb';
import {
  getAcceptedPayments,
  getCheckout,
  getCustomerContactFromCheckout,
  getOrganizationFromCheckout,
  getPayment,
  getPaymentByTransferId,
} from '../sqldb/queries';
import { PaymentStatus } from '../sqldb/types';
import { FinixReceiptResponse } from './types';

interface FinixReceiptItem {
  name: string;
  description: string;
  quantity: number;
  price_details: {
    regular_amount: number;
    currency: string;
    price_type: 'REGULAR' | 'PROMOTIONAL';
    sale_amount?: number;
  };
}

interface FinixReceiptRequestBody {
  entity_id: string;
  send_receipt_to_buyer: boolean;
  requested_delivery_methods: Array<{
    type: string;
    destinations: string[];
  }>;
  items?: FinixReceiptItem[];
}

interface CustomerContact {
  email?: string;
  phone?: string;
  emailConfirmed?: boolean;
  phoneConfirmed?: boolean;
  emailOptIn?: boolean;
  phoneOptIn?: boolean;
}

interface CustomerOverride {
  email?: string;
  phone?: string;
}

export interface ReceiptSendResult {
  success: boolean;
  message?: string;
  receiptId?: string;
}

export interface SendReceiptParams {
  sqldb: SqlDbSource;
  transferId: string;
  automatic?: boolean;
}

export interface SendReceiptByCheckoutParams {
  sqldb: SqlDbSource;
  checkoutId: number;
  deliveryMethods?: {
    email?: string;
    phone?: string;
  };
}

export interface SendReceiptByPaymentParams {
  sqldb: SqlDbSource;
  paymentId: number;
  deliveryMethods?: {
    email?: string;
    phone?: string;
  };
}

function isReceiptSendingEnabled(
  organization: Organization | null,
  automatic?: boolean,
): {
  enabled: boolean;
  reason?: string;
} {
  if (!organization) {
    return { enabled: false, reason: 'Organization not found' };
  }

  if (automatic && !organization.enableReceiptSending) {
    return {
      enabled: false,
      reason: `Receipt sending disabled for organization: ${organization.id}`,
    };
  }

  return { enabled: true };
}

function mergeCustomerContact(
  baseContact: CustomerContact | null,
  override?: CustomerOverride,
): CustomerContact | null {
  if (!baseContact && !override) return null;

  const merged = {
    ...baseContact,
    email: override?.email || baseContact?.email,
    phone: override?.phone || baseContact?.phone,
  };

  // For manual sending with overrides, assume user has permission to send
  if (override) {
    merged.emailConfirmed = override.email ? true : baseContact?.emailConfirmed;
    merged.phoneConfirmed = override.phone ? true : baseContact?.phoneConfirmed;
    merged.emailOptIn = override.email ? true : baseContact?.emailOptIn;
    merged.phoneOptIn = override.phone ? true : baseContact?.phoneOptIn;
  }

  return merged;
}

function transformCheckoutItemsToFinixItems(
  checkoutItems: CheckoutItem[],
  paymentAmount: number,
  paymentIndex?: number,
  totalPayments?: number,
): FinixReceiptItem[] {
  // Generate payment name based on payment count
  let paymentName = 'Payment';
  if (paymentIndex != null) {
    paymentName = `Payment (${paymentIndex} of ${totalPayments})`;
  }

  // Create description from non-zero price items
  const description = checkoutItems
    .filter((item) => item.price !== 0)
    .map((item) => item.description)
    .join(', ');

  return [
    {
      name: paymentName,
      description,
      quantity: 0,
      price_details: {
        regular_amount: paymentAmount,
        currency: 'USD',
        price_type: 'REGULAR' as const,
      },
    },
  ];
}

async function generateFinixReceipt(
  transferId: string,
  customerContact: CustomerContact,
  checkoutItems?: CheckoutItem[],
  paymentAmount?: number,
  paymentIndex?: number,
  totalPayments?: number,
): Promise<FinixReceiptResponse | null> {
  const finix = getFinix();

  const deliveryMethods = [];

  // Add email delivery if available and opted in
  if (
    customerContact.email &&
    customerContact.emailConfirmed &&
    customerContact.emailOptIn
  ) {
    deliveryMethods.push({
      type: 'EMAIL',
      destinations: [customerContact.email],
    });
  }

  // Add SMS delivery if available and opted in
  if (
    customerContact.phone &&
    customerContact.phoneConfirmed &&
    customerContact.phoneOptIn
  ) {
    deliveryMethods.push({
      type: 'SMS',
      destinations: [customerContact.phone],
    });
  }

  if (deliveryMethods.length === 0) {
    console.log(`No valid delivery methods for transfer: ${transferId}`);
    return null;
  }

  try {
    const requestBody: FinixReceiptRequestBody = {
      entity_id: transferId,
      send_receipt_to_buyer: true,
      requested_delivery_methods: deliveryMethods,
    };

    // Add itemized list if checkout items are provided
    if (
      checkoutItems &&
      checkoutItems.length > 0 &&
      paymentAmount !== undefined
    ) {
      const items = transformCheckoutItemsToFinixItems(
        checkoutItems,
        paymentAmount,
        paymentIndex,
        totalPayments,
      );
      if (items.length > 0) {
        console.log(
          `Adding ${items.length} itemized items to receipt for transfer: ${transferId}`,
        );
        requestBody.items = items;
      } else {
        console.log(
          `No items generated for transfer: ${transferId}, sending receipt without itemization`,
        );
      }
    } else {
      console.log(
        `No checkout items or payment amount provided for transfer: ${transferId}, sending receipt without itemization`,
      );
    }

    const receipt = await finix.fetch('/receipts', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Finix-Version': '2022-02-01',
      },
      body: JSON.stringify(requestBody),
    });

    return receipt;
  } catch (error) {
    console.error(
      `Finix receipt creation failed for transfer ${transferId}:`,
      error,
    );
    return null;
  }
}

interface SendReceiptCoreParams {
  sqldb: SqlDbSource;
  transferId: string;
  checkoutId?: number;
  customerOverride?: CustomerOverride;
  automatic?: boolean;
  paymentAmount?: number;
  paymentId?: number;
}

async function sendReceiptCore(
  params: SendReceiptCoreParams,
): Promise<ReceiptSendResult> {
  const { sqldb, transferId, customerOverride, automatic } = params;

  try {
    let checkoutId = params.checkoutId;
    let paymentAmount = params.paymentAmount;
    let paymentId = params.paymentId;

    if (!checkoutId) {
      const payment = await getPaymentByTransferId(sqldb.knex, { transferId });

      if (!payment) {
        return {
          success: false,
          message: `No payment found for transfer ID: ${transferId}`,
        };
      }

      checkoutId = payment.checkoutId;
      paymentAmount = payment.amount;
      paymentId = payment.id;
    }

    const checkout = await getCheckout(sqldb.knex, {
      id: checkoutId,
    });

    if (!checkout) {
      return {
        success: false,
        message: `No checkout found with ID: ${checkoutId}`,
      };
    }

    const organization = await getOrganizationFromCheckout(sqldb.knex, {
      checkoutId,
    });

    const { enabled, reason } = isReceiptSendingEnabled(
      organization,
      automatic,
    );
    if (!enabled) {
      return { success: false, message: reason };
    }

    const baseCustomerContact = await getCustomerContactFromCheckout(
      sqldb.knex,
      {
        checkoutId,
      },
    );

    const customerContact = mergeCustomerContact(
      baseCustomerContact,
      customerOverride,
    );

    if (!customerContact?.email && !customerContact?.phone) {
      return {
        success: false,
        message: 'No customer contact information available',
      };
    }

    let paymentIndex: number | undefined;
    let totalPayments: number | undefined;

    if (paymentId) {
      const allPayments = await getAcceptedPayments(sqldb.knex, {
        checkoutId,
      });
      totalPayments = allPayments.length;
      paymentIndex = allPayments.findIndex((p) => p.id === paymentId) + 1;
    }

    const checkoutItems = checkout?.items;
    const receipt = await generateFinixReceipt(
      transferId,
      customerContact,
      checkoutItems,
      paymentAmount,
      paymentIndex,
      totalPayments,
    );

    if (!receipt || !receipt.id) {
      const errorMessage = receipt?._embedded?.errors?.[0]?.message;
      return {
        success: false,
        message: `Failed to generate receipt: ${
          errorMessage || 'Unknown error'
        }`,
      };
    }

    console.log(
      `Receipt generated successfully for transfer: ${transferId}, receipt ID: ${receipt.id}`,
    );

    return {
      success: true,
      message: `Receipt sent successfully (ID: ${receipt.id})`,
      receiptId: receipt.id,
    };
  } catch (error) {
    console.error(`Error sending receipt for transfer ${transferId}:`, error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

export async function sendReceipt(params: SendReceiptParams): Promise<void> {
  const { sqldb, transferId, automatic = true } = params;

  const result = await sendReceiptCore({
    sqldb,
    transferId,
    automatic,
  });

  if (!result.success) {
    console.warn(result.message);
  }
}

export async function sendReceiptByPayment(
  params: SendReceiptByPaymentParams,
): Promise<ReceiptSendResult> {
  const { sqldb, paymentId, deliveryMethods } = params;

  try {
    const payment = await getPayment(sqldb.knex, { id: paymentId });
    if (!payment) {
      return {
        success: false,
        message: 'Payment not found',
      };
    }

    if (payment.status !== PaymentStatus.ACCEPTED) {
      return {
        success: false,
        message: 'Receipt can only be sent for accepted payments',
      };
    }

    const transferId = payment.transactionId;
    if (!transferId) {
      return {
        success: false,
        message: 'No transfer ID found for payment',
      };
    }

    return sendReceiptCore({
      sqldb,
      transferId,
      checkoutId: payment.checkoutId,
      customerOverride: deliveryMethods,
      automatic: false,
      paymentAmount: payment.amount,
      paymentId: payment.id,
    });
  } catch (error) {
    console.error(`Error sending receipt for payment ${paymentId}:`, error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}
