export interface FinixDeliveryMethod {
  type: 'EMAIL' | 'SMS' | 'PRINT';
  destinations: string[];
}

export interface FinixEntityDetails {
  id: string;
  created_at: string;
  type: 'TRANSFER' | 'AUTHORIZATION';
}

export interface FinixBusinessAddress {
  city: string;
  country: string;
  region: string;
  line1: string;
  line2?: string;
  postal_code: string;
}

export interface FinixMerchantDetails {
  id: string;
  business_name: string;
  doing_business_as: string;
  business_address: FinixBusinessAddress;
}

export interface FinixBillingAddress {
  city: string;
  country: string;
  region: string;
  line1: string;
  line2?: string;
  postal_code: string;
}

export interface FinixPaymentInstrumentDetails {
  id: string;
  type: 'PAYMENT_CARD' | 'BANK_ACCOUNT';
  billing_address: FinixBillingAddress;
  bin: string;
  brand: string;
  card_type: string;
  last_four: string;
  name: string;
}

export interface FinixAmountBreakdown {
  subtotal_amount?: number;
  tax_amount?: number;
  tip_amount?: number;
  surcharge_amount?: number;
  shipping_amount?: number;
  discount_amount?: number;
}

export interface FinixNetworkDetails {
  emv_application_id?: string;
  emv_application_cryptogram?: string;
  emv_cryptogram_type?: string;
  emv_terminal_verification_results?: string;
}

export interface FinixReceiptLinks {
  self: {
    href: string;
  };
}

export interface FinixReceiptResponse {
  id: string;
  created_at: string;
  updated_at: string;
  amount: number;
  currency: string;
  amount_breakdown: FinixAmountBreakdown | null;
  send_receipt_to_buyer: boolean;
  device_id: string | null;
  entity_details: FinixEntityDetails;
  type: 'BUYER' | 'MERCHANT';
  items: any[];
  merchant_details: FinixMerchantDetails;
  payment_instrument_details: FinixPaymentInstrumentDetails;
  network_details: FinixNetworkDetails | null;
  requested_delivery_methods: FinixDeliveryMethod[];
  receipt_url: string;
  _links: FinixReceiptLinks;
  _embedded: {
    errors: {
      message: string;
    }[];
  };
}
