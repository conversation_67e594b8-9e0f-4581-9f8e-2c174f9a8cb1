import { IsEmail, IsOptional, IsPhoneNumber } from 'class-validator';
import { Field, ID, InputType } from 'type-graphql';
import { IsNumberID } from '../../common/type-graphql';

@InputType()
export class SendReceiptDeliveryMethodsInput {
  @Field({ nullable: true })
  @IsOptional()
  @IsEmail()
  email?: string;

  @Field({ nullable: true })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;
}

@InputType()
export default class SendReceiptInput {
  @Field(() => ID)
  @IsNumberID()
  paymentId!: string;

  @Field(() => SendReceiptDeliveryMethodsInput, { nullable: true })
  @IsOptional()
  deliveryMethods?: SendReceiptDeliveryMethodsInput;
}
