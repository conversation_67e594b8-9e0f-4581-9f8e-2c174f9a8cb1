import { ForbiddenError, UserInputError } from 'apollo-server';
import { Arg, Ctx, Field, Mutation, ObjectType, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { User } from '../../user/sqldb';
import { authorizeCheckout } from '../common';
import { sendReceiptByPayment } from '../finix/receipt';
import SendReceiptInput from './SendReceiptInput';

@ObjectType()
export class SendReceiptPayload {
  @Field()
  success!: boolean;

  @Field({ nullable: true })
  message?: string;
}

@Resolver()
export default class SendReceiptResolver {
  @Mutation(() => SendReceiptPayload)
  async sendReceipt(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: SendReceiptInput,
  ): Promise<SendReceiptPayload> {
    // Validate that at least one delivery method is provided
    if (input.deliveryMethods) {
      const { email, phone } = input.deliveryMethods;
      if (!email && !phone) {
        throw new UserInputError(
          'At least one delivery method (email or phone) must be provided',
        );
      }
    }

    const paymentId = intFromID(input.paymentId) as number;
    const payment = await sqldb.payment(paymentId);

    if (!payment) {
      throw new UserInputError('Payment not found');
    }

    if (!authorizeCheckout(user, payment.checkoutId, { sqldb })) {
      throw new ForbiddenError(
        'Not authorized to send receipt for this checkout',
      );
    }

    return sendReceiptByPayment({
      sqldb,
      paymentId,
      deliveryMethods: input.deliveryMethods,
    });
  }
}
