import { ApolloError } from 'apollo-server';
import { find, pick } from 'lodash';
import { PartialModelGraph } from 'objection';
import { SqlDbSource } from '../../datasources';
import { getOrganization } from '../organization/sqldb/queries';
import { calculateFee } from '../payment/common';
import { completeCheckoutPayment } from '../payment/complete-checkout-payment';
import { PaymentMethod } from '../payment/payment';
import { createPackageCheckout } from './checkout';
import { preparePackageItems } from './common';
import { PackageItem, PackageItemDefinition } from './sqldb';
import { PackageItemDefinitionFields } from './sqldb/types';

interface CreatePackageItemDefinitionParams {
  sqldb: SqlDbSource;
  packageId: number;
  procedureGroupIds: number[];
  packageItemDefinition: PackageItemDefinitionFields;
}

export async function createPackageItemDefinition(
  params: CreatePackageItemDefinitionParams,
): Promise<PackageItemDefinition> {
  const {
    sqldb,
    packageId,
    procedureGroupIds,
    packageItemDefinition: { points },
  } = params;

  const pkg = await sqldb.package(packageId);

  if (!pkg) {
    throw new ApolloError(
      'Invalid package',
      'create-package-item-definition:package',
    );
  }

  for (const id of procedureGroupIds) {
    const group = await sqldb.procedureBaseDefGroup(id);

    if (!group || group.marketplaceId !== pkg.marketplaceId) {
      throw new ApolloError(
        'Invalid procedure group',
        'create-package-item-definition:procedure-group',
      );
    }
  }

  const procedureGroups = procedureGroupIds.map((id) => ({
    id,
  }));

  return await PackageItemDefinition.query(sqldb.knex).insertGraphAndFetch(
    { packageId, procedureGroups, points },
    { relate: true },
  );
}

interface UpdatePackageItemDefinitionParams {
  sqldb: SqlDbSource;
  packageItemDefId: number;
  procedureGroupIds?: number[];
  packageItemDefinition?: Partial<PackageItemDefinitionFields>;
}

export async function updatePackageItemDefinition(
  params: UpdatePackageItemDefinitionParams,
): Promise<PackageItemDefinition> {
  const {
    sqldb,
    packageItemDefId,
    procedureGroupIds,
    packageItemDefinition = {},
  } = params;

  const packageItemDef = await sqldb.packageItemDefinition(packageItemDefId);

  if (!packageItemDef) {
    throw new ApolloError(
      'Invalid package item definition',
      'update-package-item-definition:package-item-definition',
    );
  }

  const pkg = await sqldb.package(packageItemDef.packageId);

  if (!pkg) {
    throw new ApolloError(
      'Invalid package',
      'update-package-item-definition:package',
    );
  }

  if (procedureGroupIds) {
    for (const id of procedureGroupIds) {
      const group = await sqldb.procedureBaseDefGroup(id);

      if (!group || group.marketplaceId !== pkg.marketplaceId) {
        throw new ApolloError(
          'Invalid procedure group',
          'update-package-item-definition:procedure-group',
        );
      }
    }
  }

  const updateParams = pick(packageItemDefinition, ['points']);
  const procedureGroups = procedureGroupIds?.map((id) => ({ id }));

  return await PackageItemDefinition.query(sqldb.knex)
    .modify('withArchived')
    .upsertGraphAndFetch(
      {
        id: packageItemDefId,
        ...updateParams,
        ...(procedureGroups && { procedureGroups }),
      },
      {
        relate: ['procedureGroups'],
        unrelate: ['procedureGroups'],
      },
    );
}

interface GrantPackageParams {
  sqldb: SqlDbSource;
  packageId: number;
  marketplaceUserId: number;
  membershipId?: number;
  checkoutId?: number;
  expiresAt?: Date;
  primaryOrganizationId?: number | null;
  paymentMethod?: PaymentMethod;
  fee?: number;
}

export async function grantPackage(
  params: GrantPackageParams,
): Promise<PackageItem[]> {
  const {
    sqldb,
    packageId,
    marketplaceUserId,
    membershipId,
    checkoutId,
    expiresAt,
    primaryOrganizationId,
    paymentMethod,
    fee: feeParam,
  } = params;

  const pkg = await sqldb.package(packageId);

  if (!pkg) {
    throw new ApolloError('Invalid package', 'grant-package:package');
  }

  const membership = await sqldb.membership(membershipId);

  if (membershipId && !membership) {
    throw new ApolloError('Invalid membership', 'grant-package:membership');
  }

  if (
    primaryOrganizationId &&
    !validatePrimaryOrganization({
      sqldb,
      marketplaceId: pkg.marketplaceId,
      primaryOrganizationId,
    })
  ) {
    throw new ApolloError(
      'Invalid primary organization',
      'grant-package:primary-organization',
    );
  }

  const marketplace = await sqldb.marketplace(pkg.marketplaceId);
  const paymentAccountId = marketplace?.paymentAccountId;

  if (paymentMethod && !paymentAccountId) {
    throw new ApolloError(
      'Marketplace does not have a payment account',
      'grant-package:payment-account',
    );
  }

  const feeProfile = {
    fixed: marketplace?.feeProfileFixed,
    basisPoints: marketplace?.feeProfileBasisPoints,
  };

  const amount = membership?.membershipDefinition?.price ?? pkg.price;
  const fee = feeParam ?? calculateFee({ amount, feeProfile });
  const cost = amount - fee;

  const packageItems: PartialModelGraph<PackageItem>[] = preparePackageItems({
    packageItemDefinitions: pkg.packageItemDefinitions ?? [],
    marketplaceUserId,
    membershipId,
    checkoutId,
    cost,
    expiresAt,
    primaryOrganizationId,
  });

  if (!packageItems?.length) {
    throw new ApolloError(
      `The package is empty. Add items to the package before granting it.`,
      'grant-package:package-items',
    );
  }

  if (!membershipId && !checkoutId && paymentAccountId) {
    const checkout = await createPackageCheckout({
      sqldb,
      pkg,
      marketplaceUserId,
      paymentMethod,
    });

    if (!checkout) {
      throw new ApolloError(
        'Failed to create checkout',
        'grant-package:create-checkout',
      );
    }

    for (const packageItem of packageItems) {
      packageItem.checkoutId = checkout.id;
    }

    if (paymentMethod) {
      await completeCheckoutPayment({
        sqldb,
        checkoutId: checkout.id,
        paymentAccountId,
        expectedAmount: amount,
        feeProfile,
      });
    }
  }

  return PackageItem.query(sqldb.knex).insertGraphAndFetch(packageItems);
}

interface UpdatePackageItemParams {
  sqldb: SqlDbSource;
  packageItemId: number;
  packageItemParams?: Partial<PackageItem>;
}

export async function updatePackageItem(
  params: UpdatePackageItemParams,
): Promise<PackageItem> {
  const { sqldb, packageItemId, packageItemParams } = params;

  const packageItem = await sqldb.packageItem(packageItemId);

  if (!packageItem) {
    throw new ApolloError(
      'Invalid package item',
      'update-package-item:package-item',
    );
  }

  const updateParams = pick(packageItemParams, [
    'expiresAt',
    'primaryOrganizationId',
  ]);

  if (updateParams.expiresAt && updateParams.expiresAt < new Date()) {
    throw new ApolloError(
      'Expiration date must be in the future',
      'update-package-item:expiration-date',
    );
  }

  if (
    updateParams.primaryOrganizationId &&
    !validatePrimaryOrganization({
      sqldb,
      marketplaceId: packageItem.packageItemDefinition?.package?.marketplaceId,
      primaryOrganizationId: updateParams.primaryOrganizationId,
    })
  ) {
    throw new ApolloError(
      'Invalid primary organization',
      'update-package-item:primary-organization',
    );
  }

  return await PackageItem.query(sqldb.knex).patchAndFetchById(
    packageItemId,
    updateParams,
  );
}

interface ValidatePrimaryOrganizationParams {
  sqldb: SqlDbSource;
  marketplaceId?: number;
  primaryOrganizationId: number;
}

export async function validatePrimaryOrganization({
  sqldb,
  marketplaceId,
  primaryOrganizationId,
}: ValidatePrimaryOrganizationParams): Promise<boolean> {
  const organization = await getOrganization(sqldb.knex, {
    id: primaryOrganizationId,
  }).withGraphJoined('marketplaces');

  return Boolean(
    marketplaceId &&
      find(organization?.marketplaces ?? [], { id: marketplaceId }),
  );
}
