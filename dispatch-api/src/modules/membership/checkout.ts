import { SqlDbSource } from '../../datasources';
import { createCheckout } from '../payment/create-checkout';
import { PaymentMethod } from '../payment/payment';
import { Checkout } from '../payment/sqldb';
import { CheckoutItemType } from '../payment/sqldb/types';
import { Membership, Package } from './sqldb';

interface CreateMembershipCheckoutParams {
  sqldb: SqlDbSource;
  membership: Membership;
  paymentMethod?: PaymentMethod;
}

export async function createMembershipCheckout(
  params: CreateMembershipCheckoutParams,
): Promise<Checkout | null> {
  const { sqldb, membership, paymentMethod } = params;
  const { marketplaceUserId, membershipDefinitionId } = membership;

  const membershipDefinition = await sqldb.membershipDefinition(
    membershipDefinitionId,
  );

  if (!membershipDefinition?.price) {
    return null;
  }

  const items = [
    {
      type: CheckoutItemType.MEMBERSHIP,
      quantity: 1,
      price: membershipDefinition.price,
      description: `Membership: ${membershipDefinition?.name ?? ''}`,
      key: String(membership.id),
    },
  ];

  return await createCheckout({
    sqldb,
    items,
    marketplaceUserId,
    marketplaceId: membershipDefinition.marketplaceId,
    paymentMethod,
  });
}

interface CreatePackageCheckoutParams {
  sqldb: SqlDbSource;
  pkg: Package;
  marketplaceUserId: number;
  paymentMethod?: PaymentMethod;
}

export async function createPackageCheckout(
  params: CreatePackageCheckoutParams,
): Promise<Checkout | null> {
  const { sqldb, pkg, marketplaceUserId, paymentMethod } = params;

  const price = pkg.price;

  const items = [
    {
      type: CheckoutItemType.PACKAGE,
      quantity: 1,
      price,
      description: `Package: ${pkg.name}`,
      key: String(pkg.id),
    },
  ];

  return await createCheckout({
    sqldb,
    items,
    marketplaceUserId,
    marketplaceId: pkg.marketplaceId,
    paymentMethod,
  });
}
