export enum MembershipDefinitionPeriodType {
  QUARTERLY = 'quarterly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export enum MembershipStatusType {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  CANCELLED = 'cancelled',
  PAYMENT_ERROR = 'payment_error',
}

export interface PackageFields {
  name: string;
  price: number;
  marketplaceId: number;
  advertise: boolean;
  description?: string;
  list?: string;
}

export interface PackageItemDefinitionFields {
  points: number;
}

export interface PackageItemFields {
  points: number;
  balance: number;
  costBasis: number;
  expiresAt?: Date;
  primaryOrganizationId?: number;
}

export interface MembershipDefinitionFields {
  name: string;
  price: number;
  period: MembershipDefinitionPeriodType;
  marketplaceId: number;
  packageId?: number | null;
  advertise: boolean;
  description?: string;
  list?: string;
}

export interface DiscountFields {
  percentage: number;
}

export interface MembershipFields {
  membershipDefinitionId: number;
  marketplaceUserId: number;
  renewalDate: Date;
  status: MembershipStatusType;
}

export interface OrganizationProcedureFields {
  procedureId: number;
  organizationId: number;
}
