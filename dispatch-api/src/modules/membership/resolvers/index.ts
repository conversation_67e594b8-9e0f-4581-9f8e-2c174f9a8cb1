import ArchiveDiscountResolver from './ArchiveDiscountResolver';
import ArchiveMembershipDefinitionResolver from './ArchiveMembershipDefinitionResolver';
import ArchivePackageItemDefinitionResolver from './ArchivePackageItemDefinitionResolver';
import ArchivePackageResolver from './ArchivePackageResolver';
import CancelMembershipResolver from './CancelMembershipResolver';
import CreateDiscountResolver from './CreateDiscountResolver';
import CreateMembershipDefinitionResolver from './CreateMembershipDefinitionResolver';
import CreateMembershipResolver from './CreateMembershipResolver';
import CreatePackageItemDefinitionResolver from './CreatePackageItemDefinitionResolver';
import CreatePackageResolver from './CreatePackageResolver';
import DeleteMembershipResolver from './DeleteMembershipResolver';
import DeletePackageItemResolver from './DeletePackageItemResolver';
import DiscountResolver from './DiscountResolver';
import GrantPackageResolver from './GrantPackageResolver';
import MembershipDefinitionResolver from './MembershipDefinitionResolver';
import MembershipResolver from './MembershipResolver';
import PackageItemDefinitionResolver from './PackageItemDefinitionResolver';
import PackageItemResolver from './PackageItemResolver';
import PackageResolver from './PackageResolver';
import ReactivateMembershipResolver from './ReactivateMembershipResolver';
import UpdateDiscountResolver from './UpdateDiscountResolver';
import UpdateMembershipDefinitionResolver from './UpdateMembershipDefinitionResolver';
import UpdateMembershipResolver from './UpdateMembershipResolver';
import UpdatePackageItemDefinitionResolver from './UpdatePackageItemDefinitionResolver';
import UpdatePackageItemResolver from './UpdatePackageItemResolver';
import UpdatePackageResolver from './UpdatePackageResolver';

export default [
  ArchiveMembershipDefinitionResolver,
  ArchivePackageResolver,
  CreateDiscountResolver,
  CreateMembershipDefinitionResolver,
  CreatePackageItemDefinitionResolver,
  CreatePackageResolver,
  ArchiveDiscountResolver,
  ArchivePackageItemDefinitionResolver,
  UpdateDiscountResolver,
  UpdateMembershipDefinitionResolver,
  UpdatePackageResolver,
  UpdatePackageItemDefinitionResolver,
  UpdateMembershipResolver,
  PackageResolver,
  PackageItemDefinitionResolver,
  MembershipDefinitionResolver,
  MembershipResolver,
  PackageItemResolver,
  DiscountResolver,
  CreateMembershipResolver,
  GrantPackageResolver,
  DeleteMembershipResolver,
  DeletePackageItemResolver,
  UpdatePackageResolver,
  UpdatePackageItemResolver,
  CancelMembershipResolver,
  ReactivateMembershipResolver,
] as const;
