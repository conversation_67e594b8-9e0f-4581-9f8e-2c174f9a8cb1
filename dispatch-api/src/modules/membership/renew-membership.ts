import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { completeCheckoutPayment } from '../payment/complete-checkout-payment';
import { createMembershipCheckout } from './checkout';
import { getRenewalDate } from './common';
import { grantPackage, validatePrimaryOrganization } from './package-item';
import { Membership } from './sqldb';
import { getMembership } from './sqldb/queries';
import { MembershipStatusType } from './sqldb/types';

interface RenewMembershipParams {
  sqldb: SqlDbSource;
  membershipId: number;
  paymentInstrumentId?: number;
}

export async function renewMembership(
  params: RenewMembershipParams,
): Promise<Membership> {
  const { sqldb, membershipId, paymentInstrumentId } = params;

  const membership = await getMembership(sqldb.knex, { id: membershipId });

  if (!membership?.membershipDefinition) {
    throw new ApolloError('Invalid membership', 'renew-membership:membership');
  }

  const { renewalDate } = membership;
  const renewal = new Date(renewalDate);

  // renew if within 1 hour of renewal date
  if (+renewal - Date.now() > 60 * 60 * 1000) {
    return membership;
  }

  const marketplaceUser = await sqldb.marketplaceUser(
    membership.marketplaceUserId,
  );

  if (!marketplaceUser) {
    throw new ApolloError(
      'Invalid marketplace user',
      'renew-membership:marketplace-user',
    );
  }

  const { membershipDefinition } = membership;

  const marketplace = await sqldb.marketplace(
    membershipDefinition.marketplaceId,
  );

  const packageId = membershipDefinition.packageId;
  const price = membershipDefinition.price;

  renewal.setSeconds(0, 0);
  let newRenewal = undefined;

  let status = MembershipStatusType.INACTIVE;
  let checkout;

  if (price > 0) {
    if (
      marketplaceUser.primaryInstrumentId &&
      membership.status !== MembershipStatusType.CANCELLED
    ) {
      const paymentAccountId = marketplace?.paymentAccountId;

      if (!paymentAccountId) {
        throw new ApolloError(
          'Marketplace does not have a payment account',
          'renew-membership:payment-account',
        );
      }

      checkout = await createMembershipCheckout({
        sqldb,
        membership,
        paymentMethod: {
          marketplaceUserId: marketplaceUser.id,
          paymentInstrumentId:
            paymentInstrumentId || marketplaceUser.primaryInstrumentId,
        },
      });

      if (checkout) {
        try {
          await completeCheckoutPayment({
            sqldb,
            checkoutId: checkout.id,
            paymentAccountId,
            expectedAmount: price,
            feeProfile: {
              fixed: marketplace?.feeProfileFixed,
              basisPoints: marketplace?.feeProfileBasisPoints,
            },
          });

          status = MembershipStatusType.ACTIVE;
        } catch (e) {
          await checkout.$query(sqldb.knex).patch({ voidedAt: new Date() });

          status = MembershipStatusType.PAYMENT_ERROR;
        }
      }
    }
  } else {
    status = MembershipStatusType.ACTIVE;
  }

  if (status === MembershipStatusType.ACTIVE) {
    newRenewal = getRenewalDate(membershipDefinition.period, renewal);

    if (packageId) {
      try {
        const lastPackageItem = await membership
          .$relatedQuery('packageItems', sqldb.knex)
          .orderBy('createdAt', 'desc')
          .limit(1);

        let primaryOrganizationId = lastPackageItem[0]?.primaryOrganizationId;

        if (
          primaryOrganizationId &&
          !(await validatePrimaryOrganization({
            sqldb,
            marketplaceId: marketplace?.id,
            primaryOrganizationId,
          }))
        ) {
          primaryOrganizationId = null;
        }

        await grantPackage({
          sqldb,
          packageId,
          marketplaceUserId: marketplaceUser.id,
          membershipId: membership.id,
          checkoutId: checkout?.id,
          primaryOrganizationId,
          expiresAt: newRenewal,
        });
      } catch (e) {
        console.error(e);
        status = MembershipStatusType.INACTIVE;

        // // refund the payment
        // if (checkout && price > 0) {
        //   try {
        //     await refundCheckout({
        //       sqldb,
        //       checkoutId: checkout.id,
        //       refundAmount: price,
        //       balanceAfterRefund: 0,
        //     });
        //   } catch (e) {
        //     console.error(e);
        //   }
        // }
      }
    }
  }

  return await membership
    .$query(sqldb.knex)
    .patchAndFetch({ status, renewalDate: newRenewal });
}
