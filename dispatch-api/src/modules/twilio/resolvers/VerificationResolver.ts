import { ApolloError, ForbiddenError } from 'apollo-server-express';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { authorizeMarketplaceUser } from '../../marketplace/common';
import { User } from '../../user/sqldb';
import { confirmConsumerEmail, requestConfirmConsumerEmail } from '../email';
import { confirmConsumerPhone, requestConfirmConsumerPhone } from '../text';
import ConfirmConsumerEmailInput from './ConfirmConsumerEmailInput';
import ConfirmConsumerPhoneInput from './ConfirmConsumerPhoneInput';
import RequestConfirmConsumerEmailInput from './RequestConfirmConsumerEmailInput';
import RequestConfirmConsumerPhoneInput from './RequestConfirmConsumerPhoneInput';

@Resolver()
export default class VerificationResolver {
  @Mutation(() => Boolean)
  async requestConfirmConsumerEmail(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input', () => RequestConfirmConsumerEmailInput)
    input: RequestConfirmConsumerEmailInput,
  ): Promise<boolean> {
    const { email } = input;

    const marketplaceId = intFromID(input.marketplaceId) as number;
    const marketplaceUserId = intFromID(input.marketplaceUserId) as number;

    const marketplaceUser = await sqldb.marketplaceUser(marketplaceUserId);

    if (!marketplaceUser) {
      throw new ApolloError(
        'Invalid marketplace user',
        'request-confirm-consumer-email:marketplace-user',
      );
    }

    if (
      !(await authorizeMarketplaceUser(user, marketplaceUser.groupId, {
        sqldb,
        full: true,
      }))
    ) {
      throw new ForbiddenError('Not authorized (requestConfirmConsumerEmail)');
    }

    if (!marketplaceUser.email) {
      throw new ApolloError(
        'Marketplace user does not have an email',
        'request-confirm-consumer-email:email',
      );
    }

    if (marketplaceUser.emailConfirmed) {
      throw new ApolloError(
        'Email already confirmed',
        'request-confirm-consumer-email:email-confirmed',
      );
    }

    return requestConfirmConsumerEmail({
      sqldb,
      marketplaceId,
      marketplaceUserId,
      email,
    });
  }

  @Mutation(() => Boolean)
  async confirmConsumerEmail(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input', () => ConfirmConsumerEmailInput)
    input: ConfirmConsumerEmailInput,
  ): Promise<boolean> {
    const { token } = input;

    const marketplaceId = intFromID(input.marketplaceId) as number;

    return confirmConsumerEmail({
      sqldb,
      marketplaceId,
      token,
    });
  }

  @Mutation(() => Boolean)
  async requestConfirmConsumerPhone(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input', () => RequestConfirmConsumerPhoneInput)
    input: RequestConfirmConsumerPhoneInput,
  ): Promise<boolean> {
    const marketplaceId = intFromID(input.marketplaceId) as number;
    const marketplaceUserId = intFromID(input.marketplaceUserId) as number;

    const marketplaceUser = await sqldb.marketplaceUser(marketplaceUserId);

    if (!marketplaceUser) {
      throw new ApolloError(
        'Invalid marketplace user',
        'request-confirm-consumer-phone:marketplace-user',
      );
    }

    if (
      !(await authorizeMarketplaceUser(user, marketplaceUser.groupId, {
        sqldb,
        full: true,
      }))
    ) {
      throw new ForbiddenError('Not authorized (requestConfirmConsumerPhone)');
    }

    if (!marketplaceUser.phone) {
      throw new ApolloError(
        'Marketplace user does not have a phone number',
        'request-confirm-consumer-phone:phone',
      );
    }

    if (marketplaceUser.phoneConfirmed) {
      throw new ApolloError(
        'Phone already confirmed',
        'request-confirm-consumer-phone:phone-confirmed',
      );
    }

    return requestConfirmConsumerPhone({
      sqldb,
      to: marketplaceUser.phone,
      marketplaceId,
    });
  }

  @Mutation(() => Boolean)
  async confirmConsumerPhone(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input', () => ConfirmConsumerPhoneInput)
    input: ConfirmConsumerPhoneInput,
  ): Promise<boolean> {
    const marketplaceId = intFromID(input.marketplaceId) as number;
    const marketplaceUserId = intFromID(input.marketplaceUserId) as number;

    const { code } = input;

    const marketplaceUser = await sqldb.marketplaceUser(marketplaceUserId);

    if (!marketplaceUser) {
      throw new ApolloError(
        'Invalid marketplace user',
        'confirm-consumer-phone:marketplace-user',
      );
    }

    if (
      !(await authorizeMarketplaceUser(user, marketplaceUser.groupId, {
        sqldb,
        full: true,
      }))
    ) {
      throw new ForbiddenError('Not authorized (confirmConsumerPhone)');
    }

    if (!marketplaceUser.phone) {
      throw new ApolloError(
        'Marketplace user does not have a phone number',
        'confirm-consumer-phone:phone',
      );
    }

    if (marketplaceUser.phoneConfirmed) {
      throw new ApolloError(
        'Phone already confirmed',
        'confirm-consumer-phone:phone-confirmed',
      );
    }

    return confirmConsumerPhone({
      sqldb,
      to: marketplaceUser.phone,
      code,
      marketplaceId,
      marketplaceUserId,
    });
  }
}
