import { ApolloError, ForbiddenError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { RoleScope } from '../../role/sqldb/Role';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { upsertTwilioIntegration } from '../integration';
import UpdateTwilioIntegrationInput from './UpdateTwilioIntegrationInput';
import { TwilioIntegration } from '../sqldb';

@Resolver()
export default class UpdateTwilioIntegrationResolver {
  @Mutation(() => TwilioIntegration, { nullable: true })
  async updateTwilioIntegration(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: UpdateTwilioIntegrationInput,
  ): Promise<TwilioIntegration | null> {
    const marketplace = await sqldb.marketplace(intFromID(input.marketplaceId));
    const {
      appointmentRequestsEnabled,
      appointmentsEnabled,
      // clientDayReminder,
    } = input;

    if (!marketplace) {
      throw new ApolloError(
        'Invalid marketplace',
        'update-attentive-integration:marketplace',
      );
    }

    if (
      !authorize(user, ['marketplaces:full']) &&
      !authorize(user, 'marketplace:update', {
        scope: RoleScope.MARKETPLACE,
        resourceId: marketplace.id,
      })
    ) {
      throw new ForbiddenError('Not authorized (updateTwilioIntegration)');
    }

    return upsertTwilioIntegration({
      sqldb,
      marketplaceId: marketplace.id,
      appointmentRequestsEnabled,
      appointmentsEnabled,
      // clientDayReminder,
    });
  }
}
