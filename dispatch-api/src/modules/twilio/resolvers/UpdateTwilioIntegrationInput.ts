import { Field, ID, InputType } from 'type-graphql';

@InputType()
export default class UpdateTwilioIntegrationInput {
  @Field(() => Boolean, { defaultValue: false, nullable: true })
  appointmentsEnabled?: boolean;

  @Field(() => <PERSON>olean, { defaultValue: false, nullable: true })
  appointmentRequestsEnabled?: boolean;

  // @Field(() => Boolean, { defaultValue: false, nullable: true })
  // clientDayReminder?: boolean;

  @Field(() => ID)
  marketplaceId!: string;
}
