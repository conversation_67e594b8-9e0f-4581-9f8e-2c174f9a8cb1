import { ApolloError, UserInputError } from 'apollo-server-core';
import jwt from 'jsonwebtoken';
import validator from 'validator';
import { getConfig } from '../../config';
import { SqlDbSource } from '../../datasources';
import { marketplaceRender } from '../email/notification/template';
import { marketplaceSend } from '../email/sendgrid';
import { MarketplaceUser } from '../marketplace/sqldb';
import updateMarketplaceUser from '../marketplace/update-marketplace-user';

const marketplaceUserSecret = (marketplaceUser: MarketplaceUser) => {
  return `${getConfig().auth.secret}${marketplaceUser.email}`;
};

interface ResetJwtPayload {
  id: number;
}

interface RequestConfirmEmailParams {
  sqldb: SqlDbSource;
  marketplaceId: number;
  marketplaceUserId: number;
  email: string;
}

export async function requestConfirmConsumerEmail(
  params: RequestConfirmEmailParams,
): Promise<boolean> {
  const { sqldb, email: emailInput, marketplaceId, marketplaceUserId } = params;

  const email = validator.normalizeEmail(emailInput, {
    gmail_remove_subaddress: false,
  });

  if (!email) {
    throw new UserInputError('Invalid email', { invalidArgs: ['email'] });
  }

  const marketplaceUser = await sqldb.marketplaceUser(marketplaceUserId);
  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'request-confirm-email:marketplace-id',
    );
  }

  const sendgrid = await sqldb.sendgridIntegrationForMarketplace(marketplaceId);

  if (!sendgrid) {
    throw new ApolloError(
      'Marketplace does not have a configured SendGrid integration',
      'request-confirm-email:marketplace',
    );
  }

  if (!sendgrid.encryptedApiKey || !sendgrid.enabled) {
    throw new ApolloError(
      'Marketplace SendGrid integration must have a valid API key and be enabled',
      'request-confirm-email:sendgrid',
    );
  }

  if (marketplaceUser) {
    if (email !== marketplaceUser.email) {
      throw new ApolloError(
        'Provided email does not match marketplace user email',
        'request-confirm-email:email',
      );
    }

    const name = marketplace.name;
    const payload = {
      id: marketplaceUser.id,
      mgId: marketplaceUser.groupId,
      email,
    } as ResetJwtPayload;

    const token = jwt.sign(payload, marketplaceUserSecret(marketplaceUser), {
      expiresIn: '2h',
    });

    await marketplaceSend({
      name,
      sendgrid,
      to: email,
      subject: 'Confirm your email',
      template: marketplaceRender({
        preheader: name,
        title: `We received a request to confirm your ${name} email`,
        messages: [
          {
            text: 'To confirm your email, click the button below.',
          },
        ],
        action: {
          text: 'Confirm your email',
          url: `${sendgrid.webOrigin}/confirm-email?token=${token}`,
        },
      }),
    });
  }

  return true;
}

interface ConfirmEmailParams {
  sqldb: SqlDbSource;
  token: string;
  marketplaceId: number;
}

export async function confirmConsumerEmail(
  params: ConfirmEmailParams,
): Promise<boolean> {
  const { sqldb, token, marketplaceId } = params;
  const decoded = jwt.decode(token) as ResetJwtPayload | null;
  const id = decoded?.id;
  const marketplaceUser = await sqldb.marketplaceUser(id);
  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'request-confirm-email:marketplace-id',
    );
  }

  const sendgrid = await sqldb.sendgridIntegrationForMarketplace(marketplaceId);

  if (!sendgrid) {
    throw new ApolloError(
      'Marketplace does not have a configured SendGrid integration',
      'request-confirm-email:marketplace',
    );
  }

  if (!sendgrid.encryptedApiKey || !sendgrid.enabled) {
    throw new ApolloError(
      'Marketplace SendGrid integration must have a valid API key and be enabled',
      'request-confirm-email:sendgrid',
    );
  }

  if (!marketplaceUser) {
    throw new ApolloError('Invalid token', 'confirm-email:token');
  }

  if (!marketplaceUser.email) {
    throw new ApolloError('Marketplace user is missing');
  }

  try {
    jwt.verify(token, marketplaceUserSecret(marketplaceUser));
  } catch (err) {
    throw new ApolloError('Invalid token', 'confirm-email:token');
  }

  await updateMarketplaceUser({
    sqldb,
    id: marketplaceUser.id,
    emailConfirmed: true,
  });

  const name = marketplace.name;

  await marketplaceSend({
    name,
    sendgrid,
    to: marketplaceUser.email,
    subject: 'Your email has been verified',
    template: marketplaceRender({
      messages: [
        {
          text: `You can now access your profile and view your appointments.`,
        },
      ],
      action: {
        text: 'View my account',
        url: `${sendgrid.webOrigin}/login`,
      },
    }),
  });

  return true;
}
