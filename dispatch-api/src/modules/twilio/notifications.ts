import { SqlDbSource } from '../../datasources';
import { marketplaceRender } from '../email/notification/template';
import { Appointment, AppointmentRequest } from '../appointment/sqldb';
import { ProcedureBaseDefinition } from '../procedure/sqldb';
import dayjs from '../common/dayjs';
import sendText from './text';
import { ParticipantType } from '../appointment/sqldb/types';
import { marketplaceSend } from '../email/sendgrid';
import { groupIncludesBaseDef } from '../procedure-base-def-group/common';
import { MarketplaceUser } from '../marketplace/sqldb';
import { getMarketplaceMessagingServiceSid } from './integration';

interface AppointmentRequestNotificationParams {
  sqldb: SqlDbSource;
  appointmentRequest: AppointmentRequest;
  marketplaceUserId?: number;
}

export const getName = (baseDefs: ProcedureBaseDefinition[]) => {
  if (baseDefs.length === 1) {
    return baseDefs.map((baseDef) => baseDef.name);
  }

  const length = baseDefs.length;

  return `${baseDefs[0].name} + ${length - 1} other procedure${
    length > 2 ? 's' : ''
  }`;
};

export async function appointmentRequestNotification(
  params: AppointmentRequestNotificationParams,
): Promise<void> {
  const { sqldb, appointmentRequest, marketplaceUserId } = params;

  let marketplaceUser: undefined | MarketplaceUser;

  if (marketplaceUserId) {
    marketplaceUser =
      (await sqldb.marketplaceUser(marketplaceUserId)) ?? undefined;
  } else {
    const client = appointmentRequest.clientProfiles?.find(Boolean);
    marketplaceUser =
      client?.marketplaceUser ||
      (await client?.$relatedQuery('marketplaceUser', sqldb.knex));
  }

  if (!marketplaceUser) return;

  const marketplaceId = appointmentRequest.procedureBaseDefs?.find((r) =>
    Boolean(r.id),
  )?.marketplaceId;
  const marketplace = await sqldb.marketplace(marketplaceId);
  const twilio = await sqldb.twilioIntegrationForMarketplace(marketplaceId);

  const group = await sqldb.procedureBaseDefGroup(
    marketplace?.notificationsGroupId,
  );

  if (
    !groupIncludesBaseDef(group, marketplace?.procedureBaseDefs ?? [], sqldb)
  ) {
    return;
  }

  const sendgrid = await sqldb.sendgridIntegrationForMarketplace(marketplaceId);
  const name = marketplace?.name;

  if (marketplaceUser.email && sendgrid?.enabled && name) {
    try {
      marketplaceSend({
        name,
        sendgrid,
        to: marketplaceUser.email,
        subject: 'Appointment request received',
        template: marketplaceRender({
          title: `We received your appointment request with ${name}!`,
          messages: [
            {
              text: `We have received your appointment request for ${getName(
                appointmentRequest.procedureBaseDefs || [],
              )}. Please give us some time to review your request and confirm it.`,
            },
          ],
          action: {
            text: 'View your appointment',
            url: `${sendgrid.webOrigin}/appointments/request-${appointmentRequest.id}`,
          },
        }),
      });
    } catch (err) {
      console.log(err);
    }
  }
  if (
    marketplaceUser.phone &&
    marketplaceUser.phoneConfirmed &&
    marketplaceId &&
    twilio?.appointmentRequestsEnabled
  ) {
    const messagingServiceSid = await getMarketplaceMessagingServiceSid({
      sqldb,
      marketplaceId,
    });
    try {
      sendText({
        to: marketplaceUser.phone,
        body: `We have received your appointment request with ${name}. Please give us some time to review your request and confirm it.`,
        messagingServiceSid,
      });
    } catch (err) {
      console.log(err);
    }
  }
}

interface AppointmentBookedNotificationParams {
  sqldb: SqlDbSource;
  appointment: Appointment;
}

export async function appointmentBookedNotification(
  params: AppointmentBookedNotificationParams,
): Promise<void> {
  const { sqldb, appointment } = params;
  const marketplaceId = appointment.procedureBaseDefs?.find((r) =>
    Boolean(r.id),
  )?.marketplaceId;
  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    return;
  }

  const group = await sqldb.procedureBaseDefGroup(
    marketplace?.notificationsGroupId,
  );

  if (
    !groupIncludesBaseDef(group, marketplace?.procedureBaseDefs ?? [], sqldb)
  ) {
    return;
  }

  const patient = appointment.participants?.find(
    (p) => p.type === ParticipantType.PATIENT,
  );
  const client = patient?.clientProfile;

  const marketplaceUsers =
    client?.marketplaceUsers ||
    (await client?.$relatedQuery('marketplaceUsers', sqldb.knex));
  const marketplaceUserRecord =
    client?.marketplaceUser ||
    (await client?.$relatedQuery('marketplaceUser', sqldb.knex));

  const marketplaceUser = [
    marketplaceUserRecord,
    ...(marketplaceUsers ?? []),
  ].find(
    (user) =>
      user?.email === client?.email && user?.groupId === marketplace.groupId,
  );

  if (!marketplaceUser) {
    return;
  }

  const sendgrid = await sqldb.sendgridIntegrationForMarketplace(marketplaceId);
  const name = marketplace.name;
  const twilio = await sqldb.twilioIntegrationForMarketplace(marketplaceId);

  if (
    marketplaceUser.email &&
    marketplaceUser.emailConfirmed &&
    sendgrid?.enabled
  ) {
    try {
      marketplaceSend({
        name,
        sendgrid,
        to: marketplaceUser.email,
        subject: `Your appointment with ${name} is confirmed`,
        template: marketplaceRender({
          title: `We have confirmed your appointment!`,
          messages: [
            {
              text: `Your appointment for ${getName(
                appointment.procedureBaseDefs ?? [],
              )} is now confirmed at ${dayjs(appointment.start)
                .tz(client?.tzid)
                .format(
                  'hh:mm - MM/DD/YYYY',
                )}. Click the button below to view your appointment.`,
            },
          ],
          action: {
            text: 'View your appointment',
            url: `${sendgrid.webOrigin}/appointments/appointment-${appointment.id}`,
          },
        }),
      });
    } catch (err) {
      console.log(err);
    }
  }
  if (
    marketplaceUser.phone &&
    marketplaceUser.phoneConfirmed &&
    marketplaceId &&
    twilio?.appointmentsEnabled
  ) {
    const messagingServiceSid = await getMarketplaceMessagingServiceSid({
      sqldb,
      marketplaceId,
    });
    try {
      sendText({
        to: marketplaceUser.phone,
        body: `Your appointment with ${name} is confirmed for ${dayjs(
          appointment.start,
        )
          .tz(client?.tzid)
          .format('hh:mm a - MM/DD/YYYY')} !`,
        messagingServiceSid,
      });
    } catch (err) {
      console.log(err);
    }
  }
}
