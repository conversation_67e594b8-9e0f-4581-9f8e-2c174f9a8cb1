import { getConfig } from '../../config';
import { ApolloError } from 'apollo-server-express';
import {
  getMarketplaceVerificationServiceSid,
  getTwilioClient,
} from './integration';
import { SqlDbSource } from '../../datasources';
import updateMarketplaceUser from '../marketplace/update-marketplace-user';

interface SendTextParams {
  to: string;
  body: string;
  messagingServiceSid?: string;
}

export default async function sendText({
  to,
  body,
  messagingServiceSid,
}: SendTextParams) {
  const client = getTwilioClient();

  if (!client) {
    return;
  }

  const config = getConfig();

  try {
    return await client.messages.create({
      to,
      body,
      messagingServiceSid: messagingServiceSid ?? config.twilio.serviceSid,
    });
  } catch (e) {
    console.error('Error sending text', e);
  }
}

interface RequestConfirmConsumerPhoneParams {
  to: string;
  sqldb: SqlDbSource;
  marketplaceId: number;
}

export async function requestConfirmConsumerPhone({
  to,
  sqldb,
  marketplaceId,
}: RequestConfirmConsumerPhoneParams) {
  const client = getTwilioClient();

  if (!client) {
    throw new ApolloError(
      'Twilio configuration is incomplete',
      'request-verification:twilio',
    );
  }

  // const marketplace = await sqldb.marketplace(marketplaceId);

  // const phoneInUse = Boolean(
  //   await MarketplaceUser.query(sqldb.knex).findOne({
  //     phone: to,
  //     phoneConfirmed: true,
  //     groupId: marketplace?.groupId,
  //   }),
  // );

  // if (phoneInUse) {
  //   throw new ApolloError(
  //     'The phone number is already confirmed with another account',
  //     'create-marketplace-user:phone',
  //   );
  // }

  const sid = await getMarketplaceVerificationServiceSid({
    sqldb,
    marketplaceId,
  });

  const response = await client.verify.v2.services(sid).verifications.create({
    to,
    channel: 'sms',
  });

  // return true;
  return response.status === 'pending';
}

interface ConfirmConsumerPhoneParams {
  to: string;
  code: string;
  sqldb: SqlDbSource;
  marketplaceId: number;
  marketplaceUserId: number;
}

export async function confirmConsumerPhone({
  to,
  code,
  sqldb,
  marketplaceId,
  marketplaceUserId,
}: ConfirmConsumerPhoneParams) {
  const client = getTwilioClient();

  if (!client) {
    throw new ApolloError(
      'Twilio is not completely configured',
      'check-verification:twilio',
    );
  }

  // const marketplace = await sqldb.marketplace(marketplaceId);

  // const phoneInUse = Boolean(
  //   await MarketplaceUser.query(sqldb.knex).findOne({
  //     phone: to,
  //     phoneConfirmed: true,
  //     groupId: marketplace?.groupId,
  //   }),
  // );

  // if (phoneInUse) {
  //   throw new ApolloError(
  //     'The phone number is already confirmed with another account',
  //     'create-marketplace-user:phone',
  //   );
  // }

  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'confirm-consumer-phone:marketplace',
    );
  }

  const marketplaceUser = await sqldb.marketplaceUser(marketplaceUserId);

  if (marketplaceUser?.groupId !== marketplace.groupId) {
    throw new ApolloError(
      'Invalid marketplace user',
      'confirm-consumer-phone:marketplace-user',
    );
  }

  const sid = await getMarketplaceVerificationServiceSid({
    sqldb,
    marketplaceId,
  });

  const response = await client.verify.v2
    .services(sid)
    .verificationChecks.create({ to, code });

  const result = response.status === 'approved';

  if (result) {
    await updateMarketplaceUser({
      sqldb,
      id: marketplaceUser.id,
      phoneConfirmed: true,
    });
  }

  return result;
}
