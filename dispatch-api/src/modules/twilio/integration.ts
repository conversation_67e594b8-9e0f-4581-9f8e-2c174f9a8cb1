import { ApolloError } from 'apollo-server-express';
import { pick } from 'lodash';
import { PartialModelObject } from 'objection';
import { SqlDbSource } from '../../datasources';
import {
  SegmentIntegration,
  SendgridIntegration,
  TwilioIntegration,
} from './sqldb';
import twilio from 'twilio';
import { getConfig } from '../../config';
import { createEncryptedKey } from '../common/encryption';

export const attentiveApiOrigin = 'https://api.attentivemobile.com/v1';

type UpsertSendgridIntegrationParams = {
  sqldb: SqlDbSource;
  marketplaceId: number;
  apiKey?: string;
  domain?: string;
  webOrigin?: string;
  enabled?: boolean;
};

export function getTwilioClient() {
  const config = getConfig();

  if (!config.twilio.sid || !config.twilio.token || !config.twilio.serviceSid) {
    return null;
  }

  return twilio(config.twilio.sid, config.twilio.token);
}

export async function upsertSendgridIntegration(
  input: UpsertSendgridIntegrationParams,
) {
  const { apiKey, marketplaceId, sqldb } = input;

  const params: PartialModelObject<SendgridIntegration> = pick(input, [
    'enabled',
    'domain',
    'webOrigin',
  ]);

  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'update-sendgrid-integration:marketplace',
    );
  }

  const integration = await marketplace.$relatedQuery('sendgrid', sqldb.knex);

  if (params.enabled && !apiKey && !integration?.encryptedApiKey) {
    throw new ApolloError(
      'Sendgrid cannot be enabled without an API Key',
      'update-sendgrid-integration:api-key',
    );
  }

  if (apiKey) {
    const encryptedKey = await createEncryptedKey({
      sqldb,
      value: apiKey,
      tag: 'sendgrid',
      description: apiKey.slice(-4),
    });

    params.apiKeyId = encryptedKey.id;

    if (integration?.apiKeyId) {
      // delete the old key
      await integration.$relatedQuery('encryptedApiKey', sqldb.knex).delete();
    }
  }

  if (integration?.id) {
    return SendgridIntegration.query(sqldb.knex).updateAndFetchById(
      integration.id,
      {
        ...params,
      },
    );
  }

  params.enabled = params.enabled ?? false;
  params.domain = params.domain ?? '';
  params.webOrigin = params.webOrigin ?? '';

  return SendgridIntegration.query(sqldb.knex).insert({
    marketplaceId,
    ...params,
  });
}

function formatServiceName(name: string) {
  return name.replace(/[^a-zA-Z0-9 \-_]/g, '').slice(0, 30);
}

async function getTwilioIntegration(sqldb: SqlDbSource, marketplaceId: number) {
  const integration =
    await sqldb.twilioIntegrationForMarketplace(marketplaceId);

  if (!integration) {
    return await TwilioIntegration.query(sqldb.knex).insertAndFetch({
      marketplaceId,
    });
  }

  return integration;
}

interface UpsertTwilioIntegrationParams {
  sqldb: SqlDbSource;
  marketplaceId?: number;
  appointmentsEnabled?: boolean;
  appointmentRequestsEnabled?: boolean;
  // clientDayReminder?: boolean;
}

export async function upsertTwilioIntegration(
  input: UpsertTwilioIntegrationParams,
) {
  const {
    marketplaceId,
    appointmentRequestsEnabled,
    appointmentsEnabled,
    // clientDayReminder,
    sqldb,
  } = input;

  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'update-twilio-integration:marketplace',
    );
  }

  const exists = await marketplace.$relatedQuery('twilio', sqldb.knex);

  if (exists?.id) {
    return TwilioIntegration.query(sqldb.knex).updateAndFetchById(exists.id, {
      appointmentRequestsEnabled,
      appointmentsEnabled,
      // clientDayReminder,
    });
  }

  return TwilioIntegration.query(sqldb.knex).insert({
    marketplaceId,
    appointmentRequestsEnabled,
    appointmentsEnabled,
    // clientDayReminder,
  });
}

interface UpsertSegmentIntegrationParams {
  sqldb: SqlDbSource;
  marketplaceId: number;
  writeKey?: string;
  enabled?: boolean;
}

export async function upsertSegmentIntegration(
  input: UpsertSegmentIntegrationParams,
): Promise<SegmentIntegration> {
  const { sqldb, marketplaceId, writeKey, enabled } = input;
  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'update-segment-integration:marketplace',
    );
  }

  const integration =
    await sqldb.segmentIntegrationForMarketplace(marketplaceId);

  if (enabled && !writeKey && !integration?.encryptedWriteKey) {
    throw new ApolloError(
      'Sendgrid cannot be enabled without an API Key',
      'update-sendgrid-integration:api-key',
    );
  }

  let writeKeyId: number | undefined;

  if (writeKey) {
    const encryptedKey = await createEncryptedKey({
      sqldb,
      value: writeKey,
      tag: 'segment',
      description: writeKey.slice(-4),
    });

    writeKeyId = encryptedKey.id;

    if (integration?.writeKeyId) {
      // delete the old key
      await integration.$relatedQuery('encryptedWriteKey', sqldb.knex).delete();
    }
  }

  if (integration?.id) {
    const updateParams = {
      ...(enabled != null ? { enabled } : {}),
      ...(writeKeyId != null ? { writeKeyId } : {}),
    };

    if (Object.entries(updateParams).length > 0) {
      return SegmentIntegration.query(sqldb.knex).updateAndFetchById(
        integration.id,
        updateParams,
      );
    }

    return integration;
  }

  return SegmentIntegration.query(sqldb.knex).insertAndFetch({
    marketplaceId,
    enabled: enabled ?? false,
    ...(writeKeyId != null ? { writeKeyId } : {}),
  });
}

interface GetMarketplaceServiceSid {
  sqldb: SqlDbSource;
  marketplaceId: number;
}

export async function getMarketplaceVerificationServiceSid({
  sqldb,
  marketplaceId,
}: GetMarketplaceServiceSid) {
  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'get-marketplace-service-sid:marketplace',
    );
  }

  const integration = await getTwilioIntegration(sqldb, marketplace.id);

  if (integration.verificationServiceSid) {
    return integration.verificationServiceSid;
  }

  const client = getTwilioClient();

  if (!client) {
    throw new ApolloError(
      'Twilio is not fully configured',
      'get-marketplace-service-sid:twilio',
    );
  }

  const response = await client.verify.v2.services.create({
    friendlyName: formatServiceName(marketplace.name),
  });

  await integration.$query(sqldb.knex).patch({
    verificationServiceSid: response.sid,
  });

  return response.sid;
}

export async function getMarketplaceMessagingServiceSid({
  sqldb,
  marketplaceId,
}: GetMarketplaceServiceSid) {
  const marketplace = await sqldb.marketplace(marketplaceId);

  if (!marketplace) {
    throw new ApolloError(
      'Invalid marketplace',
      'get-marketplace-service-sid:marketplace',
    );
  }

  const integration = await getTwilioIntegration(sqldb, marketplace.id);

  if (integration.messagingServiceSid) {
    return integration.messagingServiceSid;
  }

  const client = getTwilioClient();

  if (!client) {
    throw new ApolloError(
      'Twilio is not fully configured',
      'get-marketplace-service-sid:twilio',
    );
  }

  const response = await client.messaging.v1.services.create({
    friendlyName: formatServiceName(marketplace.name),
  });

  await integration.$query(sqldb.knex).patch({
    messagingServiceSid: response.sid,
  });

  return response.sid;
}
