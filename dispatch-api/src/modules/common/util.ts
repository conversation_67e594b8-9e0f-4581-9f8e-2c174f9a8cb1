import { PhoneNumberFormat, PhoneNumberUtil } from 'google-libphonenumber';
import { Readable } from 'stream';
import dayjs from './dayjs';

const phoneUtil = PhoneNumberUtil.getInstance();

export function fullName(...args: unknown[]): string {
  return args
    .filter((x) => x)
    .join(' ')
    .trim();
}

export function parsePhoneNumber(pn: string, region = 'US'): string | null {
  const phoneNumber = phoneUtil.parse(pn, region);
  return phoneUtil.isValidNumber(phoneNumber)
    ? phoneUtil.format(phoneNumber, PhoneNumberFormat.E164)
    : null;
}

export async function sleep(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export async function readableToBuffer(stream: Readable): Promise<Buffer> {
  const chunks = [];
  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return Buffer.concat(chunks);
}

export function formatTimeRange(start: Date, end: Date, tzid?: string): string {
  if (tzid) {
    return (
      dayjs(start).tz(tzid).format('ddd, MM/DD/YY, h:mm A') +
      (dayjs(end).tz(tzid).isSame(dayjs(start).tz(tzid), 'day')
        ? dayjs(end).tz(tzid).format(' — h:mm A z')
        : dayjs(end).tz(tzid).format(' — ddd, MM/DD/YY, h:mm A z'))
    );
  }
  return [start.toISOString(), end.toISOString()].join(' — ');
}

export function safeJSONParse<T = unknown>(text = ''): T | null {
  let json: T | null = null;
  try {
    json = JSON.parse(text) as T;
  } catch {
    // invalid json
  }
  return json;
}

export const calculateAge = (dob: string | Date | null): number => {
  const birthDate = dayjs(dob);
  const today = dayjs();
  return today.diff(birthDate, 'year');
};
