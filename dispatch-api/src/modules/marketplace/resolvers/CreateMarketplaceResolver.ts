import { ForbiddenError } from 'apollo-server';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { authorize, authorizeRoles } from '../../user/authorize';
import { User } from '../../user/sqldb';
import createMarketplace from '../create-marketplace';
import { Marketplace } from '../sqldb';
import CreateMarketplaceInput from './CreateMarketplaceInput';

@Resolver()
export default class CreateMarketplaceResolver {
  @Mutation(() => Marketplace, { nullable: true })
  async createMarketplace(
    @CurrentUser() user: User,
    @Ctx() { dataSources }: ResolverContext,
    @Arg('input') input: CreateMarketplaceInput,
  ): Promise<Marketplace | null | undefined> {
    const {
      name,
      ownerProfileIds,
      organizationIds,
      requireDispatchApproval,
      requirePractitionerApproval,
      slackWebhookUrl,
      reviewsIoKey,
      reviewsIoStoreId,
      primaryColor,
      logoToken,
    } = input;

    const groupId = input.groupId && intFromID(String(input.groupId));

    if (!authorize(user, 'marketplaces:full')) {
      throw new ForbiddenError('Not authorized (createMarketplace)');
    }

    if (
      ownerProfileIds.length &&
      !authorize(user, ['profiles:full', 'profiles:list'])
    ) {
      throw new ForbiddenError(
        'Not authorized to assign owner role (createMarketplace)',
      );
    }

    if (
      organizationIds.length &&
      !authorize(user, ['organizations:full', 'organizations:list'])
    ) {
      throw new ForbiddenError(
        'Not authorized to add organization (createMarketplace)',
      );
    }

    const ownerProfileIdsSet = new Set(ownerProfileIds.map(intFromID));
    const organizationIdsSet = new Set(organizationIds.map(intFromID));

    const userProfiles = (user.profiles ?? []).filter((profile) =>
      authorizeRoles(profile.roles ?? [], 'marketplaces:full'),
    );

    if (!userProfiles.some(({ id }) => ownerProfileIdsSet.has(id))) {
      ownerProfileIdsSet.add(userProfiles[0].id);
    }

    return createMarketplace({
      sqldb: dataSources.sqldb,
      marketplace: {
        name,
        groupId,
        requireDispatchApproval,
        requirePractitionerApproval,
        slackWebhookUrl,
        reviewsIoKey,
        reviewsIoStoreId,
        primaryColor,
      },
      ownerProfileIds: [...ownerProfileIdsSet] as number[],
      organizationIds: [...organizationIdsSet] as number[],
      logoToken,
      userId: user.id,
    });
  }
}
