import { ApolloError, ForbiddenError } from 'apollo-server';
import { omit } from 'lodash';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { updateClientProfile } from '../../client-profile/sqldb/queries';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { parsePhoneNumber } from '../../common/util';
import { authorize } from '../../user/authorize';
import { User } from '../../user/sqldb';
import { authorizeMarketplaceUser } from '../common';
import MarketplaceUser from '../sqldb/MarketplaceUser';
import { getMarketplaceUser } from '../sqldb/queries';
import updateMarketplaceUser from '../update-marketplace-user';
import UpdateMarketplaceUserInput from './UpdateMarketplaceUserInput';

@Resolver()
export default class UpdateMarketplaceUserResolver {
  @Mutation(() => MarketplaceUser, { nullable: true })
  async updateMarketplaceUser(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: UpdateMarketplaceUserInput,
  ): Promise<MarketplaceUser | null> {
    const marketplaceUser = await getMarketplaceUser(sqldb.knex, {
      id: intFromID(input.id) as number,
    });

    if (!marketplaceUser) {
      throw new ApolloError(
        'Invalid marketplaceUser id',
        'update-marketplace-user:id',
      );
    }

    if (
      !(await authorizeMarketplaceUser(user, marketplaceUser.groupId, {
        sqldb,
        full: true,
      }))
    ) {
      throw new ForbiddenError('Not authorized (updateMarketplaceUser)');
    }

    if (input.emailConfirmed != null || input.phoneConfirmed != null) {
      if (!authorize(user, 'users:full')) {
        throw new ForbiddenError('Not authorized (updateMarketplaceUser)');
      }
    }

    const {
      clientProfile,
      membership,
      primaryInstrumentId,
      phoneOptIn,
      emailOptIn,
      emailConfirmed,
      phoneConfirmed,
    } = input;

    if (clientProfile) {
      const params = omit(clientProfile, ['id']);

      if (clientProfile.phone) {
        const phone = parsePhoneNumber(clientProfile.phone);

        if (!phone) {
          throw new ApolloError(
            'Invalid phone number',
            'update-marketplace-user:phone',
          );
        }

        params.phone = phone;
      }

      await updateClientProfile(sqldb.knex, {
        id: marketplaceUser.clientProfileId,
        params,
      });
    }

    if (
      membership != null ||
      primaryInstrumentId ||
      phoneOptIn !== marketplaceUser.phoneOptIn ||
      emailOptIn !== marketplaceUser.emailOptIn ||
      emailConfirmed !== marketplaceUser.emailConfirmed ||
      phoneConfirmed !== marketplaceUser.phoneConfirmed
    ) {
      if (primaryInstrumentId) {
        const instrument = await sqldb.paymentInstrument(
          intFromID(primaryInstrumentId),
        );

        if (!instrument) {
          throw new ApolloError(
            'Invalid instrument id',
            'update-marketplace-user:primary-instrument-id',
          );
        }

        if (instrument.marketplaceUserId !== marketplaceUser.id) {
          throw new ApolloError(
            'Primary instrument id must be an instrument belonging to marketplace user',
            'update-marketplace-user:primary-instrument-id',
          );
        }
      }

      return updateMarketplaceUser({
        sqldb,
        id: marketplaceUser.id,
        membership,
        primaryInstrumentId: intFromID(primaryInstrumentId),
        phoneOptIn,
        emailOptIn,
        emailConfirmed,
        phoneConfirmed,
      });
    }

    return (
      (await getMarketplaceUser(sqldb.knex, {
        id: marketplaceUser.id,
      })) ?? null
    );
  }
}
