import { Field, ID, InputType } from 'type-graphql';
import ClientProfileInput from '../../client-profile/resolvers/ClientProfileInput';
import { IsNumberID } from '../../common/type-graphql';

@InputType()
export default class UpdateMarketplaceUserInput {
  @Field(() => ID)
  @IsNumberID()
  id!: string;

  @Field(() => ClientProfileInput, { nullable: true })
  clientProfile?: ClientProfileInput;

  @Field(() => String, { nullable: true })
  membership?: string;

  @Field(() => ID, { nullable: true })
  primaryInstrumentId?: string;

  @Field(() => Boolean, { nullable: true })
  phoneOptIn?: boolean;

  @Field(() => Boolean, { nullable: true })
  emailOptIn?: boolean;

  @Field(() => String, { nullable: true })
  phone?: string;

  @Field(() => String, { nullable: true })
  email?: string;

  @Field(() => Boolean, { nullable: true })
  emailConfirmed?: boolean;

  @Field(() => Boolean, { nullable: true })
  phoneConfirmed?: boolean;
}
