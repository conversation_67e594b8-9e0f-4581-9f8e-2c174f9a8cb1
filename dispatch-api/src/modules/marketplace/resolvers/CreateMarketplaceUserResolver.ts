import { ForbiddenError } from 'apollo-server';
import { pick } from 'lodash';
import { Arg, Ctx, Mutation, Resolver } from 'type-graphql';
import { ResolverContext } from '../../../api/context';
import { CurrentUser, intFromID } from '../../common/type-graphql';
import { User } from '../../user/sqldb';
import { authorizeMarketplaceUser } from '../common';
import { createMarketplaceUser } from '../create-marketplace-user';
import MarketplaceUser from '../sqldb/MarketplaceUser';
import CreateMarketplaceUserInput from './CreateMarketplaceUserInput';
import { authorize } from '../../user/authorize';

@Resolver()
export default class CreateMarketplaceUserResolver {
  @Mutation(() => MarketplaceUser, { nullable: true })
  async createMarketplaceUser(
    @CurrentUser() user: User,
    @Ctx() { dataSources: { sqldb } }: ResolverContext,
    @Arg('input') input: CreateMarketplaceUserInput,
  ): Promise<MarketplaceUser | null> {
    const groupId = intFromID(input.groupId) as number;

    if (
      !(await authorizeMarketplaceUser(user, groupId, {
        sqldb,
        full: true,
      }))
    ) {
      throw new ForbiddenError('Not authorized (createMarketplaceUser)');
    }

    const {
      email,
      phone,
      emailConfirmed,
      phoneConfirmed,
      phoneOptIn,
      emailOptIn,
    } = input;

    if (emailConfirmed != null || phoneConfirmed != null) {
      if (!authorize(user, 'users:full')) {
        throw new ForbiddenError('Not authorized (createMarketplaceUser)');
      }
    }

    return createMarketplaceUser({
      sqldb,
      marketplaceUser: {
        email,
        phone,
        emailConfirmed,
        phoneConfirmed,
        phoneOptIn,
        emailOptIn,
        groupId,
      },
      clientProfile: pick(input.clientProfile, [
        'email',
        'givenName',
        'familyName',
        'phone',
        'address',
        'dob',
        'tzid',
      ]),
    });
  }
}
