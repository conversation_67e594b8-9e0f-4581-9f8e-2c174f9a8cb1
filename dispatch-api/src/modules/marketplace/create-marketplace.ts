import { ApolloError } from 'apollo-server';
import { find } from 'lodash';
import { SqlDbSource } from '../../datasources';
import { createEncryptedKey } from '../common/encryption';
import { copyImageAsset } from '../procedure/common';
import { rolesFromSpecification } from '../role/specification';
import { addOrganization } from './add-organization';
import {
  defaultMarketplaceRoles,
  MARKETPLACE_OWNER_ROLE,
} from './default-roles';
import { Marketplace } from './sqldb';
import { MarketplaceFields } from './sqldb/types';

interface CreateMarketplaceParams {
  sqldb: SqlDbSource;
  marketplace: MarketplaceFields;
  ownerProfileIds?: number[];
  organizationIds?: number[];
  logoToken?: string;
  userId?: number;
}

export default async function createMarketplace(
  params: CreateMarketplaceParams,
): Promise<Marketplace | null | undefined> {
  const {
    sqldb,
    marketplace,
    ownerProfileIds = [],
    organizationIds = [],
    logoToken,
    userId,
  } = params;

  const {
    name,
    groupId,
    requireDispatchApproval = true,
    requirePractitionerApproval = false,
    slackWebhookUrl = '',
    reviewsIoKey = '',
    reviewsIoStoreId = '',
    feeProfileFixed = 0,
    feeProfileBasisPoints = 0,
    primaryColor,
  } = marketplace;

  if (groupId && !(await sqldb.marketplaceGroup(groupId))) {
    throw new Error('Invalid marketplace group (createMarketplace)');
  }

  const group = groupId
    ? { id: groupId }
    : { label: `${name} (default group)` };

  try {
    const roles = await rolesFromSpecification({
      sqldb,
      specification: defaultMarketplaceRoles,
    });

    const ownerRole = find(roles, { name: MARKETPLACE_OWNER_ROLE });

    if (!ownerRole) {
      throw new Error(`Missing system role '${MARKETPLACE_OWNER_ROLE}'`);
    }

    ownerRole.profiles = ownerProfileIds.map((id) => ({ id }));

    let reviewsIoKeyId: number | undefined;

    if (reviewsIoKey) {
      const encryptedKey = await createEncryptedKey({
        sqldb,
        value: reviewsIoKey,
        tag: 'reviews_io',
        description: reviewsIoKey.slice(-4),
      });

      reviewsIoKeyId = encryptedKey.id;
    }

    let logo: string | undefined;
    if (logoToken && userId) {
      logo = await copyImageAsset(logoToken, userId);
    }

    const marketplace = await sqldb.knex.transaction(async (trx) =>
      Marketplace.query(trx).insertGraph(
        {
          name,
          roles,
          group,
          requireDispatchApproval,
          requirePractitionerApproval,
          slackWebhookUrl,
          feeProfileFixed,
          feeProfileBasisPoints,
          reviewsIoStoreId,
          primaryColor,
          ...(logo ? { logo } : {}),
          ...(reviewsIoKeyId ? { reviewsIoKeyId } : {}),
        },
        {
          relate: ['roles.profiles', 'group'],
        },
      ),
    );

    for (const organizationId of organizationIds) {
      try {
        await addOrganization({
          sqldb,
          marketplaceId: marketplace.id,
          organizationId,
        });
      } catch (err) {
        console.log(err);
      }
    }

    return sqldb.marketplace(marketplace.id);
  } catch (err) {
    console.log(err);
    throw new ApolloError(
      'Error creating the marketplace record',
      'marketplace:create',
    );
  }
}
