import { ApolloError } from 'apollo-server';
import { SqlDbSource } from '../../datasources';
import { createClientProfile } from '../client-profile/create-client-profile';
import { ClientProfileFields } from '../client-profile/sqldb/types';
import MarketplaceUser from './sqldb/MarketplaceUser';
import { getMarketplaceUser } from './sqldb/queries';
import { MarketplaceUserFields } from './sqldb/types';
import { parsePhoneNumber } from '../common/util';

interface CreateMarketplaceUserParams {
  sqldb: SqlDbSource;
  marketplaceUser: MarketplaceUserFields;
  clientProfile: ClientProfileFields;
}

export async function createMarketplaceUser(
  params: CreateMarketplaceUserParams,
): Promise<MarketplaceUser> {
  const { sqldb, marketplaceUser, clientProfile } = params;
  const {
    groupId,
    email,
    emailConfirmed,
    phoneConfirmed,
    phoneOptIn,
    emailOptIn,
  } = marketplaceUser;

  const { id: clientProfileId } = await createClientProfile({
    sqldb,
    clientProfile,
  });

  const phone = clientProfile.phone && parsePhoneNumber(clientProfile.phone);

  if (!phone) {
    throw new ApolloError(
      'Invalid phone number',
      'create-client-profile:phone',
    );
  }

  const { id } = await MarketplaceUser.query(sqldb.knex).insert({
    groupId,
    clientProfileId,
    email,
    emailConfirmed,
    phone,
    phoneConfirmed,
    phoneOptIn,
    emailOptIn,
  });

  const marketplaceUserRecord = await getMarketplaceUser(sqldb.knex, { id });

  if (!marketplaceUserRecord) {
    throw new ApolloError(
      'Error creating record',
      'create-marketplace-user:create',
    );
  }

  if (emailConfirmed || phoneConfirmed) {
    // TODO: link client profiles to marketplace user
  }

  return marketplaceUserRecord;
}
