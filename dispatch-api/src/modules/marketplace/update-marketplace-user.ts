import { SqlDbSource } from '../../datasources';
import MarketplaceUser from './sqldb/MarketplaceUser';

interface UpdateMarketplaceUserParams {
  sqldb: SqlDbSource;
  id: number;
  email?: string;
  phone?: string;
  membership?: string;
  primaryInstrumentId?: number;
  phoneOptIn?: boolean;
  emailOptIn?: boolean;
  emailConfirmed?: boolean;
  phoneConfirmed?: boolean;
}

export default async function updateMarketplaceUser(
  params: UpdateMarketplaceUserParams,
): Promise<MarketplaceUser> {
  const {
    sqldb,
    id,
    membership,
    primaryInstrumentId,
    phoneOptIn,
    emailOptIn,
    email,
    phone,
    emailConfirmed,
    phoneConfirmed,
  } = params;

  const result = await MarketplaceUser.query(sqldb.knex).patchAndFetchById(id, {
    membership,
    primaryInstrumentId,
    phoneOptIn,
    emailOptIn,
    email,
    phone,
    emailConfirmed: emailConfirmed ?? (email ? false : undefined),
    phoneConfirmed: phoneConfirmed ?? (phone ? false : undefined),
  });

  if (emailConfirmed || phoneConfirmed) {
    // TODO: link client profiles to marketplace user
  }

  return result;
}
