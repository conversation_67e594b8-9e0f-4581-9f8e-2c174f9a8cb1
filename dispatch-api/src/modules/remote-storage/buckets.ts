import { getConfig } from '../../config';

export interface RemoteStorageObjectPath {
  bucket: string;
  key: string;
}

interface GetUploadsObjectPathParams {
  token: string;
  userId: number;
}

export function getUploadedObjectPath({
  token,
  userId,
}: GetUploadsObjectPathParams): RemoteStorageObjectPath {
  return {
    bucket: getConfig().remoteStorage.uploadsBucket,
    key: `user/${userId}/${token}`,
  };
}

interface GetDocumentsObjectPathParams {
  filepath: string;
  emrInstanceId: number;
}

export function getEmrDocumentObjectPath({
  filepath,
  emrInstanceId,
}: GetDocumentsObjectPathParams): RemoteStorageObjectPath {
  return {
    bucket: getConfig().remoteStorage.documentsBucket,
    key: `emr-instance/${emrInstanceId}/${filepath}`,
  };
}

interface GetFormsObjectPathParams {
  token: string;
  patientId: number;
}

export function getPatientDocumentObjectPath({
  token,
  patientId,
}: GetFormsObjectPathParams): RemoteStorageObjectPath {
  return {
    bucket: getConfig().remoteStorage.documentsBucket,
    key: `patient/${patientId}/${token}`,
  };
}

export function getAssetObjectPath(token: string): RemoteStorageObjectPath {
  return {
    bucket: getConfig().remoteStorage.assetsBucket,
    key: `public-assets/${token}`,
  };
}

interface GetReportObjectPathParams {
  organizationId: number;
  filename: string;
}

export function getReportObjectPath({
  organizationId,
  filename,
}: GetReportObjectPathParams): RemoteStorageObjectPath {
  return {
    bucket: getConfig().remoteStorage.documentsBucket,
    key: `reports/organization/${organizationId}/${filename}`,
  };
}
