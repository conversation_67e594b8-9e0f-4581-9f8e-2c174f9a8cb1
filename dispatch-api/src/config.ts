import { <PERSON><PERSON>anager } from '@aws-sdk/client-secrets-manager';
import { SSM } from '@aws-sdk/client-ssm';
import { ServiceAccount } from 'firebase-admin';
import { Knex } from 'knex';
import { chunk } from 'lodash';
import { env, envBoolean, envJson } from './modules/common/env';
import { safeJSONParse } from './modules/common/util';

require('dotenv').config();

// map remote config to local environment variables
const ssmParams: { [key: string]: string } = {
  'api-session-secret': 'API_SESSION_SECRET',
  'api-session-max-age': 'API_SESSION_MAX_AGE',
  'api-cors-origin': 'API_CORS_ORIGIN',
  'auth-secret': 'AUTH_SECRET',
  'auth-refresh-token-expires-in': 'AUTH_REFRESH_TOKEN_EXPIRES_IN',
  'sendgrid-api-key': 'SENDGRID_API_KEY',
  'web-url-origin': 'WEB_URL_ORIGIN',
  'invitation-salt': 'INVITATION_SALT',
  'google-maps-api-key': 'GOOGLE_MAPS_API_KEY',
  'firebase-service-account-json': 'FIREBASE_SERVICE_ACCOUNT_JSON',
  'api-playground': 'API_PLAYGROUND',
  'finix-username': 'FINIX_USERNAME',
  'finix-password': 'FINIX_PASSWORD',
  'finix-webhook-auth': 'FINIX_WEBHOOK_AUTH',
  'finix-test': 'FINIX_TEST',
  'finix-merchant-identity': 'FINIX_MERCHANT_IDENTITY',
  'twilio-account-sid': 'TWILIO_ACCOUNT_SID',
  'twilio-service-sid': 'TWILIO_SERVICE_SID',
  'twilio-token': 'TWILIO_AUTH_TOKEN',
  'encryption-key-256': 'ENCRYPTION_KEY_256',
};

export interface Config {
  prod: boolean;
  sqldb: Knex.Config;
  api: {
    port: number;
    sessionSecret: string;
    sessionMaxAge: number;
    origin: string;
    corsOrigin: string[];
    playground: boolean;
  };
  auth: {
    secret: string;
    refreshTokenExpiresIn: string;
    cookieName: string;
  };
  email: {
    sendgridApiKey: string;
    webUrlOrigin: string;
  };
  invitation: {
    salt: string;
  };
  logger: {
    slackUrl?: string;
  };
  maps: {
    googleKey: string;
  };
  firebase: {
    serviceAccount?: ServiceAccount;
  };
  remoteStorage: {
    uploadsBucket: string;
    documentsBucket: string;
    assetsBucket: string;
  };
  finix: {
    username?: string;
    password?: string;
    webhookAuth?: string;
    test: boolean;
    merchantIdentity?: string;
  };
  twilio: {
    sid?: string;
    serviceSid?: string;
    token?: string;
  };
  apollo: {
    key?: string;
    graphRef?: string;
  };
  crypto: {
    encryptionKey256: string;
  };
}

let configData: Config | undefined;

export function getConfig(): Config {
  return configData as Config;
}

export async function loadConfig(): Promise<Config> {
  if (!configData) {
    await loadStoredConfig();
    configData = buildConfig();
  }

  return configData;
}

export function buildConfig(): Config {
  const client = env('SQLDB_CLIENT');
  const ssl = process.env.SQLDB_SSL == null || envBoolean('SQLDB_SSL');
  const prod = process.env.NODE_ENV === 'production';

  const config: Config = {
    prod,
    sqldb: {
      client,
      connection:
        client === 'sqlite3'
          ? {
              filename: env('SQLDB_FILENAME'),
            }
          : process.env.SQLDB_CONNECTION_STRING || {
              host: env('SQLDB_HOST'),
              database: env('SQLDB_DATABASE'),
              user: env('SQLDB_USER'),
              password: env('SQLDB_PASSWORD'),
              port: +env('SQLDB_PORT'),
              ssl: ssl && {
                rejectUnauthorized: false, // heroku (dev)
              },
            },
      debug: Boolean(process.env.SQLDB_DEBUG),
    },
    api: {
      port: +(process.env.PORT || '4000'),
      sessionSecret: env('API_SESSION_SECRET'),
      sessionMaxAge: 1000 * +(process.env.API_SESSION_MAX_AGE || 60 * 60 * 2),
      origin: env('API_ORIGIN'),
      corsOrigin: env('API_CORS_ORIGIN').split(','),
      playground: envBoolean('API_PLAYGROUND'),
    },
    auth: {
      secret: env('AUTH_SECRET'),
      refreshTokenExpiresIn: env('AUTH_REFRESH_TOKEN_EXPIRES_IN'),
      cookieName: 'sessionid',
    },
    email: {
      sendgridApiKey: env('SENDGRID_API_KEY'),
      webUrlOrigin: env('WEB_URL_ORIGIN'),
    },
    invitation: {
      salt: env('INVITATION_SALT'),
    },
    logger: {
      slackUrl: process.env.SLACK_URL,
    },
    maps: {
      googleKey: env('GOOGLE_MAPS_API_KEY'),
    },
    firebase: {
      serviceAccount: envJson('FIREBASE_SERVICE_ACCOUNT_JSON'),
    },
    remoteStorage: {
      uploadsBucket: env('AWS_S3_UPLOADS_BUCKET'),
      documentsBucket: env('AWS_S3_DOCUMENTS_BUCKET'),
      assetsBucket: env('AWS_S3_ASSETS_BUCKET'),
    },
    finix: {
      username: process.env.FINIX_USERNAME,
      password: process.env.FINIX_PASSWORD,
      webhookAuth: process.env.FINIX_WEBHOOK_AUTH,
      test: envBoolean('FINIX_TEST'),
      merchantIdentity: process.env.FINIX_MERCHANT_IDENTITY,
    },
    twilio: {
      sid: process.env.TWILIO_ACCOUNT_SID,
      serviceSid: process.env.TWILIO_SERVICE_SID,
      token: process.env.TWILIO_AUTH_TOKEN,
    },
    apollo: {
      key: process.env.APOLLO_KEY,
      graphRef: process.env.APOLLO_GRAPH_REF,
    },
    crypto: {
      encryptionKey256: env('ENCRYPTION_KEY_256'), // base64 encoded
    },
  };

  if (!config.prod) {
    console.log(config);
  }

  return config;
}

const ssm = new SSM();

async function loadStoredConfig() {
  const dbSecretArn = process.env.SQLDB_SECRET_ARN;

  if (!dbSecretArn) {
    return;
  }

  console.log('Loading remote config...');

  // fetch database credentials from secrets manager

  const secretsManager = new SecretsManager();

  const dbParams = await secretsManager.getSecretValue({
    SecretId: dbSecretArn,
  });

  const secrets = safeJSONParse<{ [key: string]: string }>(
    dbParams.SecretString ?? '',
  );

  if (!secrets) {
    throw new Error('Error fetching db secret');
  }

  process.env.SQLDB_CLIENT = 'pg';
  process.env.SQLDB_HOST = secrets.host;
  process.env.SQLDB_DATABASE = secrets.dbname;
  process.env.SQLDB_USER = secrets.username;
  process.env.SQLDB_PASSWORD = secrets.password;
  process.env.SQLDB_PORT = secrets.port;

  // fetch other config values from parameter store

  const iter = chunk(Object.keys(ssmParams), 10);

  for (const names of iter) {
    const response = await ssm.getParameters({
      Names: names,
      WithDecryption: true,
    });

    for (const param of response.Parameters || []) {
      if (param.Name != null && param.Value != null) {
        const key = ssmParams[param.Name];
        if (key && process.env[key] == null) {
          process.env[key] = param.Value;
        }
      }
    }
  }
}
