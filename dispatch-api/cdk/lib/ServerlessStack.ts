import { CfnOutput, Duration, Stack, StackProps } from 'aws-cdk-lib';
import { SubnetType, type Vpc } from 'aws-cdk-lib/aws-ec2';
import { Rule, Schedule } from 'aws-cdk-lib/aws-events';
import { LambdaFunction } from 'aws-cdk-lib/aws-events-targets';
import { Effect, PolicyStatement } from 'aws-cdk-lib/aws-iam';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Bucket } from 'aws-cdk-lib/aws-s3';
import { ISecret } from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';

interface ServerlessStackProps extends StackProps {
  vpc: Vpc;
  domainName: string;
  buckets: Record<string, Bucket>;
  db: {
    secret?: ISecret;
    ssl?: boolean;
  };
}

export class ServerlessStack extends Stack {
  readonly lambdaFunctions: Record<string, NodejsFunction>;

  constructor(scope: Construct, id: string, props: ServerlessStackProps) {
    super(scope, id, props);

    const { vpc, domainName, buckets, db } = props;

    const defaults = {
      runtime: Runtime.NODEJS_16_X,
      timeout: Duration.seconds(300),
      handler: 'handler',
      environment: {
        SQLDB_SECRET_ARN: String(db.secret?.secretFullArn),
        SQLDB_SSL: String(db.ssl),
        AWS_S3_UPLOADS_BUCKET: buckets.uploads.bucketName,
        AWS_S3_DOCUMENTS_BUCKET: buckets.documents.bucketName,
        AWS_S3_ASSETS_BUCKET: buckets.assets.bucketName,
        API_ORIGIN: `https://${domainName}`,
      },
      vpc,
      vpcSubnets: { subnetType: SubnetType.PRIVATE_WITH_EGRESS },
      bundling: {
        esbuildArgs: {
          '--tree-shaking': true,
        },
        externalModules: [
          'mysql2',
          'pg-query-stream',
          'pg-native',
          'mock-aws-s3',
          'nock',
          'oracledb',
          'mysql',
          'better-sqlite3',
          'tedious',
          '@mapbox',
        ],
        // logLevel: LogLevel.DEBUG,
      },
    };

    this.lambdaFunctions = {
      renewMemberships: new NodejsFunction(this, 'RenewMembershipsFunction', {
        ...defaults,
        description: 'Update the state of recurring memberships',
        entry: 'build/lambdas/renew-memberships.js',
        initialPolicy: [
          new PolicyStatement({
            effect: Effect.ALLOW,
            actions: [
              'ssm:GetParameters',
              'secretsmanager:GetSecretValue',
              'kms:Decrypt',
            ],
            resources: ['*'],
          }),
        ],
      }),
      // notifyClients: new NodejsFunction(this, 'NotifyClientsFunction', {
      //   ...defaults,
      //   description: 'Notify clients of upcoming appointments',
      //   entry: 'build/lambdas/notify-clients.js',
      //   initialPolicy: [
      //     new PolicyStatement({
      //       effect: Effect.ALLOW,
      //       actions: [
      //         'ssm:GetParameters',
      //         'secretsmanager:GetSecretValue',
      //         'kms:Decrypt',
      //       ],
      //       resources: ['*'],
      //     }),
      //   ],
      // }),
    };

    db.secret?.grantRead(this.lambdaFunctions.renewMemberships);

    new Rule(this, 'RenewMembershipsFunctionScheduleRule', {
      schedule: Schedule.rate(Duration.minutes(60)),
      targets: [new LambdaFunction(this.lambdaFunctions.renewMemberships)],
    });

    // db.secret?.grantRead(this.lambdaFunctions.notifyClients);

    // new Rule(this, 'NotifyClientsFunctionScheduleRule', {
    //   schedule: Schedule.cron({ hour: '12', minute: '0' }),
    //   targets: [new LambdaFunction(this.lambdaFunctions.notifyClients)],
    // });

    new CfnOutput(this, 'RenewMembershipsFunctionName', {
      value: this.lambdaFunctions.renewMemberships.functionName,
      description: '`renewMemberships` lambda function',
      exportName: `${this.region}:${this.account}:${this.stackName}:renewMemberships-function-name`,
    });

    // new CfnOutput(this, 'NotifyClientsFunctionName', {
    //   value: this.lambdaFunctions.notifyClients.functionName,
    //   description: '`notifyClients` lambda function',
    //   exportName: `${this.region}:${this.account}:${this.stackName}:notifyClients-function-name`,
    // });
  }
}
