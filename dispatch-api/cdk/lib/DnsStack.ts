import { Stack, StackProps } from 'aws-cdk-lib';
import {
  Certificate,
  CertificateValidation,
  DnsValidatedCertificate,
} from 'aws-cdk-lib/aws-certificatemanager';
import { HostedZone } from 'aws-cdk-lib/aws-route53';
import { Construct } from 'constructs';

interface DnsStackProps extends StackProps {
  domainName: string;
}

export class DnsStack extends Stack {
  readonly hostedZone: HostedZone;
  readonly certificate: Certificate;

  constructor(scope: Construct, id: string, props: DnsStackProps) {
    super(scope, id, props);

    const { domainName } = props;

    this.hostedZone = new HostedZone(this, 'HostedZone', {
      zoneName: domainName,
    });

    // this.certificate = new DnsValidatedCertificate(this, 'Certificate', {
    //   hostedZone: this.hostedZone,
    //   domainName,
    // });

    // use the following after updating to cdk 2.x

    this.certificate = new Certificate(this, 'Certificate', {
      domainName,
      validation: CertificateValidation.fromDns(this.hostedZone),
    });
  }
}
