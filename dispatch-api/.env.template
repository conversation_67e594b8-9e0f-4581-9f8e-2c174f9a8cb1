
PORT="4000"
API_SESSION_SECRET="********************************"
API_SESSION_MAX_AGE="7200"
API_CORS_ORIGIN="http://localhost:3000"
API_ORIGIN="https://localhost:4000"
API_PLAYGROUND=1

AUTH_SECRET="********************************"
AUTH_REFRESH_TOKEN_EXPIRES_IN="90d"

# APOLLO_KEY=service:dispatch-staging:***
# APOLLO_GRAPH_REF=dispatch-staging@current
# APOLLO_SCHEMA_REPORTING=true

WEB_URL_ORIGIN="http://localhost:3000"
INVITATION_SALT="$2b$10$16htvNFdL8UtIf99Vf7ete"
ENCRYPTION_KEY_256="h4hiY6/aL2VnFPeL70fCBrVP7bJnoLkqD5/8cZLT04U="

GOOGLE_MAPS_API_KEY=
SENDGRID_API_KEY="*********************************************************************"
# SLACK_URL="*********************************************************************************"
# FIREBASE_SERVICE_ACCOUNT_JSON=

AWS_ACCESS_KEY_ID="AKIAYL2CQ2DZXMFPT2OA"
AWS_SECRET_ACCESS_KEY=
AWS_REGION="us-west-2"
AWS_S3_UPLOADS_BUCKET="localapistorage07090786-uploadsbucket5e5e9b64-12n0waq4seisu"
AWS_S3_DOCUMENTS_BUCKET="localapistorage07090786-documentsbucket9ec9deb9-g5vjda9ux9bx"
AWS_S3_FORMS_BUCKET="localapistorage07090786-formsbucket9c3f8e46-1hmc1xxfor4uf"

FINIX_USERNAME="USaiptKhA74YJGn5Cv7r69Vf"
FINIX_PASSWORD="9d928137-ffd2-4e26-9124-9687aff4eba6"
FINIX_WEBHOOK_AUTH="nomad-dev:NGLgYnm2X3gNcWjQpsAG1QwUyVcYG7hU"
FINIX_MERCHANT_IDENTITY="IDfGh52cHRUqL2gnHfbTxYPS"
FINIX_TEST=1

TWILIO_ACCOUNT_SID=
TWILIO_SERVICE_SID=
TWILIO_AUTH_TOKEN=

SQLDB_CLIENT="pg"
SQLDB_HOST="localhost"
SQLDB_DATABASE="nmd"
SQLDB_USER="postgres-local"
SQLDB_PASSWORD="BO=9ysI^c=unK1qoaUWWmDb.PEQPmK"
SQLDB_PORT="4511"
SQLDB_SSL="false"
SQLDB_DEBUG=1

# SQLDB_CLIENT="sqlite3"
# SQLDB_FILENAME="dev.sqlite"
# SQLDB_CLIENT="pg"
# SQLDB_CONNECTION_STRING="postgres://localhost:5432/nomadmd-dev"