/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.table('client_profiles', (table) => {
    table.string('sex_assigned_at_birth').nullable();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table('client_profiles', (table) => {
    table.dropColumn('sex_assigned_at_birth');
  });
};
