/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema
    .table('organizations', (table) => {
      table.boolean('enable_practitioner_sms').defaultTo(false);
    })
    .table('profiles', (table) => {
      table.boolean('allow_sms_notifications').defaultTo(true);
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema
    .table('organizations', (table) => {
      table.dropColumn('enable_practitioner_sms');
    })
    .table('profiles', (table) => {
      table.dropColumn('allow_sms_notifications');
    });
};
