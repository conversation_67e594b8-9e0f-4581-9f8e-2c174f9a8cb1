const helpers = require('./helpers/permissions');

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema
    .createTable('reports', (table) => {
      table.increments('id').primary();
      table.string('type').notNullable();
      table.string('filename').nullable();
      table.string('filepath').nullable();
      table.string('status').notNullable().defaultTo('processing');
      table
        .integer('organization_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('organizations')
        .onUpdate('CASCADE')
        .onDelete('CASCADE');
      table
        .integer('user_id')
        .unsigned()
        .notNullable()
        .references('id')
        .inTable('users')
        .onUpdate('CASCADE')
        .onDelete('CASCADE');
      table.datetime('created_at').notNullable().defaultTo(knex.fn.now());
      table.datetime('updated_at').notNullable().defaultTo(knex.fn.now());

      table.index(['type']);
      table.index(['status']);
      table.index(['organization_id']);
      table.index(['user_id']);
      table.index(['created_at']);
    })
    .then(() =>
      helpers.addRootPermissions(knex, ['reports:list', 'reports:full']),
    )
    .then(() =>
      helpers.addOrgPermissions(knex, [
        'organization.reports:list',
        'organization.reports:full',
      ]),
    )
    .then(() =>
      helpers.addMarketplacePermissions(knex, [
        'marketplace.reports:list',
        'marketplace.reports:full',
      ]),
    );
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return Promise.resolve()
    .then(() =>
      helpers.removeMarketplacePermissions(knex, [
        'marketplace.reports:list',
        'marketplace.reports:full',
      ]),
    )
    .then(() =>
      helpers.removeOrgPermissions(knex, [
        'organization.reports:list',
        'organization.reports:full',
      ]),
    )
    .then(() =>
      helpers.removeRootPermissions(knex, ['reports:list', 'reports:full']),
    )
    .then(() => knex.schema.dropTableIfExists('reports'));
};
