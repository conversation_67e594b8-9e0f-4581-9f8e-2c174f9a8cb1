/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.table('organizations', (table) => {
    table.boolean('enable_receipt_sending').defaultTo(false);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table('organizations', (table) => {
    table.dropColumn('enable_receipt_sending');
  });
};
