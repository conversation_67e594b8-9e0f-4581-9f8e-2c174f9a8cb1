/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.table('marketplaces', function (table) {
    table.string('logo', 255).nullable();
    table.string('primary_color', 255).nullable();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table('marketplaces', function (table) {
    table.dropColumn('logo');
    table.dropColumn('primary_color');
  });
};
