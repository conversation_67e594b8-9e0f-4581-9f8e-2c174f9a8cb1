{
  "extends": "@tsconfig/node16/tsconfig.json",
  "compilerOptions": {
    /* Visit https://aka.ms/tsconfig.json to read more about this file */
    "sourceMap": true /* Generates corresponding '.map' file. */,
    "outDir": "./build" /* Redirect output structure to th  e directory. */,
    "rootDir": "./src" /* Specify the root directory of input files. Use to control the output directory structure with --outDir. */,
    "noImplicitAny": true /* Raise error on expressions and declarations with an implied 'any' type. */,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true
  },
  "exclude": [
    "cdk",
    "cdk.out",
    "scripts",
    "**/*.test.ts",
    "**/*.spec.ts",
    "**/test-setup.ts"
  ]
}
