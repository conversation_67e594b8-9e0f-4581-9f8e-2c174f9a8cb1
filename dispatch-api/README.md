## Installation

- Create a .env file in the project root directory (copy from .env.template)
- `npm run install`
- `npm run build`
- `npm run migrate`
- `npm run seed`

## Running in dev

### API server

- `npm run dev`
- GraphQL Playground
  - http://localhost:4000/graphql
  - Update the `request.credentials` setting to enable cookies: `{ "request.credentials": "include" }`,
  - Authenticate with seed user:
  ```graphql
  mutation {
    authenticate(
      input: { email: "<EMAIL>", password: "hurryup22" }
    ) {
      accessToken
      refreshToken
      user {
        id
      }
    }
  }
  ```

### Serverless infrastructure

- https://docs.localstack.cloud/getting-started/installation/
- `LOCALSTACK_AUTH_TOKEN=... localstack start`
- `npm run build`
- `cdklocal bootstrap`
- `cdklocal deploy local/ApiServerless local/ApiUser --require-approval never`

## AWS infrastructure

### New environment

- Create an AWS account for the environment
- Create an IAM user for the target account from the AWS console
- Configure an AWS profile for the account (for example 'nmds'):

  `aws configure --profile`

- Create a new keypair named "id_rsa" and save the private key

  `aws ec2 create-key-pair --profile xxx --key-name id_rsa --query 'KeyMaterial' --output text > id_rsa.pem`

  `chmod 400 id_rsa.pem`

### Environment variables

- NOTE: the runtime stack will use version 1 of secure strings
- `aws ssm put-parameter --name "api-session-secret" --value "k&wXyN45+PUACcaR5SnGps5VFXx$-9@g" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "api-session-max-age" --value "7200" --type "String" --profile xxx`
- `aws ssm put-parameter --name "api-cors-origin" --value "https://test.nomadmd.app" --type "String" --profile xxx`
- `aws ssm put-parameter --name "auth-secret" --value "4wXfBEqAxfYNcVTJZVmU#Wa^Ys$tCx3H" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "auth-refresh-token-expires-in" --value "90d" --type "String" --profile xxx`
- `aws ssm put-parameter --name "sendgrid-api-key" --value "*********************************************************************" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "web-url-origin" --value "https://test.nomadmd.app" --type "String" --profile xxx`
- `aws ssm put-parameter --name "invitation-salt" --value "$2b$10$16htvNFdL8UtIf99Vf7ete" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "google-maps-api-key" --value "AIzaSyBy7SF-jZzYi6Vub2H9yFgS7AJJa512345" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "api-playground" --value "1" --type "String" --profile xxx`
- `aws ssm put-parameter --name "finix-username" --value "USaiptKhA74YJGn5Cv7r69Vf" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "finix-password" --value "9d928137-ffd2-4e26-9124-9687aff4eba6" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "finix-webhook-auth" --value "nomad-dev:NGLgYnm2X3gNcWjQpsAG1QwUyVcYG7hU" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "finix-merchant-identity" --value "IDkJoahpQHqqH71RafxVmQm3" --type "String" --profile xxx`
- `aws ssm put-parameter --name "finix-test" --value "1" --type "String" --profile xxx`
- `aws ssm put-parameter --name "twilio-account-sid" --value "MG677fda308847b45460a05aec06f5565b" --type "String" --profile xxx`
- `aws ssm put-parameter --name "twilio-service-sid" --value "AC66b30fb88141ddd630bb4bea9fd5505d" --type "String" --profile xxx`
- `aws ssm put-parameter --name "twilio-token" --value "abc12345" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "apollo-key" --value "service:dispatch-staging:***" --type "SecureString" --profile xxx`
- `aws ssm put-parameter --name "apollo-graph-ref" --value "dispatch-staging@current" --type "String" --profile xxx`
- `aws ssm put-parameter --name "encryption-key-256" --value "MXOEGU82m2WuDGFqP2yyl9auW/C+yVOwfWMTJG6Qr1w=" --type "SecureString" --profile xxx`

- ```
  aws ssm put-parameter \
    --name firebase-service-account-json \
    --type SecureString \
    --value file://local/nomadmd-staging-firebase-adminsdk-y2dob-1d942331ce.json \
    --profile xxx
  ```

### Encrypted keys

- Environment variable `ENCRYPTION_KEY_256` must be a random 256 bit base64 encoded key

  `node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"`

### Deploy infrastructure

- `npm run build`
- `cdk bootstrap --profile xxx`
- `cdk synth --profile xxx`
- `cdk deploy --profile xxx dev/VPC dev/DNS dev/Data dev/Runtime dev/ApiServerless`

### Create/Update DNS records in root account (use CLI)

- Get the hosted zone id in the sub account

  `aws route53 list-hosted-zones --profile xxx`

- List the NS records for the domain in the sub account (for example `test.nomadmd.app`)

  ``aws route53 list-resource-record-sets --hosted-zone-id /hostedzone/Z04899391SDJKIEKALDRM --profile xxx --query 'ResourceRecordSets[?Type==`NS`]'``

- Log in to the AWS console root account, create or update the NS record with the name servers from previous step

- Wait for the certificate to be validated

- See https://github.com/Macca2805/aws-route-53-subdomains

### Access the RDS database

- Determine the bastion host public address:

  `aws ec2 describe-instances --filters Name=tag:Name,Values=BastionHost --query "Reservations[*].Instances[*].[InstanceId,PublicDnsName,PublicIpAddress]" --output table --profile xxx`

- Start bastion host

  `aws ec2 start-instances --instance-ids i-033892b75cdabadda --profile xxx`

- https://aws.amazon.com/premiumsupport/knowledge-center/rds-connect-using-bastion-host-linux/

- Create ssh tunnel

  `ssh -i [keypair] -NL 8886:[rds-endpoint]:5432 ec2-user@[bastion-ip] -v`

  Example:

  `ssh -i id_rsa.pem -NL 8886:dr1if089as5wjj5.cr1jbtg3snbi.us-west-1.rds.amazonaws.com:5432 ec2-user@************ -v`

- Connect

  `psql postgres://apiuser@localhost:8886/nmd`

- Stop bastion host

  `aws ec2 stop-instances --instance-ids i-033892b75cdabadda --profile xxx`
