/**
 * Usage:
 * npx ts-node scripts/test-receipt.ts <transferId>
 */

import 'reflect-metadata';
import { sendReceipt } from '../src/modules/payment/finix/receipt';
import { SqlDbSource } from '../src/datasources';
import { createKnex } from '../src/knex';
import { loadConfig } from '../src/config';

async function run() {
  const transferId = process.argv[2];

  if (!transferId) {
    console.error('Usage: npx ts-node scripts/test-receipt.ts <transferId>');
    process.exit(1);
  }

  console.log(`Testing receipt functionality for transfer: ${transferId}`);

  try {
    const config = await loadConfig();
    const knexInstance = createKnex(config.sqldb);
    const sqldb = new SqlDbSource(knexInstance);

    await sendReceipt({
      sqldb,
      transferId,
    });

    console.log('Receipt test completed successfully');
  } catch (error) {
    console.error('Receipt test failed:', error);
  } finally {
    process.exit(0);
  }
}

run();
