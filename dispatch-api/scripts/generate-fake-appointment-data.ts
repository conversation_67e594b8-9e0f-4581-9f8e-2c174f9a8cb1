#!/usr/bin/env ts-node

import 'reflect-metadata';
import dayjs from 'dayjs';
import { Knex } from 'knex';
import { initialize } from 'objection';
import { loadConfig } from '../src/config';
import { createKnex } from '../src/knex';
import { Appointment } from '../src/modules/appointment/sqldb';
import {
  AppointmentStatus,
  ParticipantType,
  ParticipationStatus,
} from '../src/modules/appointment/sqldb/types';
import { ClientProfile } from '../src/modules/client-profile/sqldb';
import { Marketplace } from '../src/modules/marketplace/sqldb';
import { MarketplaceUser } from '../src/modules/marketplace/sqldb';
import { Organization } from '../src/modules/organization/sqldb';
import { Checkout, CheckoutItem, Payment } from '../src/modules/payment/sqldb';
import {
  CheckoutItemType,
  PaymentStatus,
  PaymentType,
} from '../src/modules/payment/sqldb/types';
import { ProcedureBaseDefinition } from '../src/modules/procedure/sqldb';
import { Profile } from '../src/modules/profile/sqldb';

interface GenerateFakeDataParams {
  organizationId: number;
  appointmentCount?: number;
}

// Fake data arrays
const FIRST_NAMES = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Amanda',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Deborah',
  'Steven',
  'Rachel',
  'Paul',
  'Carolyn',
  'Andrew',
  'Janet',
];

const LAST_NAMES = [
  'Smith',
  'Johnson',
  'Williams',
  'Brown',
  'Jones',
  'Garcia',
  'Miller',
  'Davis',
  'Rodriguez',
  'Martinez',
  'Hernandez',
  'Lopez',
  'Gonzalez',
  'Wilson',
  'Anderson',
  'Thomas',
  'Taylor',
  'Moore',
  'Jackson',
  'Martin',
  'Lee',
  'Perez',
  'Thompson',
  'White',
  'Harris',
  'Sanchez',
  'Clark',
  'Ramirez',
  'Lewis',
  'Robinson',
];

const LOCATIONS = [
  '123 Main St, Anytown, ST 12345',
  '456 Oak Ave, Somewhere, ST 67890',
  '789 Pine Rd, Elsewhere, ST 54321',
  '321 Elm St, Nowhere, ST 98765',
  '654 Maple Dr, Anywhere, ST 13579',
  '987 Cedar Ln, Someplace, ST 24680',
];

const MEMBERSHIP_TYPES = ['Basic', 'Premium', 'VIP', 'Standard', ''];

const PROCEDURE_NAMES = [
  'Consultation',
  'Follow-up',
  'Treatment',
  'Therapy Session',
  'Examination',
  'Procedure A',
  'Procedure B',
  'Wellness Check',
  'Assessment',
  'Screening',
];

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomNumber(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function getRandomDate(): Date {
  const now = dayjs();
  const startOfMonth = now.startOf('month');
  const endOfMonth = now.endOf('month');

  const randomTime = getRandomNumber(
    startOfMonth.valueOf(),
    endOfMonth.valueOf(),
  );

  return new Date(randomTime);
}

function generateRandomEmail(firstName: string, lastName: string): string {
  const domains = [
    'gmail.com',
    'yahoo.com',
    'hotmail.com',
    'outlook.com',
    'example.com',
  ];
  const domain = getRandomElement(domains);
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`;
}

function generateRandomPhone(): string {
  const areaCode = getRandomNumber(200, 999);
  const exchange = getRandomNumber(200, 999);
  const number = getRandomNumber(1000, 9999);
  return `${areaCode}-${exchange}-${number}`;
}

async function createFakeClientProfile(knex: Knex): Promise<number> {
  const firstName = getRandomElement(FIRST_NAMES);
  const lastName = getRandomElement(LAST_NAMES);

  const [clientProfile] = await knex('clientProfiles')
    .insert({
      givenName: firstName,
      familyName: lastName,
      email: generateRandomEmail(firstName, lastName),
      phone: generateRandomPhone(),
      address: getRandomElement(LOCATIONS),
      dob: dayjs()
        .subtract(getRandomNumber(18, 80), 'years')
        .format('YYYY-MM-DD'),
      tzid: 'America/New_York',
      createdAt: knex.fn.now(),
      updatedAt: knex.fn.now(),
    })
    .returning('id');

  return clientProfile.id || clientProfile;
}

async function createFakeMarketplaceUser(
  knex: Knex,
  clientProfileId: number,
  marketplaceGroupId: number,
): Promise<number> {
  const [marketplaceUser] = await knex('marketplaceUsers')
    .insert({
      clientProfileId,
      groupId: marketplaceGroupId,
      membership: getRandomElement(MEMBERSHIP_TYPES),
      createdAt: knex.fn.now(),
      updatedAt: knex.fn.now(),
    })
    .returning('id');

  return marketplaceUser.id || marketplaceUser;
}

async function createFakeCheckout(
  knex: Knex,
  marketplaceUserId: number,
  marketplaceId: number,
): Promise<number> {
  // Create checkout first
  const procedureTotal = getRandomNumber(5000, 50000); // $50-$500 in cents
  const gratuityAmount = getRandomNumber(500, 5000); // $5-$50 in cents
  const travelFee = getRandomNumber(0, 2000); // $0-$20 in cents
  const packageCredit = getRandomNumber(0, 1000); // $0-$10 credit in cents
  const membershipDiscount = getRandomNumber(0, 2000); // $0-$20 discount in cents

  const total =
    procedureTotal +
    gratuityAmount +
    travelFee -
    packageCredit -
    membershipDiscount;
  const paid = Math.random() > 0.2 ? total : getRandomNumber(0, total); // 80% chance fully paid
  const balance = total - paid;

  const [checkout] = await knex('checkouts')
    .insert({
      total,
      paid,
      balance,
      marketplaceUserId,
      marketplaceId,
      createdAt: knex.fn.now(),
      updatedAt: knex.fn.now(),
    })
    .returning('id');

  const checkoutId = checkout.id || checkout;

  // Create checkout items
  const items = [
    {
      checkoutId,
      type: CheckoutItemType.PROCEDURE,
      description: getRandomElement(PROCEDURE_NAMES),
      quantity: 1,
      price: procedureTotal,
      key: 'procedure',
    },
  ];

  if (gratuityAmount > 0) {
    items.push({
      checkoutId,
      type: CheckoutItemType.GRATUITY,
      description: 'Gratuity',
      quantity: 1,
      price: gratuityAmount,
      key: 'gratuity',
    });
  }

  if (travelFee > 0) {
    items.push({
      checkoutId,
      type: CheckoutItemType.TRAVEL_FEE,
      description: 'Travel Fee',
      quantity: 1,
      price: travelFee,
      key: 'travel_fee',
    });
  }

  if (packageCredit > 0) {
    items.push({
      checkoutId,
      type: CheckoutItemType.PACKAGE_CREDIT,
      description: 'Package Credit',
      quantity: 1,
      price: -packageCredit, // Negative for credit
      key: 'package_credit',
    });
  }

  if (membershipDiscount > 0) {
    items.push({
      checkoutId,
      type: CheckoutItemType.DISCOUNT,
      description: 'Membership Discount',
      quantity: 1,
      price: -membershipDiscount, // Negative for discount
      key: 'membership_discount',
    });
  }

  // Add custom line items occasionally
  if (Math.random() > 0.8) {
    items.push({
      checkoutId,
      type: CheckoutItemType.OTHER,
      description: 'Additional Service',
      quantity: 1,
      price: getRandomNumber(1000, 5000),
      key: 'custom',
    });
  }

  await knex('checkoutItems').insert(items);

  // Create payment if checkout is paid
  if (paid > 0) {
    await knex('payments').insert({
      checkoutId,
      type: PaymentType.CREDIT,
      status: PaymentStatus.ACCEPTED,
      description: 'Credit Card Payment',
      amount: paid,
      amountRequested: paid,
      fee: Math.floor(paid * 0.029), // 2.9% fee
      transactionId: `txn_${Date.now()}_${getRandomNumber(1000, 9999)}`,
    });
  }

  return checkoutId;
}

async function createFakeAppointmentRequest(
  knex: Knex,
  marketplaceId: number,
): Promise<number> {
  const [request] = await knex('appointmentRequests')
    .insert({
      marketplaceId,
      status: 'fulfilled',
      location: getRandomElement(LOCATIONS),
      latitude: 40.7128 + (Math.random() - 0.5) * 0.1, // Around NYC
      longitude: -74.006 + (Math.random() - 0.5) * 0.1,
      notes:
        Math.random() > 0.7 ? 'Special instructions for appointment' : null,
    })
    .returning('id');

  return request.id || request;
}

async function createFakeAppointmentConstraint(
  knex: Knex,
  requestId: number,
  organizationId: number,
): Promise<number> {
  const [constraint] = await knex('appointmentConstraints')
    .insert({
      requestId,
    })
    .returning('id');

  const constraintId = constraint.id || constraint;

  // Link constraint to organization
  await knex('appointmentConstraintsOrganizations').insert({
    constraintId,
    organizationId,
  });

  // Create time range for the constraint
  const startTime = getRandomDate();
  const endTime = dayjs(startTime)
    .add(getRandomNumber(30, 180), 'minutes')
    .toDate();

  await knex('appointmentTimeRanges').insert({
    constraintId,
    start: startTime,
    end: endTime,
  });

  return constraintId;
}

async function generateFakeAppointmentData({
  organizationId,
  appointmentCount = 50,
}: GenerateFakeDataParams) {
  const config = await loadConfig();
  const knex = createKnex(config.sqldb);

  try {
    // Initialize Objection models
    await initialize(knex, [
      Appointment,
      ClientProfile,
      Marketplace,
      MarketplaceUser,
      Organization,
      Profile,
      ProcedureBaseDefinition,
      Checkout,
      CheckoutItem,
      Payment,
    ]);

    console.log(
      `Generating ${appointmentCount} fake appointments for organization ${organizationId}...`,
    );

    // Get organization details
    const organization = await Organization.query(knex)
      .findById(organizationId)
      .withGraphFetched('[marketplaces.group]');

    if (!organization) {
      throw new Error(`Organization with ID ${organizationId} not found`);
    }

    console.log(`Found organization: ${organization.name}`);

    // Get profiles from this organization
    const profiles = await Profile.query(knex)
      .where('organizationId', organizationId)
      .whereNull('deletedAt');

    if (profiles.length === 0) {
      throw new Error(
        `No active profiles found for organization ${organizationId}`,
      );
    }

    console.log(`Found ${profiles.length} profiles in organization`);

    // Get procedure base definitions from marketplaces
    const marketplaces = organization.marketplaces || [];
    if (marketplaces.length === 0) {
      throw new Error(
        `No marketplaces found for organization ${organizationId}`,
      );
    }

    const marketplace = marketplaces[0]; // Use first marketplace
    console.log(`Using marketplace: ${marketplace.name}`);

    const procedureBaseDefs = await ProcedureBaseDefinition.query(knex)
      .where('marketplaceId', marketplace.id)
      .whereNull('deletedAt')
      .limit(10);

    if (procedureBaseDefs.length === 0) {
      throw new Error(
        `No procedure base definitions found for marketplace ${marketplace.id}`,
      );
    }

    console.log(`Found ${procedureBaseDefs.length} procedure base definitions`);

    // Generate appointments
    for (let i = 0; i < appointmentCount; i++) {
      console.log(`Creating appointment ${i + 1}/${appointmentCount}...`);

      // Create fake client profile
      const clientProfileId = await createFakeClientProfile(knex);

      // Create marketplace user
      const marketplaceUserId = await createFakeMarketplaceUser(
        knex,
        clientProfileId,
        marketplace.groupId,
      );

      // Create checkout with items and payment
      const checkoutId = await createFakeCheckout(
        knex,
        marketplaceUserId,
        marketplace.id,
      );

      // Create appointment request
      const requestId = await createFakeAppointmentRequest(
        knex,
        marketplace.id,
      );

      // Create appointment constraint
      const constraintId = await createFakeAppointmentConstraint(
        knex,
        requestId,
        organizationId,
      );

      // Create appointment
      const startTime = getRandomDate();
      const duration = getRandomNumber(30, 180); // 30-180 minutes
      const endTime = dayjs(startTime).add(duration, 'minutes').toDate();
      const status = getRandomElement([
        AppointmentStatus.BOOKED,
        AppointmentStatus.COMPLETED,
        AppointmentStatus.COMPLETED,
        AppointmentStatus.COMPLETED, // Weight towards completed
      ]);

      const [appointment] = await knex('appointments')
        .insert({
          status,
          start: startTime,
          end: endTime,
          duration,
          location: getRandomElement(LOCATIONS),
          latitude: 40.7128 + (Math.random() - 0.5) * 0.1,
          longitude: -74.006 + (Math.random() - 0.5) * 0.1,
          notes: Math.random() > 0.8 ? 'Appointment notes here' : null,
          constraintId,
          checkoutId,
          createdAt: knex.fn.now(),
          updatedAt: knex.fn.now(),
        })
        .returning('id');

      const appointmentId = appointment.id || appointment;

      // Create appointment participants
      const practitionerProfile = getRandomElement(profiles);

      // Practitioner participant
      await knex('appointmentParticipants').insert({
        appointmentId,
        type: ParticipantType.PRACTITIONER,
        status: ParticipationStatus.ACCEPTED,
        name: `${practitionerProfile.givenName} ${practitionerProfile.familyName}`,
        profileId: practitionerProfile.id,
        orderApproved: Math.random() > 0.1, // 90% approved
      });

      // Patient participant
      const clientProfile =
        await ClientProfile.query(knex).findById(clientProfileId);
      await knex('appointmentParticipants').insert({
        appointmentId,
        type: ParticipantType.PATIENT,
        status: ParticipationStatus.ACCEPTED,
        name: `${clientProfile?.givenName} ${clientProfile?.familyName}`,
        clientProfileId,
      });

      // Link appointment to procedure base definitions
      const selectedProcedures = [getRandomElement(procedureBaseDefs)];
      if (Math.random() > 0.7 && procedureBaseDefs.length > 1) {
        // 30% chance of multiple procedures, but avoid duplicates
        const secondProcedure = getRandomElement(
          procedureBaseDefs.filter((p) => p.id !== selectedProcedures[0].id),
        );
        selectedProcedures.push(secondProcedure);
      }

      for (const procedure of selectedProcedures) {
        await knex('appointmentsProcedureBaseDefs').insert({
          appointmentId,
          baseDefId: procedure.id,
        });
      }

      // Link appointment request to client profile
      await knex('appointmentRequestsClientProfiles').insert({
        requestId,
        clientProfileId,
      });

      // Link appointment request to procedure base definitions
      for (const procedure of selectedProcedures) {
        await knex('appointmentRequestsProcedureBaseDefs').insert({
          requestId,
          baseDefId: procedure.id,
        });
      }
    }

    console.log(
      `Successfully generated ${appointmentCount} fake appointments!`,
    );
  } catch (error) {
    console.error('Error generating fake data:', error);
    throw error;
  } finally {
    await knex.destroy();
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    console.error(
      'Usage: ts-node generate-fake-appointment-data.ts <organizationId> [appointmentCount]',
    );
    console.error('Example: ts-node generate-fake-appointment-data.ts 1 25');
    process.exit(1);
  }

  const organizationId = parseInt(args[0], 10);
  const appointmentCount = args[1] ? parseInt(args[1], 10) : 50;

  if (isNaN(organizationId)) {
    console.error('Error: organizationId must be a valid number');
    process.exit(1);
  }

  if (isNaN(appointmentCount) || appointmentCount <= 0) {
    console.error('Error: appointmentCount must be a positive number');
    process.exit(1);
  }

  try {
    await generateFakeAppointmentData({ organizationId, appointmentCount });
    console.log('Fake data generation completed successfully!');
  } catch (error) {
    console.error('Failed to generate fake data:', error);
    process.exit(1);
  }
}

// Export for use as a module
export { generateFakeAppointmentData };

// Run if called directly
if (require.main === module) {
  main();
}
