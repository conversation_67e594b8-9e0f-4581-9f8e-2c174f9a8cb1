// usage:
// npx ts-node scripts/create-procedure-defs.ts

import 'reflect-metadata';
import { find, map } from 'lodash';
import { initialize } from 'objection';
import { loadConfig } from '../src/config';
import SqlDbSource from '../src/datasources/SqlDbSource';
import { createKnex } from '../src/knex';
import { createMedication } from '../src/modules/inventory/create-medication';
import { Medication } from '../src/modules/inventory/sqldb';
import { MedicationProtocolFields } from '../src/modules/inventory/sqldb/types';
import { Organization } from '../src/modules/organization/sqldb';
import { createProcedureDefinition } from '../src/modules/procedure/create-def';
import {
  ProcedureBaseDefinition,
  ProcedureDefinition,
  ProcedureProfile,
} from '../src/modules/procedure/sqldb';
import { updateProcedureProfile } from '../src/modules/procedure/update-procedure-profile';

// ** Config
const PROCEDURE_BASE_DEF_ID = 561;
const TEMPLATE_DEF_ID = 23044;
const ORGANIZATION_IDS = [
  2, // Seattle, WA
  3, // Portland, OR
  5, // San Francisco, CA
  9, // Albuquerque, NM
  10, // Bakersfield, CA
  11, // Chicago, IL
  12, // Fresno, CA
  13, // Green Bay, WI
  14, // Maui, HI
  15, // Kansas City, MO
  16, // Knoxville, TN
  17, // Las Vegas, NV
  18, // Los Angeles, CA
  19, // Madison, WI
  20, // Memphis, TN
  21, // Milwaukee, WI
  22, // Minneapolis, MN
  23, // Napa/Santa Rosa, CA
  24, // Nashville, TN
  25, // New Orleans, LA
  26, // Oklahoma City, OK
  27, // Orange County, CA
  28, // Philadelphia, PA
  29, // Pittsburgh, PA
  30, // Reno, NV
  31, // Sacramento, CA
  32, // San Diego, CA
  33, // Santa Barbara, CA
  34, // Santa Fe, NM
  35, // St. Louis, MO
  36, // Big Island, HI
  37, // Oahu, HI
  38, // Kauai, HI
  39, // Bellingham, WA
  40, // Spokane, WA
  41, // Boise, ID
  42, // Palo Alto, CA
  44, // Indianapolis, IN
  45, // Cleveland, OH
  47, // Columbus, OH
  48, // Cinncinatti, OH
  49, // Aspen, CO
  50, // Jackson Hole, WY
  51, // Salt Lake/Park City, UT
  52, // St. George, UT
  54, // New Jersey, NJ
  55, // New York City, NY
  56, // Connecticut
  57, // Detroit, MI
  58, // Miami, FL
  59, // Ft. Lauderdale, FL
  60, // West Palm Beach, FL
  61, // Jacksonville, FL
  62, // Tampa, FL
  63, // Orlando, FL
  64, // Atlanta, GA
  65, // Charlotte, NC
  66, // Raleigh-Durham, NC
  67, // Washington, D.C.
  68, // Baltimore, MD
  69, // Denver, CO
  70, // Austin, TX
  73, // Virginia
  74, // Boca Raton, FL
  75, // Boston, MA
  76, // Rhode Island
  77, // Louisville, KY
  78, // Dallas, TX
  79, // New Hampshire
  80, // Maine
  82, // San Antonio, TX
  83, // Houston, TX
  84, // Rockford, IL
  85, // Inland Empire, CA
  86, // Phoenix, AZ
  87, // Palm Springs, CA
  88, // Tucson, AZ
  89, // Fort Worth, TX
  90, // Colorado Springs, CO
  91, // Sun Valley, ID
  92, // Central Coast, CA
  // 93, // Drip Hydration Events
  94, // Iowa City, IA
  95, // London, UK
  96, // Westlake Village Clinic
  97, // International
  98, // Queensland, Australia
  99, // Adelaide, Australia
  100, // Rome, Italy
  101, // Milan, Spain
  102, // Barcelona, Spain
  103, // Madrid, Spain
  104, // Tenerife, Spain
  105, // Cancun, Mexico
  106, // Ibiza, Spain
  108, // Sydney, Australia
  109, // Perth, Australia
  110, // Melbourne, Australia
  111, // Brisbane, Australia
  112, // Eugene, OR
  113, // Manchester, UK
];

const PROCEDURE_PROFILE_NAMES = [
  '1. Vitamin Infusions and Boosters',
  // '4. NAD+ Orders',
  'RN (**Personnel Profile**)',
  // 'Specialty Infusions (**Personnel Profile**)',
  // 'Office Orders (**Personnel Profile**)',
  // 'Lab Test - Swab (**Personnel Profile**)',
  // 'HQ Orders (***Personnel Profile***)',
];
// **

async function run() {
  const config = await loadConfig();

  const knex = createKnex(config.sqldb);
  const sqldb = new SqlDbSource(knex);

  // objection throws an error if these models aren't initialized:
  await initialize(knex, [ProcedureDefinition, ProcedureBaseDefinition]);

  const baseDef = await ProcedureBaseDefinition.query(sqldb.knex).findById(
    PROCEDURE_BASE_DEF_ID,
  );

  if (!baseDef) {
    throw new Error('Invalid base definition');
  }

  const def = await ProcedureDefinition.query(sqldb.knex)
    .findById(TEMPLATE_DEF_ID)
    .withGraphFetched(
      '[organization, consentForms, medicationProtocols.medication]',
    );

  if (!def) {
    throw new Error('Invalid template definition');
  }

  for (const orgId of ORGANIZATION_IDS) {
    const organization = await Organization.query(sqldb.knex)
      .findById(orgId)
      .withGraphFetched('[marketplaces, medications]');

    if (!organization) {
      throw new Error(`Invalid organization: ${orgId}`);
    }

    if (!find(organization.marketplaces ?? [], { id: baseDef.marketplaceId })) {
      throw new Error(`Organization ${orgId} not in marketplace`);
    }

    if (
      !def.organization?.emrInstanceId ||
      organization.emrInstanceId !== def.organization.emrInstanceId
    ) {
      throw new Error(`Organization ${orgId} does not share EMR instance`);
    }

    const existingDef = await ProcedureDefinition.query(sqldb.knex)
      .joinRelated('baseDefinitions')
      .where({
        'procedureDefinitions.organizationId': organization.id,
        'baseDefinitions.id': baseDef.id,
      });

    if (existingDef.length) {
      console.log(
        `Skipping: base definition already implemented for org:${orgId} (${organization.name})`,
      );
      continue;
    }

    // create any medications that don't exist on this organization
    const newMedications: Medication[] = [];

    for (const protocol of def.medicationProtocols ?? []) {
      const medication = protocol.medication;
      if (
        medication &&
        !find(organization.medications ?? [], {
          name: medication.name,
          unit: medication.unit,
        })
      ) {
        newMedications.push(medication);
      }
    }

    for (const medication of newMedications) {
      await createMedication({
        sqldb,
        organizationId: organization.id,
        medication,
      });
    }

    const medications = await Medication.query(sqldb.knex).where({
      organizationId: organization.id,
    });

    // build medication protocols
    const medicationProtocols: MedicationProtocolFields[] = [];

    for (const protocol of def.medicationProtocols ?? []) {
      const medication = protocol.medication;

      const medicationId =
        medication &&
        find(medications, {
          name: medication.name,
          unit: medication.unit,
        })?.id;

      if (medicationId) {
        medicationProtocols.push({
          medicationId,
          dose: protocol.dose,
        });
      } else {
        throw new Error('Medication was not created');
      }
    }

    const result = await createProcedureDefinition({
      sqldb,
      baseIds: [baseDef.id],
      organizationId: orgId,
      definition: {
        ...def,
        medicationProtocols,
      },
      consentFormIds: map(def.consentForms ?? [], 'id'),
    });

    const id = result?.id;

    // add the new procedure definition to procedure profiles
    if (id) {
      for (const name of PROCEDURE_PROFILE_NAMES) {
        const procedureProfile = await ProcedureProfile.query(sqldb.knex)
          .findOne({
            'procedureProfiles.organizationId': organization.id,
            'procedureProfiles.name': name,
          })
          .withGraphJoined('procedureDefs');

        if (procedureProfile) {
          await updateProcedureProfile({
            sqldb,
            procedureProfileId: procedureProfile.id,
            defIds: [
              ...(procedureProfile.procedureDefs ?? []).map((def) => def.id),
              id,
            ],
          });
        }
      }
    }
  }

  knex.destroy();
}

run();
