---
type: "always_apply"
---

# Nomad MD Guidelines for AI Agents

## Project Overview
Vertical SaaS for mobile health operators: marketplace storefronts, scheduling, dispatch, payments. Stack: location-based checkout, HIPAA EMR with telehealth, ML dispatcher. Integrations: Finix, Slack, Attentive, Reviews.io. Pricing: tiered by users/sales/size.

Architecture: Microservices with 2 GraphQL APIs (dispatch-api: core/EMR, consumer-api: patient ops), 3 web frontends (Next.js consumer/dispatch, React checkout), Flutter mobile for providers, static landing. DB: PostgreSQL. Infra: AWS CDK. Integrations: Finix, Twilio, SendGrid, Google Maps, Firebase.

Data Model: Multi-tenant hierarchy: MarketplaceGroups > Marketplaces > Organizations. Patients: ClientProfiles linked to MarketplaceUsers. Providers: Profiles with roles. Appointments: requests to dispatch to completion with EMR.

## Repos
- checkout-web: React SPA for checkout.
- consumer-api: GraphQL API for patient ops.
- consumer-web: Next.js patient interface.
- dispatch-api: GraphQL API for dispatch/EMR.
- dispatch-mobile: Flutter provider app.
- dispatch-web: Next.js provider/admin dashboard.
- landing-page: Static marketing site.

## Service Details

### checkout-web
- Stack: React 18, TanStack Query, GraphQL-request, Leaflet.
- Features: Booking, payments, availability.
- Key Dirs: app/scripts/components/nomadMD/, app/scripts/services/, app/scripts/graphql/.
- Styles: SCSS in checkout-web/app/styles.
- Deployment: AWS CDK to CloudFront/S3.

### consumer-api
- Stack: Node/TS, Apollo Server, PostgreSQL/Knex/Objection, type-graphql.
- Features: Auth (JWT/OAuth), appointments, marketplaces, payments.
- Key Dirs: src/modules/user/, src/modules/marketplace/, src/modules/appointment/, src/modules/payment/.
- Deployment: AWS CDK ECS/Fargate/RDS.

### consumer-web
- Stack: Next.js 12/TS, React 18, MUI 5, Apollo Client.
- Features: Booking, profile, payments; modes: checkout, clinic, mobile, hybrid.
- Key Dirs: src/pages/, src/components/, src/hooks/.
- Deployment: AWS CDK CloudFront.

### dispatch-api
- Stack: Node/TS, Apollo Server, PostgreSQL/Knex/Objection, type-graphql, Finix/Stripe, Twilio/SendGrid/Slack, AWS SDK, Google Maps, Segment.
- Features: Appointments, EMR, dispatch, users, orgs, payments, availability, reports.
- Key Dirs: src/modules/appointment/, src/modules/emr/, src/modules/user/, src/modules/organization/, src/modules/payment/, src/modules/procedure/, src/modules/availability/.
- Deployment: AWS CDK ECS/Fargate/RDS.

### dispatch-mobile
- Stack: Flutter/Dart, graphql_flutter, Dio, Provider, Google Maps, FCM.
- Features: Appointments, EMR docs, navigation, signatures, offline, telehealth.
- Key Dirs: lib/ui/page/main/appointment/, lib/ui/page/main/appointment/chart_note/, lib/controller/, lib/model/.
- Deployment: App Store/Play Store, Firebase.

### dispatch-web
- Stack: Next.js 12/TS, React 17, MUI 4, Apollo Client, Chart.js, DevExpress Scheduler/Grid.
- Features: Dashboard, appointments, patients, providers, orgs, financials, reports.
- Key Dirs: src/pages/, src/components/, src/hooks/.
- Deployment: AWS CDK CloudFront.

### landing-page
- Stack: Static HTML/CSS/JS.
- Features: Branding, app links, contact, privacy.
- Deployment: S3/CloudFront.

## System Actors & Roles
- Patients: consumer-web/checkout-web; book, manage profile/payments.
- Providers: dispatch-mobile/web; manage appts, EMR, navigation.
- Org Admins: dispatch-web; staff, services, finances, reports.
- Dispatchers: dispatch-web; route appts, monitor.
- Marketplace Owners: dispatch-web; orgs, brand, finances.
- Platform Admins: dispatch-web; full access.
- API Consumers: GraphQL APIs via keys.

## Permissions
Hierarchy: ROOT > MARKETPLACE > ORGANIZATION (admin/dispatch/provider).
Scopes: Root (*:*), Marketplace (marketplace.*), Org (organization.*), Self (*.self).
Resources: Users, Appts, EMR, Procedures, Payments, etc.

## Glossary
- Appointment: Scheduled service; statuses: PENDING, BOOKED, COMPLETED, etc.
- Appointment Request: Patient request pre-dispatch.
- Availability: Provider time slots.
- Client Profile: Patient info.
- Marketplace: Storefront; Group owns multiple.
- Organization: Provider entity.
- Procedure: Service (e.g., IV Hydration).
- Profile: Provider account.
- Chart Notes: Clinical docs.
- EMR: Patient records (forms, obs, vitals).
- Dispatch: Routing by proximity/avail.
- Checkout: Transaction record.
- Membership/Package: Subscriptions/bundles.
- Payment Instrument: Stored method.
- Role: Permission sets; Scope: ROOT/MARKETPLACE/ORG.

## Data Model
- Marketplace: MarketplaceGroup > Marketplace > Organization.
- Scheduling: AvailabilityRanges > AppointmentRequest > Appointment (Participants, Constraints).
- EMR: EMRInstance > EMRPatient > Forms, Observations, Vitals, Documents.
- Procedures: ProcedureDef > ProcedureProfiles, FormTemplates > Forms.
- Finance: Checkout > Payment/PaymentInstrument; Package, Membership, Payout.

## Guidelines for completing tasks
- Always check for existing naming and style conventions and codebase best practices and follow them.
- DO NOT write tests for your changes
- DO NOT add unnecessary comments to the code, only add comments to explain non-obvious code
- DO NOT try to run npm scripts or run the app locally
- If it's necessary to add/modify database fields first list all the changes that need to be made to the database schema and then ask for confirmation before making any changes
- Database migrations are made using `pnpx knex migrate:make <migration name>` script in dispatch-api and consumer-api