import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import CardDataRow from '@/components/CardDataRow';
import Content from '@/components/Content';
import CreateProcedureProfileDialog from '@/components/CreateProcedureProfileDialog';
import CreateProfileDialog from '@/components/CreateProfileDialog';
import CreateRoleDialog from '@/components/CreateRoleDialog';
import MuiLink from '@/components/Link';
import PaymentAccountCard from '@/components/PaymentAccountCard';
import ProcedureDefDetailDialog, {
  durationRange,
} from '@/components/ProcedureDefDetailDialog';
import ProcedureProfileDetailDialog from '@/components/ProcedureProfileDetailDialog';
import ProfileDetailDialog from '@/components/ProfileDetailDialog';
import ResponsiveCell from '@/components/ResponsiveCell';
import RoleDetailDialog from '@/components/RoleDetailDialog';
import UserFrame from '@/components/UserFrame';
import {
  RoleFieldsFragment,
  RoleScope,
  useOrganizationSettingsQuery,
  useProcedureDefinitionQuery,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useDenormalizedOrganization } from '@/hooks/organization';
import { useProfile } from '@/hooks/profile';
import { useClickHandler, usePathHash } from '@/hooks/router';
import { useLayoutStyles, useTableStyles } from '@/hooks/styles';
import { formatPhoneNumber, fullName } from '@/utils/common';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import ChevronIcon from '@material-ui/icons/ChevronRight';
import ArrowIcon from '@material-ui/icons/East';
import EditIcon from '@material-ui/icons/Edit';
import clsx from 'clsx';
import { find } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import CheckCircle from '@material-ui/icons/CheckCircle';
import ErrorIcon from '@material-ui/icons/Error';

const listPermission = [
  'organizations:full',
  'organizations:list',
  {
    scope: RoleScope.Organization,
    permission: [],
  },
];

const editPermission = [
  'organizations:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization:update'],
  },
];

const createPermission = [
  'profiles:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.profiles:create', 'organization.profiles:full'],
  },
];

const profilesPermission = [
  'profiles:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.profiles:full'],
  },
];

const rolesPermission = [
  'roles:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.roles:full'],
  },
];

const procPermission = [
  'procedure-defs:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.procedure-defs:full'],
  },
];

const paymentsPermission = [
  'payments:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.payments:full'],
  },
];

const emrPermission = ['emr:full'];

export default function Organization(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const tableClasses = useTableStyles();

  const hash = usePathHash();
  const clickHandler = useClickHandler();
  const router = useRouter();
  const id = router.query.id as string;

  const [canList] = useAuthorize(listPermission, { resourceId: id });
  const [canEdit] = useAuthorize(editPermission, { resourceId: id });
  const [canCreateProfile] = useAuthorize(createPermission, { resourceId: id });
  const [canCreateRole] = useAuthorize(rolesPermission, { resourceId: id });
  const [canEditProcedures] = useAuthorize(procPermission, { resourceId: id });
  const [canEditEmr] = useAuthorize(emrPermission);
  const [canEditProfiles] = useAuthorize(profilesPermission, {
    resourceId: id,
  });
  const [canEditPayments] = useAuthorize(paymentsPermission, {
    resourceId: id,
  });

  const { profile } = useProfile();
  const { data: organizationData } = useOrganizationSettingsQuery({
    variables: { id },
    skip: !id || !canList,
  });

  const organization = useDenormalizedOrganization(
    organizationData?.organization,
  );

  const selectedProfile = (organization?.profiles ?? []).find(
    (p) => p.pid === hash.profile,
  );

  const { data: procedureDefData } = useProcedureDefinitionQuery({
    variables: { id: hash.def as string },
    skip: !hash.def || !canList,
  });

  const selectedDefinition =
    procedureDefData?.procedureDefinition ||
    (organization?.procedureDefs ?? []).find((def) => def.id === hash.def);

  const selectedRole = (organization?.roles ?? []).find(
    (r) => r.id === hash.role,
  );

  const selectedProcedureProfile = (organization?.procedureProfiles ?? []).find(
    (p) => p.id === hash.procedureProfile,
  );

  const rootPath = `/p/${profile?.pid}/root`;
  const path = `${rootPath}/organizations/${organization?.id}`;

  return (
    <UserFrame>
      <Content title={organization?.name ?? 'Organization'} maxWidth="md">
        <BreadcrumbsHeader backHref={`/p/${profile?.pid}/root/organizations`}>
          <MuiLink
            color="inherit"
            href={`/p/${profile?.pid}/root/organizations`}
          >
            Organizations
          </MuiLink>
          <Typography color="textPrimary">{organization?.name}</Typography>
        </BreadcrumbsHeader>

        <Card className={layoutClasses.card} variant="outlined">
          <CardHeader title="Organization Information" />
          <CardContent>
            <CardDataRow label="ID" value={organization?.id} />
            <CardDataRow label="Name" value={organization?.name} />
            <CardDataRow
              label="Time zone"
              value={
                <span>
                  {organization && dayjs().tz(organization.tzid).format('z ')}-{' '}
                  {organization?.tzid}
                </span>
              }
            />
            <CardDataRow label="Email" value={organization?.email} />
            <CardDataRow
              label="Phone Number"
              value={formatPhoneNumber(organization?.phone)}
            />
            {!!organization?.address && (
              <CardDataRow label="Address" value={organization?.address} />
            )}
            {!!organization?.slackWebhookUrl && (
              <CardDataRow
                label="Slack webhook"
                value={
                  organization.slackWebhookUrl.length > 40
                    ? `${organization.slackWebhookUrl.substring(0, 40)}...`
                    : organization.slackWebhookUrl
                }
              />
            )}
            {!!organization?.googleReviewsUrl && (
              <CardDataRow
                label="Google Reviews URL"
                value={
                  organization.googleReviewsUrl.length > 40
                    ? `${organization.googleReviewsUrl.substring(0, 40)}...`
                    : organization.googleReviewsUrl
                }
              />
            )}
            <CardDataRow
              value={
                Boolean(organization) && (
                  <Box display="flex">
                    {organization?.providesAtClinic ? (
                      <>
                        <CheckCircle
                          className={layoutClasses.icon}
                          color="primary"
                        />
                        Enabled
                      </>
                    ) : (
                      <>
                        <ErrorIcon
                          className={layoutClasses.icon}
                          color="secondary"
                        />
                        Disabled
                      </>
                    )}
                  </Box>
                )
              }
              label="Clinic services"
            />
            <CardDataRow
              value={
                Boolean(organization) && (
                  <Box display="flex">
                    {organization?.enablePractitionerSms ? (
                      <>
                        <CheckCircle
                          className={layoutClasses.icon}
                          color="primary"
                        />
                        Enabled
                      </>
                    ) : (
                      <>
                        <ErrorIcon
                          className={layoutClasses.icon}
                          color="secondary"
                        />
                        Disabled
                      </>
                    )}
                  </Box>
                )
              }
              label="Pending appt SMS"
            />

            {canEdit && (
              <Box mt={3}>
                <Link href={`${path}/edit`} passHref>
                  <Button
                    component="a"
                    variant="outlined"
                    color="primary"
                    startIcon={<EditIcon />}
                  >
                    Edit information
                  </Button>
                </Link>
              </Box>
            )}
          </CardContent>
        </Card>

        <Card className={layoutClasses.card} variant="outlined">
          <CardHeader title="Personnel" />
          <CardContent>
            <TableContainer>
              <Table
                className={tableClasses.fixed}
                aria-label="personnel table"
              >
                <TableHead className={tableClasses.smUp}>
                  <TableRow className={tableClasses.row}>
                    <ResponsiveCell min="sm" width="50%">
                      Name
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width="50%">
                      Role
                    </ResponsiveCell>
                    <ResponsiveCell min="md" width="37%">
                      Mobile
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width="23%">
                      User ID
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width={56} />
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(organization?.profiles ?? []).map((p) => (
                    <TableRow
                      key={p.id}
                      className={clsx(
                        tableClasses.row,
                        tableClasses.highlight,
                        tableClasses.clickable,
                      )}
                      onClick={clickHandler.push(`${path}#profile=${p.pid}`)}
                    >
                      <ResponsiveCell min="xs" component="th" scope="row">
                        {fullName([p?.givenName, p?.familyName], p?.title) || [
                          p?.email,
                        ]}
                      </ResponsiveCell>
                      <ResponsiveCell min="sm" component="th" scope="row">
                        {
                          (
                            find(p.roles, {
                              scope: RoleScope.Organization,
                              resourceId: id,
                            }) as RoleFieldsFragment
                          )?.name
                        }
                      </ResponsiveCell>
                      <ResponsiveCell min="md" component="th" scope="row">
                        {formatPhoneNumber(p.phone)}
                      </ResponsiveCell>
                      <ResponsiveCell min="sm" component="th" scope="row">
                        {p.userId ?? ''}
                      </ResponsiveCell>
                      <ResponsiveCell
                        min="xs"
                        component="th"
                        scope="row"
                        width={56}
                      >
                        <Box color="text.secondary" display="flex">
                          <ChevronIcon />
                        </Box>
                      </ResponsiveCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            {canCreateProfile && (
              <Box mt={3}>
                <Link href={`${path}#create-profile`} passHref>
                  <Button
                    component="a"
                    variant="outlined"
                    color="primary"
                    startIcon={<AddIcon />}
                  >
                    Create profile
                  </Button>
                </Link>
              </Box>
            )}
          </CardContent>
        </Card>

        <Card className={layoutClasses.card} variant="outlined">
          <CardHeader title="Procedure Definitions" />
          <CardContent>
            <TableContainer>
              <Table
                className={tableClasses.fixed}
                aria-label="personnel table"
              >
                <TableHead className={tableClasses.smUp}>
                  <TableRow className={tableClasses.row}>
                    <ResponsiveCell min="sm" width="50%">
                      Name
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width="50%">
                      Marketplace
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width="25%">
                      Duration
                    </ResponsiveCell>
                    <ResponsiveCell min="md" width="25%">
                      Price
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width={56} />
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(organization?.procedureDefs ?? []).map((def) => (
                    <TableRow
                      key={def.id}
                      className={clsx(
                        tableClasses.row,
                        tableClasses.highlight,
                        tableClasses.clickable,
                      )}
                      onClick={clickHandler.push(`${path}#def=${def.id}`)}
                    >
                      <ResponsiveCell min="xs" component="th" scope="row">
                        {def.name}
                      </ResponsiveCell>
                      <ResponsiveCell min="sm" component="th" scope="row">
                        {def.baseDefinitions
                          .map((baseDef) =>
                            find(organization?.marketplaces ?? [], {
                              id: baseDef.marketplaceId,
                            }),
                          )
                          .map((marketplace) => marketplace?.name)
                          .filter((name) => name)
                          .join(', ')}
                      </ResponsiveCell>
                      <ResponsiveCell min="sm" component="th" scope="row">
                        {durationRange(def.baseDefinitions) ?? def.duration}
                      </ResponsiveCell>
                      <ResponsiveCell min="md" component="th" scope="row">
                        ${def.price}
                      </ResponsiveCell>
                      <ResponsiveCell
                        min="xs"
                        component="th"
                        scope="row"
                        width={56}
                      >
                        <Box color="text.secondary" display="flex">
                          <ChevronIcon />
                        </Box>
                      </ResponsiveCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            {canEditProcedures && (
              <Box mt={3}>
                <Link href={`${path}/procedures`} passHref>
                  <Button
                    component="a"
                    variant="outlined"
                    color="primary"
                    startIcon={<EditIcon />}
                  >
                    Edit procedures
                  </Button>
                </Link>
              </Box>
            )}
          </CardContent>
        </Card>

        <Card className={layoutClasses.card} variant="outlined">
          <CardHeader title="Roles" />
          <CardContent>
            <TableContainer>
              <Table className={tableClasses.fixed} aria-label="roles table">
                <TableHead className={tableClasses.smUp}>
                  <TableRow className={tableClasses.row}>
                    <ResponsiveCell min="sm">Name</ResponsiveCell>
                    <ResponsiveCell min="sm" style={{ width: 56 }} />
                  </TableRow>
                </TableHead>
                <TableBody>
                  {(organization?.roles ?? []).map((role) => (
                    <TableRow
                      key={role.id}
                      className={clsx(
                        tableClasses.row,
                        tableClasses.highlight,
                        tableClasses.clickable,
                      )}
                      onClick={clickHandler.push(`${path}#role=${role.id}`)}
                    >
                      <ResponsiveCell min="xs" component="th" scope="row">
                        {role.name}
                      </ResponsiveCell>
                      <ResponsiveCell
                        min="xs"
                        component="th"
                        scope="row"
                        width={56}
                      >
                        <Box color="text.secondary" display="flex">
                          <ChevronIcon />
                        </Box>
                      </ResponsiveCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            {canCreateRole && (
              <Box mt={3}>
                <Link href={`${path}#create-role`} passHref>
                  <Button
                    component="a"
                    variant="outlined"
                    color="primary"
                    startIcon={<AddIcon />}
                  >
                    Create role
                  </Button>
                </Link>
              </Box>
            )}
          </CardContent>
        </Card>

        <Card className={layoutClasses.card} variant="outlined">
          <CardHeader title="Marketplaces" />
          <CardContent>
            <TableContainer>
              <Table
                className={tableClasses.fixed}
                aria-label="marketplaces table"
              >
                <TableHead className={tableClasses.smUp}>
                  <TableRow className={tableClasses.row}>
                    <ResponsiveCell min="sm">Name</ResponsiveCell>
                    <ResponsiveCell min="sm" width="33%">
                      ID
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width={56} />
                  </TableRow>
                </TableHead>
                <TableBody>
                  {organization?.marketplaces.map((marketplace) => (
                    <TableRow
                      key={marketplace.id}
                      className={clsx(
                        tableClasses.row,
                        tableClasses.highlight,
                        tableClasses.clickable,
                      )}
                      onClick={clickHandler.push(
                        `/p/${profile?.pid}/root/marketplaces/${marketplace.id}`,
                      )}
                    >
                      <ResponsiveCell min="xs" component="th" scope="row">
                        {marketplace.name}
                      </ResponsiveCell>
                      <ResponsiveCell
                        min="sm"
                        component="th"
                        scope="row"
                        width="33%"
                      >
                        {marketplace.id}
                      </ResponsiveCell>
                      <ResponsiveCell
                        min="xs"
                        component="th"
                        scope="row"
                        width={56}
                      >
                        <Box color="text.secondary" display="flex">
                          <ChevronIcon />
                        </Box>
                      </ResponsiveCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {canEditProfiles && (
          <Card className={layoutClasses.card} variant="outlined">
            <CardHeader title="Procedure Profiles" />
            <CardContent>
              <TableContainer>
                <Table className={tableClasses.fixed} aria-label="roles table">
                  <TableHead className={tableClasses.smUp}>
                    <TableRow className={tableClasses.row}>
                      <ResponsiveCell min="sm">Name</ResponsiveCell>
                      <ResponsiveCell min="sm" style={{ width: 56 }} />
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {(organization?.procedureProfiles ?? []).map(
                      (procedureProfile) => (
                        <TableRow
                          key={procedureProfile.id}
                          className={clsx(
                            tableClasses.row,
                            tableClasses.highlight,
                            tableClasses.clickable,
                          )}
                          onClick={clickHandler.push(
                            `${path}#procedureProfile=${procedureProfile.id}`,
                          )}
                        >
                          <ResponsiveCell min="xs" component="th" scope="row">
                            {procedureProfile.name}
                          </ResponsiveCell>
                          <ResponsiveCell
                            min="xs"
                            component="th"
                            scope="row"
                            width={56}
                          >
                            <Box color="text.secondary" display="flex">
                              <ChevronIcon />
                            </Box>
                          </ResponsiveCell>
                        </TableRow>
                      ),
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
              <Box mt={3}>
                <Link href={`${path}#create-procedure-profile`} passHref>
                  <Button
                    component="a"
                    variant="outlined"
                    color="primary"
                    startIcon={<AddIcon />}
                  >
                    Create procedure profile
                  </Button>
                </Link>
              </Box>
            </CardContent>
          </Card>
        )}

        <Card className={layoutClasses.card} variant="outlined">
          <CardHeader title="EMR" />
          <CardContent>
            <CardDataRow
              label="EMR Instance"
              value={organization?.emrInstance?.label ?? <em>Not assigned</em>}
            />
            {canEdit && canEditEmr && (
              <Box mt={3}>
                {organization?.emrInstanceId ? (
                  <Link
                    href={`${rootPath}/emr-instances/${organization.emrInstanceId}`}
                    passHref
                  >
                    <Button
                      component="a"
                      variant="outlined"
                      color="primary"
                      startIcon={<ArrowIcon />}
                    >
                      EMR Details
                    </Button>
                  </Link>
                ) : (
                  <Link href={`${rootPath}/emr-instances`} passHref>
                    <Button
                      component="a"
                      variant="outlined"
                      color="primary"
                      startIcon={<AddIcon />}
                    >
                      Assign an EMR instance
                    </Button>
                  </Link>
                )}
              </Box>
            )}
          </CardContent>
        </Card>

        {canEditPayments && (
          <div>
            <Typography variant="h6" gutterBottom>
              Payment Accounts
            </Typography>
            {organization?.paymentAccounts.map((paymentAccount) => (
              <PaymentAccountCard
                key={paymentAccount.id}
                paymentAccount={paymentAccount}
                organization={organization}
              />
            ))}
          </div>
        )}

        <ProfileDetailDialog
          key={`{selectedProfile?.id}-${Boolean(selectedProfile)}`}
          open={Boolean(selectedProfile)}
          profile={
            selectedProfile && {
              ...selectedProfile,
              organization,
            }
          }
          onClose={() => {
            if (selectedProfile) {
              router.replace(path);
              router.back();
            }
          }}
        />

        {!!organization && (
          <CreateProfileDialog
            open={Object.keys(hash).includes('create-profile')}
            organization={organization}
            onClose={() => {
              if (Object.keys(hash).includes('create-profile')) {
                router.replace(path);
                router.back();
              }
            }}
          />
        )}

        <ProcedureDefDetailDialog
          open={Boolean(selectedDefinition)}
          definition={selectedDefinition}
          organization={organization}
          onClose={() => {
            if (selectedDefinition) {
              router.replace(path);
              router.back();
            }
          }}
        />

        <RoleDetailDialog
          open={Boolean(selectedRole)}
          role={selectedRole}
          onClose={() => {
            if (selectedRole) {
              router.replace(path);
              router.back();
            }
          }}
        />

        {!!organization && (
          <CreateRoleDialog
            open={Object.keys(hash).includes('create-role')}
            scope={RoleScope.Organization}
            resourceId={organization.id}
            onClose={() => {
              if (Object.keys(hash).includes('create-role')) {
                router.replace(path);
                router.back();
              }
            }}
          />
        )}

        <ProcedureProfileDetailDialog
          open={Boolean(selectedProcedureProfile)}
          procedureProfile={selectedProcedureProfile}
          organization={organization}
          onClose={() => {
            if (selectedProcedureProfile) {
              router.replace(path);
              router.back();
            }
          }}
        />

        {!!organization && (
          <CreateProcedureProfileDialog
            open={Object.keys(hash).includes('create-procedure-profile')}
            organization={organization}
            onClose={() => {
              if (Object.keys(hash).includes('create-procedure-profile')) {
                router.replace(path);
                router.back();
              }
            }}
          />
        )}
      </Content>
    </UserFrame>
  );
}
