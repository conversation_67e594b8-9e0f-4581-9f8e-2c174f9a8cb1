import Content from '@/components/Content';
import OrganizationForm<PERSON>ields, {
  OrganizationFields,
  OrganizationSchema,
} from '@/components/OrganizationFormFields';
import UserFrame from '@/components/UserFrame';
import {
  RoleScope,
  UpdateOrganizationInput,
  useOrganizationSettingsQuery,
  useUpdateOrganizationMutation,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { useLayoutStyles } from '@/hooks/styles';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  LinearProgress,
  Switch,
  useMediaQuery,
  useTheme,
  FormControlLabel,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';

const editPermission = [
  'organizations:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization:update'],
  },
];

export default function EditOrganization(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const router = useRouter();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const [error, setError] = useState<string[]>([]);
  const [providesAtClinic, setProvidedsAtClinic] = useState(false);
  const [enablePractitionerSms, setEnablePractitionerSms] = useState(false);

  const [updateOrganization, { loading: submitting }] =
    useUpdateOrganizationMutation();

  const { query } = useRouter();
  const id = query.id as string;

  const [canEdit] = useAuthorize(editPermission, { resourceId: id });

  const { profile } = useProfile();
  const { data: organizationData, loading } = useOrganizationSettingsQuery({
    variables: { id },
    skip: !id,
    onCompleted(data) {
      setProvidedsAtClinic(Boolean(data.organization?.providesAtClinic));
      setEnablePractitionerSms(
        Boolean(data?.organization?.enablePractitionerSms),
      );
    },
  });

  const organization = organizationData?.organization;
  const returnUri = `/p/${profile?.pid}/root/organizations/${id}`;

  const initialValues: OrganizationFields = {
    name: organization?.name ?? '',
    tzid: organization?.tzid ?? dayjs.tz.guess(),
    slackWebhookUrl: organization?.slackWebhookUrl ?? '',
    googleReviewsUrl: organization?.googleReviewsUrl ?? '',
    phone: organization?.phone ?? '',
    email: organization?.email ?? '',
    address: organization?.address ?? '',
  };

  async function handleSubmit(values: OrganizationFields) {
    if (!profile || !organization || submitting) {
      return;
    }

    const input = {
      ...Object.fromEntries(
        Object.entries(values).filter(
          ([key, value]) =>
            initialValues[key as keyof OrganizationFields] !== value,
        ),
      ),
      id: organization.id,
      providesAtClinic,
      enablePractitionerSms,
    } as UpdateOrganizationInput;

    if (input.slackWebhookUrl === '') {
      input.slackWebhookUrl = null;
    }

    if (input.googleReviewsUrl === '') {
      input.googleReviewsUrl = null;
    }

    try {
      const { errors } = await updateOrganization({
        variables: { input },
      });

      if (errors?.length) {
        setError(
          formatGraphQLErrors(errors) ?? ['Error updating organization'],
        );
      } else {
        router.push(returnUri);
        return;
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  }

  return (
    <UserFrame>
      <Content title="Edit Organization" maxWidth="sm">
        {!loading && (!profile || !organization) ? (
          <Alert severity="error" variant="filled">
            Invalid organization
          </Alert>
        ) : (
          <div>
            {error.length > 0 &&
              error.map((err) => (
                <Box key={err} mb={2}>
                  <Alert severity="error" variant="outlined">
                    {err}
                  </Alert>
                </Box>
              ))}
            <Card className={layoutClasses.card} variant="outlined">
              <div className={layoutClasses.progress}>
                {submitting && <LinearProgress />}
              </div>
              <CardHeader title="Edit Organization" />
              <CardContent>
                {!loading && (
                  <Formik
                    initialValues={initialValues}
                    validationSchema={OrganizationSchema}
                    onSubmit={handleSubmit}
                  >
                    {({ dirty }) => (
                      <Form>
                        <OrganizationFormFields
                          tzid={initialValues.tzid}
                          native={fullScreen}
                        />
                        <Box display="flex">
                          <FormControlLabel
                            control={
                              <Switch
                                checked={providesAtClinic}
                                onChange={(_, checked) =>
                                  setProvidedsAtClinic(checked)
                                }
                                name="checkedA"
                                color="primary"
                                size="medium"
                              />
                            }
                            label="Provides services at clinic"
                          />
                        </Box>
                        <Box display="flex">
                          <FormControlLabel
                            control={
                              <Switch
                                checked={enablePractitionerSms}
                                onChange={(_, checked) =>
                                  setEnablePractitionerSms(checked)
                                }
                                name="checkedB"
                                color="primary"
                                size="medium"
                              />
                            }
                            label="Send text notifications to providers"
                          />
                        </Box>

                        <Box mt={4} display="flex" justifyContent="flex-end">
                          <Box>
                            <Link href={returnUri} passHref>
                              <Button
                                component="a"
                                variant="text"
                                color="primary"
                                size="large"
                              >
                                Cancel
                              </Button>
                            </Link>
                          </Box>
                          <Box ml={1}>
                            <Button
                              variant="contained"
                              color="primary"
                              size="large"
                              disabled={
                                (!dirty &&
                                  organization?.providesAtClinic ===
                                    providesAtClinic &&
                                  organization?.enablePractitionerSms ===
                                    enablePractitionerSms) ||
                                submitting ||
                                !canEdit
                              }
                              type="submit"
                            >
                              Save
                            </Button>
                          </Box>
                        </Box>
                      </Form>
                    )}
                  </Formik>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </Content>
    </UserFrame>
  );
}
