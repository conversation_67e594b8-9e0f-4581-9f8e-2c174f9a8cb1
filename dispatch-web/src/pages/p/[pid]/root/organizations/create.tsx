import Content from '@/components/Content';
import OrganizationFormFields, {
  OrganizationFields,
  OrganizationSchema,
} from '@/components/OrganizationFormFields';
import UserFrame from '@/components/UserFrame';
import {
  OrganizationsDocument,
  OrganizationsQuery,
  useCreateOrganizationMutation,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { useLayoutStyles } from '@/hooks/styles';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  LinearProgress,
  Typography,
  useMediaQuery,
  useTheme,
  Switch,
  FormControlLabel,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';

import { Field, Form, Formik } from 'formik';
import { TextField } from 'formik-material-ui';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import * as yup from 'yup';

const CreateOrganizationSchema = OrganizationSchema.concat(
  yup
    .object()
    .shape({
      ownerEmails: yup
        .array()
        .transform(
          // delimited string -> string[]
          (_, originalValue) =>
            (yup.string().isType(originalValue) && [
              ...new Set(
                originalValue
                  ?.split(/[ ,;\n\t]+/)
                  .filter((x) => x)
                  .map((email) => email.toLowerCase()),
              ),
            ]) ??
            originalValue,
        )
        .required('Specify at least one owner')
        .of(yup.string().required('Required').email('Invalid email address'))
        .min(1)
        .max(10, 'You can specify up to 10 owners'),
    })
    .required(),
);

interface CreateOrganizationFields extends OrganizationFields {
  ownerEmails: string;
}

const createPermission = ['organizations:create', 'organizations:full'];

export default function CreateOrganization(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const router = useRouter();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const [error, setError] = useState<string[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [createOrganization] = useCreateOrganizationMutation();
  const [providesAtClinic, setProvidedsAtClinic] = useState(false);
  const [enablePractitionerSms, setEnablePractitionerSms] = useState(false);
  const [canCreate] = useAuthorize(createPermission);
  const { profile } = useProfile();

  const initialValues: CreateOrganizationFields = {
    name: '',
    tzid: dayjs.tz.guess(),
    slackWebhookUrl: '',
    googleReviewsUrl: '',
    phone: '',
    email: '',
    ownerEmails: '',
    address: '',
  };

  async function handleSubmit(values: CreateOrganizationFields) {
    const inputValues = CreateOrganizationSchema.cast(values);

    if (!canCreate || !inputValues) {
      return;
    }

    const input = {
      ...inputValues,
      phone: inputValues.phone || null,
      email: inputValues.email || null,
      slackWebhookUrl: inputValues.slackWebhookUrl || null,
      googleReviewsUrl: inputValues.googleReviewsUrl || null,
      address: inputValues.address || null,
      providesAtClinic,
      enablePractitionerSms,
    };

    setSubmitting(true);

    try {
      const { errors } = await createOrganization({
        variables: { input },
        update: (store, response) => {
          const organization = response.data?.createOrganization;

          if (organization) {
            try {
              const data = store.readQuery<OrganizationsQuery>({
                query: OrganizationsDocument,
              });

              const organizations = [
                ...(data?.organizations ?? []),
                organization,
              ];

              store.writeQuery<OrganizationsQuery>({
                query: OrganizationsDocument,
                data: { organizations },
              });
            } catch {
              // might get here if the organizations query hasn't been cached yet
            }
          }
        },
      });

      if (errors?.length) {
        setError(
          formatGraphQLErrors(errors) ?? ['Error creating the organization'],
        );
      } else {
        router.push(`/p/${profile?.pid}/root/organizations`);
        return;
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }

    setSubmitting(false);
  }

  return (
    <UserFrame>
      <Content title="Create organization" maxWidth="sm">
        {!canCreate ? (
          <Alert severity="error" variant="filled">
            Not authorized
          </Alert>
        ) : (
          <div>
            {error.length > 0 &&
              error.map((err) => (
                <Box key={err} mb={2}>
                  <Alert severity="error" variant="outlined">
                    {err}
                  </Alert>
                </Box>
              ))}
            <Card className={layoutClasses.card} variant="outlined">
              <div className={layoutClasses.progress}>
                {submitting && <LinearProgress />}
              </div>
              <CardHeader title="Create an organization" />
              <CardContent>
                <Formik
                  initialValues={initialValues}
                  validationSchema={CreateOrganizationSchema}
                  onSubmit={handleSubmit}
                >
                  {() => (
                    <Form>
                      <OrganizationFormFields native={fullScreen} autoFocus />
                      <Box display="flex" mb={2}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={providesAtClinic}
                              onChange={(_, checked) =>
                                setProvidedsAtClinic(checked)
                              }
                              name="checkedA"
                              color="primary"
                              size="medium"
                            />
                          }
                          label="Provides services at clinic"
                        />
                      </Box>
                      <Box display="flex">
                        <FormControlLabel
                          control={
                            <Switch
                              checked={enablePractitionerSms}
                              onChange={(_, checked) =>
                                setEnablePractitionerSms(checked)
                              }
                              name="checkedB"
                              color="primary"
                              size="medium"
                            />
                          }
                          label="Send text notifications to providers"
                        />
                      </Box>
                      <Box mb={4}>
                        <Box>
                          <Field
                            component={TextField}
                            name="ownerEmails"
                            label="Send owner invitations"
                            variant="outlined"
                            multiline
                            fullWidth
                            minRows={4}
                            maxRows={10}
                          />
                        </Box>
                        <Box mx={2} my={1}>
                          <Typography variant="body2">
                            Provide a list of email addresses to receive
                            invitation codes.
                          </Typography>
                        </Box>
                      </Box>
                      <Box display="flex" justifyContent="flex-end">
                        <Box>
                          <Link
                            href={`/p/${profile?.pid}/root/organizations`}
                            passHref
                          >
                            <Button
                              component="a"
                              variant="text"
                              color="primary"
                              size="large"
                            >
                              Cancel
                            </Button>
                          </Link>
                        </Box>
                        <Box ml={1}>
                          <Button
                            variant="contained"
                            color="primary"
                            size="large"
                            disabled={submitting}
                            type="submit"
                          >
                            Create
                          </Button>
                        </Box>
                      </Box>
                    </Form>
                  )}
                </Formik>
              </CardContent>
            </Card>
          </div>
        )}
      </Content>
    </UserFrame>
  );
}
