import CardDataRow from '@/components/CardDataRow';
import Content from '@/components/Content';
import { ImageData } from '@/components/ImageUploader';
import MarketplaceChangeGroupDialog from '@/components/MarketplaceChangeGroupDialog';
import MarketplaceFormFields, {
  MarketplaceFields,
  MarketplaceSchema,
} from '@/components/MarketplaceFormFields';
import UserFrame from '@/components/UserFrame';
import {
  UpdateMarketplaceInput,
  useMarketplaceQuery,
  useUpdateMarketplaceMutation,
} from '@/generated/graphql';
import { useProfile } from '@/hooks/profile';
import { usePathHash } from '@/hooks/router';
import { useLayoutStyles } from '@/hooks/styles';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  LinearProgress,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

export default function EditMarketplace(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const router = useRouter();
  const hash = usePathHash();

  const [error, setError] = useState<string[]>([]);
  const [logo, setLogo] = useState<ImageData | null>(null);
  const [initialLogo, setInitialLogo] = useState<ImageData | null>(null);

  const [updateMarketplace, { loading: submitting }] =
    useUpdateMarketplaceMutation();

  const { query } = useRouter();
  const id = query.id as string;

  const { profile } = useProfile();
  const { data: marketplaceData, loading } = useMarketplaceQuery({
    variables: { id },
    skip: !id,
  });

  const marketplace = marketplaceData?.marketplace;
  const returnUri = `/p/${profile?.pid}/root/marketplaces/${id}`;
  const path = `/p/${profile?.pid}/root/marketplaces/${id}/edit`;

  const changeGroupOpen = Object.keys(hash).includes('change-group');

  // Initialize logo from marketplace data
  useEffect(() => {
    if (marketplace) {
      const logoData = marketplace.logo ? { url: marketplace.logo } : null;
      setLogo(logoData);
      setInitialLogo(logoData);
    }
  }, [marketplace]);

  const initialValues: MarketplaceFields = {
    name: marketplace?.name ?? '',
    requireDispatchApproval: marketplace?.requireDispatchApproval ?? true,
    requirePractitionerApproval:
      marketplace?.requirePractitionerApproval ?? false,
    slackWebhookUrl: marketplace?.slackWebhookUrl ?? '',
    reviewsIoStoreId: marketplace?.reviewsIoStoreId ?? '',
    reviewsIoKey: marketplace?.reviewsIoKeyDescription
      ? '●'.repeat(28) + marketplace.reviewsIoKeyDescription
      : '',
    primaryColor: marketplace?.primaryColor ?? '#000000',
  };

  async function handleSubmit(values: MarketplaceFields) {
    if (!profile || !marketplace || submitting) {
      return;
    }

    const input = {
      ...Object.fromEntries(
        Object.entries(values).filter(
          ([key, value]) =>
            initialValues[key as keyof MarketplaceFields] !== value,
        ),
      ),
      id: marketplace.id,
    } as UpdateMarketplaceInput;

    if (input.slackWebhookUrl === '') {
      input.slackWebhookUrl = null;
    }

    if (input.primaryColor === '') {
      input.primaryColor = null;
    }

    // Add logo to input if it's changed
    if (logo?.token) {
      input.logoToken = logo.token;
    } else if (logo === null) {
      input.logoToken = null;
    }

    try {
      const { errors } = await updateMarketplace({
        variables: { input },
      });

      if (errors?.length) {
        setError(formatGraphQLErrors(errors) ?? ['Error updating marketplace']);
      } else {
        router.push(returnUri);
        return;
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  }

  return (
    <UserFrame>
      <Content title="Edit Marketplace" maxWidth="sm">
        {!loading && (!profile || !marketplace) ? (
          <Alert severity="error" variant="filled">
            Invalid marketplace
          </Alert>
        ) : (
          <div>
            {error.length > 0 &&
              error.map((err) => (
                <Box key={err} mb={2}>
                  <Alert severity="error" variant="outlined">
                    {err}
                  </Alert>
                </Box>
              ))}
            <Card className={layoutClasses.card} variant="outlined">
              <div className={layoutClasses.progress}>
                {submitting && <LinearProgress />}
              </div>
              <CardHeader title="Edit Marketplace" />
              <CardContent>
                {!loading && (
                  <Formik
                    initialValues={initialValues}
                    validationSchema={MarketplaceSchema}
                    onSubmit={handleSubmit}
                  >
                    {({ dirty }) => {
                      // Check if logo has changed
                      const logoChanged =
                        logo?.token || logo?.url !== initialLogo?.url;
                      const hasChanges = dirty || logoChanged;

                      return (
                        <Form>
                          <MarketplaceFormFields
                            logo={logo}
                            onLogoChange={setLogo}
                          />
                          <Box mt={4} display="flex" justifyContent="flex-end">
                            <Box>
                              <Link href={returnUri} passHref>
                                <Button
                                  component="a"
                                  variant="text"
                                  color="primary"
                                  size="large"
                                >
                                  Cancel
                                </Button>
                              </Link>
                            </Box>
                            <Box ml={1}>
                              <Button
                                variant="contained"
                                color="primary"
                                size="large"
                                disabled={!hasChanges || submitting}
                                type="submit"
                              >
                                Save
                              </Button>
                            </Box>
                          </Box>
                        </Form>
                      );
                    }}
                  </Formik>
                )}
              </CardContent>
            </Card>

            <Card className={layoutClasses.card} variant="outlined">
              <CardHeader title="Marketplace Group" />
              <CardContent>
                {!loading && (
                  <>
                    <CardDataRow label="ID" value={marketplace?.group?.id} />
                    <CardDataRow
                      label="Group"
                      value={marketplace?.group?.label}
                    />
                    <Box mt={3}>
                      <Link href={`${path}#change-group`} passHref>
                        <Button
                          component="a"
                          variant="outlined"
                          color="secondary"
                        >
                          Change group
                        </Button>
                      </Link>
                    </Box>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        <MarketplaceChangeGroupDialog
          key={`MarketplaceChangeGroupDialog-${changeGroupOpen}`}
          marketplaceId={id}
          open={changeGroupOpen}
          onClose={() => {
            if (changeGroupOpen) {
              router.replace(path);
              router.back();
            }
          }}
        />
      </Content>
    </UserFrame>
  );
}
