import Content from '@/components/Content';
import { ImageData } from '@/components/ImageUploader';
import MarketplaceFormFields, {
  MarketplaceFields,
  MarketplaceSchema,
} from '@/components/MarketplaceFormFields';
import UserFrame from '@/components/UserFrame';
import {
  CreateMarketplaceInput,
  MarketplaceGroupsDocument,
  MarketplacesDocument,
  useCreateMarketplaceMutation,
  useMarketplaceGroupsQuery,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { useLayoutStyles } from '@/hooks/styles';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  FormControl,
  InputLabel,
  LinearProgress,
  Select,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { Form, Formik } from 'formik';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState } from 'react';

const createPermission = ['marketplaces:full'];

export default function CreateMarketplace(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const router = useRouter();
  const [groupId, setGroupId] = useState<string>('_new');
  const [error, setError] = useState<string[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [logo, setLogo] = useState<ImageData | null>(null);
  const { data: groupsData } = useMarketplaceGroupsQuery();
  const [createMarketplace] = useCreateMarketplaceMutation();
  const [canCreate] = useAuthorize(createPermission);
  const { profile } = useProfile();

  const marketplaceGroups = groupsData?.marketplaceGroups ?? [];

  const initialValues: MarketplaceFields = {
    name: '',
    requireDispatchApproval: true,
    requirePractitionerApproval: false,
    slackWebhookUrl: '',
    reviewsIoKey: '',
    reviewsIoStoreId: '',
    primaryColor: '#000000',
  };

  async function handleSubmit(values: MarketplaceFields) {
    if (!canCreate) {
      return;
    }

    const input: CreateMarketplaceInput = {
      ...values,
      slackWebhookUrl: values.slackWebhookUrl || null,
      logoToken: logo?.token || null,
    };

    if (groupId !== '_new') {
      input.groupId = groupId;
    }

    setSubmitting(true);

    try {
      const { errors } = await createMarketplace({
        variables: { input },
        refetchQueries: [
          { query: MarketplaceGroupsDocument },
          { query: MarketplacesDocument },
        ],
        awaitRefetchQueries: true,
      });

      if (errors?.length) {
        setError(
          formatGraphQLErrors(errors) ?? ['Error creating the marketplace'],
        );
      } else {
        router.push(`/p/${profile?.pid}/root/marketplaces`);
        return;
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }

    setSubmitting(false);
  }

  return (
    <UserFrame>
      <Content title="Create marketplace" maxWidth="sm">
        {!canCreate ? (
          <Alert severity="error" variant="filled">
            Not authorized
          </Alert>
        ) : (
          <div>
            {error.length > 0 &&
              error.map((err) => (
                <Box key={err} mb={2}>
                  <Alert severity="error" variant="outlined">
                    {err}
                  </Alert>
                </Box>
              ))}
            <Card className={layoutClasses.card} variant="outlined">
              <div className={layoutClasses.progress}>
                {submitting && <LinearProgress />}
              </div>
              <CardHeader title="Create a marketplace" />
              <CardContent>
                <Formik
                  initialValues={initialValues}
                  validationSchema={MarketplaceSchema}
                  onSubmit={handleSubmit}
                >
                  {() => (
                    <Form>
                      <MarketplaceFormFields
                        autoFocus
                        logo={logo}
                        onLogoChange={setLogo}
                      />

                      <Box mb={2} mt={3}>
                        {/* <FormControl variant="outlined" fullWidth>
                          <InputLabel htmlFor="group-id-field">
                            Marketplace group
                          </InputLabel>
                          <Field
                            component={Select}
                            name="groupId"
                            inputProps={{
                              id: 'group-id-field',
                            }}
                            label="Marketplace group"
                            native
                          >
                            <option value="_new">Create a new group</option>
                            <optgroup label="Existing groups">
                              {marketplaceGroups.map((group) => (
                                <option key={group.id} value={group.id}>
                                  {group.label}
                                </option>
                              ))}
                            </optgroup>
                          </Field>
                        </FormControl> */}

                        <FormControl variant="outlined" fullWidth>
                          <InputLabel htmlFor="select-marketplace-group">
                            Marketplace Group
                          </InputLabel>
                          <Select
                            value={groupId}
                            onChange={(event) =>
                              setGroupId(event.target.value as string)
                            }
                            inputProps={{
                              id: 'select-marketplace-group',
                            }}
                            label="Marketplace Group"
                            native
                          >
                            <option value="_new">Create a new group</option>
                            <optgroup label="Existing groups">
                              {marketplaceGroups.map((group) => (
                                <option key={group.id} value={group.id}>
                                  {group.label}
                                </option>
                              ))}
                            </optgroup>
                          </Select>
                        </FormControl>
                      </Box>

                      <Box display="flex" justifyContent="flex-end">
                        <Box>
                          <Link
                            href={`/p/${profile?.pid}/root/marketplaces`}
                            passHref
                          >
                            <Button
                              component="a"
                              variant="text"
                              color="primary"
                              size="large"
                            >
                              Cancel
                            </Button>
                          </Link>
                        </Box>
                        <Box ml={1}>
                          <Button
                            variant="contained"
                            color="primary"
                            size="large"
                            disabled={submitting}
                            type="submit"
                          >
                            Create
                          </Button>
                        </Box>
                      </Box>
                    </Form>
                  )}
                </Formik>
              </CardContent>
            </Card>
          </div>
        )}
      </Content>
    </UserFrame>
  );
}
