import Content from '@/components/Content';
import ProfileFormFields, {
  ProfileFields,
  ProfileSchema,
} from '@/components/ProfileFormFields';
import UserFrame from '@/components/UserFrame';
import { useUpdateProfileMutation, useViewerQuery } from '@/generated/graphql';
import { useLayoutStyles } from '@/hooks/styles';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  FormControlLabel,
  LinearProgress,
  useMediaQuery,
  Switch,
  useTheme,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';

import { Form, Formik } from 'formik';
import { find } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';

export default function MyProfileEdit(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const router = useRouter();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const { data } = useViewerQuery();
  const [error, setError] = useState<string[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [updateProfile] = useUpdateProfileMutation();

  const pid = router.query.pid as string;
  const profile = find(data?.viewer?.profiles ?? [], { pid });

  const [allowSmsNotifications, setAllowSmsNotifications] = useState(
    profile?.allowSmsNotifications ?? true,
  );

  const initialValues: ProfileFields = {
    givenName: profile?.givenName ?? '',
    familyName: profile?.familyName ?? '',
    email: profile?.email ?? '',
    phone: profile?.phone ?? '',
    title: profile?.title ?? '',
    address: profile?.address ?? '',
    tzid: profile?.tzid ?? dayjs.tz.guess(),
  };

  async function handleSubmit(values: ProfileFields) {
    if (!profile) {
      return;
    }

    const input = {
      ...Object.fromEntries(
        Object.entries(values).filter(
          ([key, value]) => initialValues[key as keyof ProfileFields] !== value,
        ),
      ),
      id: profile.id,
      allowSmsNotifications,
    };

    setSubmitting(true);

    try {
      const { errors } = await updateProfile({
        variables: { input },
      });

      if (errors?.length) {
        setError(formatGraphQLErrors(errors) ?? ['Error updating profile']);
      } else {
        router.push(`/p/${pid}/profile`);
        return;
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
    setSubmitting(false);
  }

  return (
    <UserFrame>
      <Content title="Edit Profile" maxWidth="sm">
        {!profile ? (
          <Alert severity="error" variant="filled">
            Invalid profile
          </Alert>
        ) : (
          <div>
            {error.length > 0 &&
              error.map((err) => (
                <Box key={err} mb={2}>
                  <Alert severity="error" variant="outlined">
                    {err}
                  </Alert>
                </Box>
              ))}
            <Card className={layoutClasses.card} variant="outlined">
              <div className={layoutClasses.progress}>
                {submitting && <LinearProgress />}
              </div>
              <CardHeader
                title="Edit Profile"
                subheader="Some information may be visible to patients and organization personnel."
              />
              <CardContent>
                <Formik
                  initialValues={initialValues}
                  validationSchema={ProfileSchema}
                  onSubmit={handleSubmit}
                >
                  {({ dirty }) => (
                    <Form>
                      <ProfileFormFields
                        tzid={initialValues.tzid}
                        native={fullScreen}
                      />
                      <Box display="flex">
                        <FormControlLabel
                          control={
                            <Switch
                              checked={allowSmsNotifications}
                              onChange={(_, checked) =>
                                setAllowSmsNotifications(checked)
                              }
                              name="allowSmsNotifications"
                              color="primary"
                              size="medium"
                            />
                          }
                          label="Enable text notifications"
                        />
                      </Box>
                      <Box mt={4} display="flex" justifyContent="flex-end">
                        <Box>
                          <Link href={`/p/${pid}/profile`} passHref>
                            <Button
                              component="a"
                              variant="text"
                              color="primary"
                              size="large"
                            >
                              Cancel
                            </Button>
                          </Link>
                        </Box>
                        <Box ml={1}>
                          <Button
                            variant="contained"
                            color="primary"
                            size="large"
                            disabled={
                              (!dirty &&
                                allowSmsNotifications ===
                                  profile?.allowSmsNotifications) ||
                              submitting
                            }
                            type="submit"
                          >
                            Save
                          </Button>
                        </Box>
                      </Box>
                    </Form>
                  )}
                </Formik>
              </CardContent>
            </Card>
          </div>
        )}
      </Content>
    </UserFrame>
  );
}
