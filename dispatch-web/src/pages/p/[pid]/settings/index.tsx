import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import CardDataRow from '@/components/CardDataRow';
import Content from '@/components/Content';
import OrganizationSettingsTabs from '@/components/OrganizationSettingsTabs';
import ResponsiveCell from '@/components/ResponsiveCell';
import UserFrame from '@/components/UserFrame';
import { RoleScope, useOrganizationSettingsQuery } from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useDenormalizedOrganization } from '@/hooks/organization';
import { useProfile } from '@/hooks/profile';
import { useLayoutStyles, useTableStyles } from '@/hooks/styles';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from '@material-ui/core';
import EditIcon from '@material-ui/icons/Edit';
import clsx from 'clsx';
import Link from 'next/link';
import CheckCircle from '@material-ui/icons/CheckCircle';
import ErrorIcon from '@material-ui/icons/Error';

const listPermission = [
  'organizations:full',
  'organizations:list',
  {
    scope: RoleScope.Organization,
    permission: [],
  },
];

const editPermission = [
  'organizations:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization:update'],
  },
];

const listMarketplacesPermission = [
  'marketplaces:list',
  'marketplaces:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.marketplaces:list'],
  },
];

export default function Settings(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const tableClasses = useTableStyles();

  const [canList] = useAuthorize(listPermission);
  const [canEdit] = useAuthorize(editPermission);
  const [canListMarketplaces] = useAuthorize(listMarketplacesPermission);

  const { profile } = useProfile();
  const id = profile?.organization?.id;

  const { data: organizationData } = useOrganizationSettingsQuery({
    variables: { id: id as string },
    skip: !id || !canList,
  });

  const organization = useDenormalizedOrganization(
    organizationData?.organization,
  );

  const path = `/p/${profile?.pid}/settings`;

  return (
    <UserFrame>
      <Content
        title={`Settings - ${organization?.name ?? 'Organization'}`}
        maxWidth="md"
      >
        <BreadcrumbsHeader>
          <Typography color="textSecondary">{organization?.name}</Typography>
          <Typography color="textPrimary">Settings</Typography>
        </BreadcrumbsHeader>

        <OrganizationSettingsTabs selected="info" />

        <Card className={layoutClasses.card} variant="outlined">
          <CardHeader
            title={
              <Box display="flex" justifyContent="space-between">
                <div>Organization Information</div>
                {canEdit && (
                  <Link href={`${path}/edit`} passHref>
                    <Button
                      component="a"
                      color="primary"
                      startIcon={<EditIcon />}
                    >
                      Edit
                    </Button>
                  </Link>
                )}
              </Box>
            }
          />
          <CardContent>
            <CardDataRow label="ID" value={organization?.id} />
            <CardDataRow label="Name" value={organization?.name} />
            <CardDataRow
              label="Time zone"
              value={
                <span>
                  {organization && dayjs().tz(organization.tzid).format('z ')}-{' '}
                  {organization?.tzid}
                </span>
              }
            />
            {!!organization?.address && (
              <CardDataRow label="Address" value={organization?.address} />
            )}
            {canEdit && !!organization?.slackWebhookUrl && (
              <CardDataRow
                label="Slack webhook"
                value={
                  organization.slackWebhookUrl.length > 40
                    ? `${organization.slackWebhookUrl.substring(0, 40)}...`
                    : organization.slackWebhookUrl
                }
              />
            )}
            {canEdit && !!organization?.googleReviewsUrl && (
              <CardDataRow
                label="Google reviews URL"
                value={
                  organization.googleReviewsUrl.length > 40
                    ? `${organization.googleReviewsUrl.substring(0, 40)}...`
                    : organization.googleReviewsUrl
                }
              />
            )}
            <CardDataRow
              value={
                Boolean(organization) && (
                  <Box display="flex">
                    {organization?.providesAtClinic ? (
                      <>
                        <CheckCircle
                          className={layoutClasses.icon}
                          color="primary"
                        />
                        Enabled
                      </>
                    ) : (
                      <>
                        <ErrorIcon
                          className={layoutClasses.icon}
                          color="secondary"
                        />
                        Disabled
                      </>
                    )}
                  </Box>
                )
              }
              label="Clinic services"
            />
            <CardDataRow
              value={
                Boolean(organization) && (
                  <Box display="flex">
                    {organization?.enablePractitionerSms ? (
                      <>
                        <CheckCircle
                          className={layoutClasses.icon}
                          color="primary"
                        />
                        Enabled
                      </>
                    ) : (
                      <>Disabled</>
                    )}
                  </Box>
                )
              }
              label="Provider SMS notifications"
            />
            <CardDataRow
              value={
                Boolean(organization) && (
                  <Box display="flex">
                    {organization?.enableReceiptSending ? (
                      <>
                        <CheckCircle
                          className={layoutClasses.icon}
                          color="primary"
                        />
                        Enabled
                      </>
                    ) : (
                      <>Disabled</>
                    )}
                  </Box>
                )
              }
              label="Auto-send receipts"
            />
          </CardContent>
        </Card>

        {canListMarketplaces && (
          <Card className={layoutClasses.card} variant="outlined">
            <CardHeader title="Marketplaces" />
            <CardContent>
              {organization?.marketplaces.length ? (
                <TableContainer>
                  <Table
                    className={tableClasses.fixed}
                    aria-label="marketplaces table"
                  >
                    <TableHead className={tableClasses.smUp}>
                      <TableRow className={tableClasses.row}>
                        <ResponsiveCell min="sm">Name</ResponsiveCell>
                        <ResponsiveCell min="sm" width="33%">
                          ID
                        </ResponsiveCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {organization?.marketplaces.map((marketplace) => (
                        <TableRow
                          key={marketplace.id}
                          className={clsx(
                            tableClasses.row,
                            tableClasses.highlight,
                          )}
                        >
                          <ResponsiveCell min="xs" component="th" scope="row">
                            {marketplace.name}
                          </ResponsiveCell>
                          <ResponsiveCell
                            min="sm"
                            component="th"
                            scope="row"
                            width="33%"
                          >
                            {marketplace.id}
                          </ResponsiveCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              ) : (
                <div>
                  This organization is not a member of any marketplaces.
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </Content>
    </UserFrame>
  );
}
