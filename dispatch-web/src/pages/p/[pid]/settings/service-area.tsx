import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import Content from '@/components/Content';
import OrganizationSettingsTabs from '@/components/OrganizationSettingsTabs';
import ServiceAreaMap from '@/components/ServiceAreaMap';
import UserFrame from '@/components/UserFrame';
import { RoleScope, useOrganizationSettingsQuery } from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { useLayoutStyles } from '@/hooks/styles';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
} from '@material-ui/core';

const listPermission = [
  'organizations:full',
  'organizations:list',
  {
    scope: RoleScope.Organization,
    permission: [],
  },
];

const editPermission = [
  'organizations:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization:update'],
  },
];

export default function SettingsServiceArea(): JSX.Element {
  const layoutClasses = useLayoutStyles();

  const [canList] = useAuthorize(listPermission);
  const [canEdit] = useAuthorize(editPermission);

  const { profile } = useProfile();
  const id = profile?.organization?.id;

  const { data: organizationData } = useOrganizationSettingsQuery({
    variables: { id: id as string },
    skip: !id || !canList,
  });

  const organization = organizationData?.organization;

  return (
    <UserFrame>
      <Content
        title={`Settings - ${organization?.name ?? 'Organization'}`}
        maxWidth="md"
      >
        <BreadcrumbsHeader>
          <Typography color="textSecondary">{organization?.name}</Typography>
          <Typography color="textPrimary">Settings</Typography>
        </BreadcrumbsHeader>

        <OrganizationSettingsTabs selected="service-area" />

        {canEdit && (
          <Card className={layoutClasses.card} variant="outlined">
            <CardHeader
              title={
                <Box display="flex" justifyContent="space-between">
                  <div>Service Area</div>
                </Box>
              }
            />
            <CardContent>
              <ServiceAreaMap organizationId={id} />
            </CardContent>
          </Card>
        )}
      </Content>
    </UserFrame>
  );
}
