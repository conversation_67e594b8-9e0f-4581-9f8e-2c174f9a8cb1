import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import Content from '@/components/Content';
import CreateProfileDialog from '@/components/CreateProfileDialog';
import ProfileDetailDialog from '@/components/ProfileDetailDialog';
import ResponsiveCell from '@/components/ResponsiveCell';
import UserFrame from '@/components/UserFrame';
import {
  ProfileFieldsFragment,
  RoleFieldsFragment,
  RoleScope,
  useOrganizationQuery,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useDenormalizedOrganization } from '@/hooks/organization';
import { useProfile } from '@/hooks/profile';
import { useClickHandler, usePathHash } from '@/hooks/router';
import { useLayoutStyles, useTableStyles } from '@/hooks/styles';
import { formatPhoneNumber, fullName } from '@/utils/common';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Table,
  TableBody,
  TableContainer,
  TableHead,
  TableRow,
} from '@material-ui/core';
import Typography from '@material-ui/core/Typography/Typography';
import AddIcon from '@material-ui/icons/Add';
import ChevronIcon from '@material-ui/icons/ChevronRight';
import GetAppIcon from '@material-ui/icons/GetApp';
import clsx from 'clsx';
import { find, sortBy } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';

const listPermission = [
  'profiles:full',
  'profiles:list',
  {
    scope: RoleScope.Organization,
    permission: ['organization.profiles:full', 'organization.profiles:list'],
  },
];

const createPermission = [
  'profiles:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.profiles:create', 'organization.profiles:full'],
  },
];

const reportsPermission = [
  'reports:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.reports:full'],
  },
];

const profileFullName = (p: ProfileFieldsFragment) =>
  fullName([p.givenName, p.familyName], p.title) || [p.email];

export default function Organization(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const tableClasses = useTableStyles();

  const hash = usePathHash();
  const clickHandler = useClickHandler();
  const router = useRouter();

  const [canList] = useAuthorize(listPermission);
  const [canCreate] = useAuthorize(createPermission);
  const [canViewReports] = useAuthorize(reportsPermission);

  const { profile } = useProfile();
  const id = profile?.organization?.id;

  const { data: organizationData } = useOrganizationQuery({
    variables: { id: id as string },
    skip: !id || !canList,
  });

  const organization = useDenormalizedOrganization(
    organizationData?.organization,
  );

  const selectedProfile = (organization?.profiles ?? []).find(
    (p) => p.pid === hash.profile,
  );

  const profiles = sortBy(organization?.profiles ?? [], profileFullName);
  const path = `/p/${profile?.pid}/personnel`;

  return (
    <UserFrame>
      <Content
        title={`Personnel - ${organization?.name ?? 'Organization'}`}
        maxWidth="md"
      >
        <BreadcrumbsHeader>
          <Typography color="textSecondary">{organization?.name}</Typography>
          <Typography color="textPrimary">Personnel</Typography>
        </BreadcrumbsHeader>

        <Card className={layoutClasses.card} variant="outlined">
          <CardHeader
            title={
              <Box
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <div>Personnel</div>
                <Box display="flex" alignItems="center">
                  {canViewReports && (
                    <Link href={`/p/${profile?.pid}/reports`} passHref>
                      <IconButton
                        color="default"
                        title="Export Practitioner Productivity Report"
                      >
                        <GetAppIcon />
                      </IconButton>
                    </Link>
                  )}
                  {canCreate && (
                    <Box ml={canViewReports ? 1 : 0}>
                      <Link href={`${path}#create`} passHref>
                        <Button
                          component="a"
                          color="primary"
                          startIcon={<AddIcon />}
                        >
                          New profile
                        </Button>
                      </Link>
                    </Box>
                  )}
                </Box>
              </Box>
            }
          />
          <CardContent>
            <TableContainer>
              <Table
                className={tableClasses.fixed}
                aria-label="personnel table"
              >
                <TableHead className={tableClasses.smUp}>
                  <TableRow className={tableClasses.row}>
                    <ResponsiveCell min="sm" width="50%">
                      Name
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width="50%">
                      Role
                    </ResponsiveCell>
                    <ResponsiveCell min="md" width="37%">
                      Mobile
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width="23%">
                      User ID
                    </ResponsiveCell>
                    <ResponsiveCell min="sm" width={56} />
                  </TableRow>
                </TableHead>
                <TableBody>
                  {profiles.map((p) => (
                    <TableRow
                      key={p.id}
                      className={clsx(
                        tableClasses.row,
                        tableClasses.highlight,
                        tableClasses.clickable,
                      )}
                      onClick={clickHandler.push(`${path}#profile=${p.pid}`)}
                    >
                      <ResponsiveCell min="xs" component="th" scope="row">
                        {profileFullName(p)}
                      </ResponsiveCell>
                      <ResponsiveCell min="sm" component="th" scope="row">
                        {
                          (
                            find(p.roles, {
                              scope: RoleScope.Organization,
                              resourceId: id,
                            }) as RoleFieldsFragment
                          )?.name
                        }
                      </ResponsiveCell>
                      <ResponsiveCell min="md" component="th" scope="row">
                        {formatPhoneNumber(p.phone)}
                      </ResponsiveCell>
                      <ResponsiveCell min="sm" component="th" scope="row">
                        {p.userId ?? ''}
                      </ResponsiveCell>
                      <ResponsiveCell
                        min="xs"
                        component="th"
                        scope="row"
                        width={56}
                      >
                        <Box color="text.secondary" display="flex">
                          <ChevronIcon />
                        </Box>
                      </ResponsiveCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        <ProfileDetailDialog
          key={`{selectedProfile?.id}-${Boolean(selectedProfile)}`}
          open={Boolean(selectedProfile)}
          profile={
            selectedProfile && {
              ...selectedProfile,
              organization,
            }
          }
          onClose={() => {
            if (selectedProfile) {
              router.replace(path);
              router.back();
            }
          }}
        />

        {!!organization && (
          <CreateProfileDialog
            open={Object.keys(hash).includes('create')}
            organization={organization}
            onClose={() => {
              if (Object.keys(hash).includes('create')) {
                router.replace(path);
                router.back();
              }
            }}
          />
        )}
      </Content>
    </UserFrame>
  );
}
