import AppHead from '@/components/AppHead';
import Bread<PERSON>rumbsHeader from '@/components/BreadcrumbsHeader';
import CreateAppointmentReportDialog from '@/components/Reports/CreateAppointmentReportDialog';
import CreatePersonnelReportDialog from '@/components/Reports/CreatePersonnelReportDialog';
import CreateReportDropdown from '@/components/Reports/CreateReportDropdown';
import ReportsGrid, { sanitizeColumns } from '@/components/Reports/ReportsGrid';
import UserFrame from '@/components/UserFrame';
import {
  ReportSortField,
  RoleScope,
  SortDirection,
  useReportsQuery,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useLocalStorage } from '@/hooks/local-storage';

import { useProfile } from '@/hooks/profile';
import { useApolloClient } from '@apollo/client';
import { Sorting, TableColumnWidthInfo } from '@devexpress/dx-react-grid';
import { Box, IconButton, Toolbar, Typography } from '@material-ui/core';
import RefreshIcon from '@material-ui/icons/Refresh';
import { useCallback, useEffect, useMemo, useState } from 'react';

const GRID_PAGE_SIZE = 50;

const defaultSorting: Sorting[] = [
  { columnName: 'createdAt', direction: 'desc' },
];
const defaultColumnOrder: string[] = [];
const defaultColumnWidths: TableColumnWidthInfo[] = [];

const createPermission = [
  'reports:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.reports:full'],
  },
];

export default function Reports(): JSX.Element {
  const apollo = useApolloClient();
  const localStorage = useLocalStorage();

  const [canCreate] = useAuthorize(createPermission);

  const { profile } = useProfile();
  const organization = profile?.organization;

  const [sorting, setSorting] = useState(defaultSorting);
  const [columnOrder, setColumnOrder] = useState(defaultColumnOrder);
  const [columnWidths, setColumnWidths] = useState(defaultColumnWidths);
  const [requestedPage, setRequestedPage] = useState({
    skip: 0,
    take: GRID_PAGE_SIZE,
  });
  const [stateLoaded, setStateLoaded] = useState(false);
  const [appointmentDialogOpen, setAppointmentDialogOpen] = useState(false);
  const [personnelDialogOpen, setPersonnelDialogOpen] = useState(false);

  useEffect(() => {
    if (profile) {
      setStateLoaded(true);
      const configJSON = localStorage?.getUserItem(`${profile.pid}:reports`);

      if (configJSON) {
        try {
          const config = JSON.parse(configJSON);
          if (config.sorting) {
            setSorting(sanitizeColumns(config.sorting));
          }
          if (config.columnOrder) {
            setColumnOrder(sanitizeColumns(config.columnOrder));
          }
          if (config.columnWidths) {
            setColumnWidths(sanitizeColumns(config.columnWidths));
          }
        } catch {
          // invalid config
        }
      }
    }
  }, [profile, localStorage]);

  useEffect(() => {
    if (profile) {
      localStorage.setUserItem(
        `${profile.pid}:reports`,
        JSON.stringify({ sorting, columnOrder, columnWidths }),
      );
    }
  }, [profile, sorting, columnOrder, columnWidths, localStorage]);

  const page = useMemo(
    () => ({
      offset: 0,
      limit: 2 * GRID_PAGE_SIZE,
      filter: organization?.id
        ? {
            organizationId: { eq: +organization.id },
          }
        : undefined,
      sort: sorting.map(({ columnName, direction }) => ({
        field: columnName.toUpperCase() as ReportSortField,
        direction:
          direction === 'desc' ? SortDirection.Desc : SortDirection.Asc,
      })),
    }),
    [organization?.id, sorting],
  );

  const {
    data: reportsData,
    fetchMore,
    loading,
    refetch,
  } = useReportsQuery({
    variables: { page },
    skip: !stateLoaded || !organization?.id,
    notifyOnNetworkStatusChange: true,
  });

  const reports = reportsData?.reports;

  const resetCache = useCallback(() => {
    apollo.cache.evict({ fieldName: 'reports' });

    setRequestedPage({
      skip: 0,
      take: GRID_PAGE_SIZE,
    });
  }, [apollo, setRequestedPage]);

  const handleSortingChange = (updatedSorting: Sorting[]) => {
    resetCache();
    setSorting(updatedSorting);
  };

  const handleColumnOrderChange = (nextOrder: string[]) => {
    setColumnOrder(nextOrder);
  };

  const handleColumnWidthsChange = (
    nextColumnWidths: TableColumnWidthInfo[],
  ) => {
    setColumnWidths(nextColumnWidths);
  };

  const handleLoadMore = (skip: number) => {
    setRequestedPage({ skip, take: GRID_PAGE_SIZE });
  };

  const reset = () => {
    resetCache();
    refetch();
  };

  useEffect(() => {
    const { skip, take } = requestedPage;
    const cachedCount = reports?.data.length ?? 0;

    if (
      reports &&
      reports.totalCount > cachedCount &&
      skip < reports.totalCount &&
      skip + take > cachedCount
    ) {
      fetchMore({
        variables: {
          page: {
            ...page,
            offset: cachedCount,
            limit: GRID_PAGE_SIZE,
          },
        },
      });
    }
  }, [requestedPage, reports, page, fetchMore]);

  return (
    <UserFrame fixed>
      <AppHead title="Reports" />
      <Box height="100%" display="flex" flexDirection="column">
        <Toolbar>
          <Box display="flex" flexGrow="1" alignItems="center">
            <Box mt={2}>
              <BreadcrumbsHeader>
                <Typography color="textPrimary">Reports</Typography>
              </BreadcrumbsHeader>
            </Box>
          </Box>

          <IconButton
            color="default"
            onClick={() => reset()}
            disabled={loading}
          >
            <RefreshIcon />
          </IconButton>

          {canCreate && (
            <CreateReportDropdown
              onCreateAppointmentReport={() => setAppointmentDialogOpen(true)}
              onCreatePersonnelReport={() => setPersonnelDialogOpen(true)}
            />
          )}
        </Toolbar>

        <Box flexGrow="1" display="flex" minHeight={0}>
          <ReportsGrid
            reports={reports?.data ?? []}
            totalCount={reports?.totalCount ?? 0}
            loading={loading}
            sorting={sorting}
            columnOrder={columnOrder}
            columnWidths={columnWidths}
            onSortingChange={handleSortingChange}
            onColumnOrderChange={handleColumnOrderChange}
            onColumnWidthsChange={handleColumnWidthsChange}
            onLoadMore={handleLoadMore}
          />
        </Box>
      </Box>

      {organization && (
        <>
          <CreateAppointmentReportDialog
            open={appointmentDialogOpen}
            onClose={() => setAppointmentDialogOpen(false)}
            onCreated={() => reset()}
            organizationId={organization.id}
          />
          <CreatePersonnelReportDialog
            open={personnelDialogOpen}
            onClose={() => setPersonnelDialogOpen(false)}
            onCreated={() => reset()}
            organizationId={organization.id}
          />
        </>
      )}
    </UserFrame>
  );
}
