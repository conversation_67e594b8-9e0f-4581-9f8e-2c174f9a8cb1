import ConfirmDialog from '@/components/ConfirmDialog';
import {
  GeoperimeterFieldsFragment,
  GeoperimeterType,
  useCreateGeoperimeterMutation,
  useDeleteGeoperimeterMutation,
  useOrganizationGeoperimetersQuery,
  useUpdateGeoperimeterMutation,
} from '@/generated/graphql';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import {
  Center,
  determineStart,
  meterToMile,
  mileToMeter,
  preparePaths,
  roundCoord,
} from '@/utils/service-area';
import {
  Box,
  Button,
  IconButton,
  makeStyles,
  TextField,
  Typography,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import DeleteForeverIcon from '@material-ui/icons/DeleteForever';
import {
  Autocomplete,
  Circle,
  GoogleMap,
  Polygon,
} from '@react-google-maps/api';
import { useSnackbar } from 'notistack';
import React, { useRef, useState } from 'react';

interface Props {
  organizationId: string | undefined;
}

const useStyles = makeStyles(() => ({
  addressInput: {
    boxSizing: `border-box`,
    border: `1px solid transparent`,
    width: `240px`,
    height: `32px`,
    padding: `0 12px`,
    borderRadius: `3px`,
    boxShadow: `0 2px 6px rgba(0, 0, 0, 0.3)`,
    fontSize: `14px`,
    outline: `none`,
    textOverflow: `ellipses`,
    position: 'absolute',
    left: '50%',
    marginLeft: '-120px',
    marginTop: '16px',
  },
  buttons: {
    marginRight: 8,
  },
  buttonContainer: {
    marginBottom: 12,
    marginTop: 4,
    borderTop: '1px solid #AEAEAE',
    // borderBottom: '1px solid #AEAEAE',
  },
  textfields: {
    width: 150,
    marginRight: 8,
    marginBottom: -8,
  },
}));

const options = {
  strokeColor: '#FF0000',
  strokeOpacity: 0.8,
  strokeWeight: 2,
  fillColor: '#FF0000',
  fillOpacity: 0.35,
};

export default function ServiceAreaMap({ organizationId }: Props): JSX.Element {
  const classes = useStyles();

  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  });

  const [autocomplete, setAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const [editing, setEditing] = useState<GeoperimeterFieldsFragment | null>(
    null,
  );
  const [adding, setAdding] = useState(false);
  const [addCircle, setAddCircle] = useState(false);
  const [addPolygon, setAddPolygon] = useState(false);
  const [travelFee, setTravelFee] = useState<number | null>(0);
  const [renderDelete, setRenderDelete] = useState(false);
  const [rad, setRad] = useState<number>(5);

  const { data, refetch } = useOrganizationGeoperimetersQuery({
    variables: { id: organizationId as string },
    skip: !organizationId,
  });

  const geoperimeters = data?.organization?.geoperimeters;

  const lat = geoperimeters?.length
    ? determineStart(geoperimeters[0]).lat
    : 36.778;

  const lng = geoperimeters?.length
    ? determineStart(geoperimeters[0]).lng
    : -119.418;

  const [center, setCenter] = useState<Center | null>(null);

  const { enqueueSnackbar } = useSnackbar();
  const [createGeoperimeter] = useCreateGeoperimeterMutation();
  const [updateGeoperimeter] = useUpdateGeoperimeterMutation();
  const [deleteGeoperimeter] = useDeleteGeoperimeterMutation();

  const mapRef = useRef(null);
  const shapeRef = useRef(null);

  const handleCancel = () => {
    setAdding(false);
    setAddCircle(false);
    setAddPolygon(false);
    setEditing(null);
    setTravelFee(0);
    setCenter(null);
    setRad(0);
  };

  const handleRadiusChanged = () => {
    if (shapeRef?.current) {
      // @ts-expect-error type does exist
      setRad(meterToMile(shapeRef?.current.state.circle.getRadius()));
    }
  };

  const handleRadiusInputChanged = (e: React.ChangeEvent<HTMLInputElement>) => {
    // @ts-expect-error type does exist

    shapeRef?.current?.state.circle.setRadius(
      mileToMeter(Number(e.target.value)),
    );
    setRad(Number(e.target.value));
  };

  const handleLatInputChanged = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newCenter = {
      lat: Number(e.target.value),
      lng: center?.lng as number,
    };
    // @ts-expect-error type does exist

    shapeRef?.current?.state.circle.setCenter({
      lat: newCenter.lat,
      lng: newCenter.lng,
    });
    setCenter({ ...newCenter });
  };

  const handleTravelFeeInputChanged = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const value = parseFloat(e.target.value);
    if (!Number.isNaN(value)) {
      setTravelFee(Math.round(value * 100) / 100);
    } else {
      setTravelFee(null);
    }
  };

  const handleLngInputChanged = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newCenter = {
      lng: Number(e.target.value),
      lat: center?.lat as number,
    };
    setCenter({ ...newCenter });
    // @ts-expect-error type does exist
    shapeRef?.current?.state.circle.setCenter({
      lat: newCenter.lat,
      lng: newCenter.lng,
    });
  };

  const handleShapeDrag = (e: google.maps.MapMouseEvent) => {
    setCenter({
      lat: roundCoord(e.latLng?.lat() as number),
      lng: roundCoord(e.latLng?.lng() as number),
    });
  };

  const getPolygonPaths = () => {
    const newLat = center ? center.lat : lat;
    const newLng = center ? center.lng : lng;

    return [
      { lat: newLat - 0.1, lng: newLng - 0.1 },
      { lat: newLat + 0.1, lng: newLng - 0.1 },
      { lat: newLat + 0.1, lng: newLng + 0.1 },
      { lat: newLat - 0.1, lng: newLng + 0.1 },
    ];
  };

  const handleShapeClick = (geo: GeoperimeterFieldsFragment) => {
    setAddCircle(false);
    setAddPolygon(false);
    if (geo.type === GeoperimeterType.Circle) {
      setCenter({ lat: geo.lat as number, lng: geo.lng as number });
      setRad(meterToMile(geo.radius as number));
      setTravelFee((geo.travelFee as number) / 100);
    } else {
      const paths = JSON.parse(geo.paths as string);
      setCenter({ lat: paths[0].lat as number, lng: paths[0].lng as number });
      setTravelFee((geo.travelFee as number) / 100);
    }
    setEditing(geo);
  };

  const handleAddressInput = () => {
    if (autocomplete) {
      const latLng = autocomplete.getPlace().geometry?.location;

      if (latLng) {
        setCenter({ lat: latLng.lat(), lng: latLng.lng() });
      }
    }
  };

  const handleDrag = () => {
    // @ts-expect-error type does exist
    const latLng = mapRef?.current?.state.map.center;

    if (latLng) {
      setCenter({
        lat: roundCoord(latLng.lat()),
        lng: roundCoord(latLng.lng()),
      });
    }
  };

  const handleDelete = async () => {
    if (organizationId && editing) {
      const result = await deleteGeoperimeter({
        variables: { id: editing.id },
      });

      if (result.data?.deleteGeoperimeter) {
        enqueueSnackbar('Coverage deleted successfully', {
          variant: 'success',
        });

        const currCenter = center;
        refetch();
        setCenter(currCenter);
      }
    }
    handleCancel();
    setRenderDelete(false);
  };

  const circleForm = () => (
    <Box display="flex" flexDirection="row" flexWrap="wrap" gridRowGap={16}>
      <TextField
        onChange={handleRadiusInputChanged}
        type="number"
        variant="outlined"
        value={rad}
        inputProps={{ step: 0.25 }}
        label="Radius (mi)"
        size="small"
        className={classes.textfields}
        InputLabelProps={{ shrink: true }}
      />
      <TextField
        type="number"
        variant="outlined"
        onChange={handleLatInputChanged}
        value={center?.lat}
        inputProps={{ step: 0.01 }}
        label="Latitude"
        size="small"
        className={classes.textfields}
        InputLabelProps={{ shrink: true }}
      />
      <TextField
        type="number"
        variant="outlined"
        onChange={handleLngInputChanged}
        value={center?.lng}
        inputProps={{ step: 0.01 }}
        label="Longitude"
        size="small"
        className={classes.textfields}
        InputLabelProps={{ shrink: true }}
      />
      <TextField
        onChange={handleTravelFeeInputChanged}
        type="number"
        variant="outlined"
        value={travelFee ?? ''}
        inputProps={{ step: 1, min: 0 }}
        label="Travel fee ($)"
        size="small"
        className={classes.textfields}
        InputLabelProps={{ shrink: true }}
      />
    </Box>
  );

  const handleSave = async () => {
    const curr = shapeRef?.current;
    if (curr && organizationId) {
      const fee = travelFee ?? 0;
      // @ts-expect-error type does exist
      const g = curr?.state?.circle
        ? // @ts-expect-error type does exist
          curr?.state?.circle
        : // @ts-expect-error type does exist
          curr?.state?.polygon;

      const paths = g.latLngs ? preparePaths(g) : null;

      if (fee < 0) {
        enqueueSnackbar('Travel fee must be 0 or greater', {
          variant: 'error',
        });
        return;
      }
      if (!Number.isNaN(g.radius) && g.radius <= 0) {
        enqueueSnackbar('Radius must be positive', {
          variant: 'error',
        });
        return;
      }

      try {
        if (editing) {
          const updatedGeo = await updateGeoperimeter({
            variables: {
              input: {
                id: editing.id,
                type: g.radius
                  ? GeoperimeterType.Circle
                  : GeoperimeterType.Polygon,
                radius: roundCoord(g.radius),
                lat: roundCoord(g.center?.lat()),
                lng: roundCoord(g.center?.lng()),
                paths,
                travelFee: fee * 100,
              },
            },
          });
          if (updatedGeo) {
            enqueueSnackbar('Coverage updated successfully', {
              variant: 'success',
            });
          }
        } else {
          const newGeo = await createGeoperimeter({
            variables: {
              input: {
                organizationId,
                type: g.radius
                  ? GeoperimeterType.Circle
                  : GeoperimeterType.Polygon,
                radius: roundCoord(g.radius),
                lat: roundCoord(g.center?.lat()),
                lng: roundCoord(g.center?.lng()),
                paths,
                travelFee: fee * 100,
              },
            },
          });

          if (newGeo) {
            enqueueSnackbar('Coverage added successfully', {
              variant: 'success',
            });
          }
        }
        handleCancel();
        refetch();
      } catch (err) {
        enqueueSnackbar(
          formatGraphQLErrors(err.graphQLErrors || [err.message]),
          { variant: 'error' },
        );
      }
    }
  };

  const renderBaseButtons = () => (
    <Box
      display="flex"
      flexDirection="row"
      className={classes.buttonContainer}
      py={2}
    >
      {adding ? (
        <>
          <Button
            variant="outlined"
            onClick={() => {
              setRad(5);
              setAddCircle(true);
            }}
            className={classes.buttons}
          >
            Radius
          </Button>
          <Button
            variant="outlined"
            onClick={() => setAddPolygon(true)}
            className={classes.buttons}
          >
            Custom
          </Button>
          <Button
            variant="outlined"
            onClick={handleCancel}
            className={classes.buttons}
          >
            Cancel
          </Button>
        </>
      ) : (
        <Button
          variant="contained"
          color="primary"
          onClick={() => setAdding(true)}
          endIcon={<AddIcon />}
          className={classes.buttons}
        >
          Add Coverage
        </Button>
      )}
    </Box>
  );

  const renderAddEditButtons = () => (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      className={classes.buttonContainer}
      py={2}
      mt={1}
    >
      <Box
        display="flex"
        flexDirection="row"
        alignItems="center"
        flexWrap="wrap"
        gridRowGap={8}
        mb={2}
      >
        {addCircle && circleForm()}
        {addPolygon && (
          <TextField
            onChange={handleTravelFeeInputChanged}
            type="number"
            variant="outlined"
            value={travelFee ?? ''}
            inputProps={{ step: 1 }}
            label="Travel fee ($)"
            size="small"
            className={classes.textfields}
            InputLabelProps={{ shrink: true }}
          />
        )}
        {editing && (
          <Box
            display="flex"
            width="100%"
            justifyContent="space-between"
            flexDirection="row"
            alignItems="center"
          >
            {editing.type === GeoperimeterType.Circle && circleForm()}
            {editing.type === GeoperimeterType.Polygon && (
              <TextField
                onChange={handleTravelFeeInputChanged}
                type="number"
                variant="outlined"
                value={travelFee ?? ''}
                inputProps={{ step: 1 }}
                label="Travel fee ($)"
                size="small"
                className={classes.textfields}
                InputLabelProps={{ shrink: true }}
              />
            )}
            <IconButton
              onClick={() => setRenderDelete(true)}
              className={classes.buttons}
            >
              <DeleteForeverIcon />
            </IconButton>
          </Box>
        )}
      </Box>
      <Box
        display="flex"
        flexDirection="row"
        width="100%"
        flexWrap="wrap"
        gridRowGap={8}
        // justifyContent="flex-end"
      >
        <Button onClick={handleCancel} className={classes.buttons}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSave}
          className={classes.buttons}
        >
          Save
        </Button>
      </Box>
    </Box>
  );

  return (
    <Box display="flex" flexDirection="column">
      {geoperimeters?.map((g) => {
        if (g.type === GeoperimeterType.Circle) {
          return (
            <Box key={g.id}>
              <Typography
                variant="subtitle2"
                style={{
                  color: 'gray',
                  marginBottom: 6,
                }}
              >
                {meterToMile(g.radius || 0)} miles around{' '}
                <Button
                  size="small"
                  color="primary"
                  onClick={() => {
                    handleCancel();
                    handleShapeClick(g);
                  }}
                  style={{ padding: 0, textDecoration: 'underline' }}
                >
                  {g.lat}, {g.lng}
                </Button>
                {g.travelFee
                  ? ` - ${formatter.format(g.travelFee / 100)} travel fee`
                  : ''}
              </Typography>
            </Box>
          );
        }
        const paths = JSON.parse(g.paths || '[]');
        return (
          <Box key={g.id}>
            <Typography
              variant="subtitle2"
              style={{
                color: 'gray',
                marginBottom: 6,
              }}
            >
              {paths.length} points around{' '}
              <Button
                size="small"
                color="primary"
                onClick={() => {
                  handleCancel();
                  handleShapeClick(g);
                }}
                style={{ padding: 0, textDecoration: 'underline' }}
              >
                {paths[0].lat}, {paths[0].lng}
              </Button>
              {g.travelFee
                ? ` - ${formatter.format(g.travelFee / 100)} travel fee`
                : ''}
            </Typography>
          </Box>
        );
      })}
      {addCircle || addPolygon || Boolean(editing)
        ? renderAddEditButtons()
        : renderBaseButtons()}
      <GoogleMap
        onDragEnd={handleDrag}
        ref={mapRef}
        id="circle-example"
        mapContainerStyle={{
          height: '600px',
          width: '100%',
        }}
        options={{
          disableDefaultUI: true,
        }}
        zoom={10}
        center={
          center || {
            lat,
            lng,
          }
        }
      >
        <Autocomplete
          onLoad={(auto) => setAutocomplete(auto)}
          onPlaceChanged={handleAddressInput}
        >
          <input
            type="text"
            placeholder="Enter organization address"
            className={classes.addressInput}
          />
        </Autocomplete>
        {addCircle && (
          <Circle
            ref={shapeRef}
            draggable
            editable
            onDragEnd={handleShapeDrag}
            onRadiusChanged={handleRadiusChanged}
            options={options}
            center={center || { lat, lng }}
            radius={mileToMeter(5)}
          />
        )}
        {addPolygon && (
          <Polygon
            options={options}
            draggable
            editable
            ref={shapeRef}
            paths={getPolygonPaths()}
          />
        )}
        {geoperimeters?.map((g) => {
          if (g.type === GeoperimeterType.Circle) {
            return (
              <Circle
                key={g.id}
                ref={editing?.id === g.id ? shapeRef : null}
                onDragEnd={handleShapeDrag}
                onRadiusChanged={handleRadiusChanged}
                draggable={editing?.id === g.id}
                editable={editing?.id === g.id}
                options={options}
                center={
                  editing?.id === g.id
                    ? center || { lat, lng }
                    : { lat: g.lat as number, lng: g.lng as number }
                }
                radius={g.radius as number}
                onClick={() => handleShapeClick(g)}
              />
            );
          }
          return (
            <Polygon
              key={g.id}
              ref={editing?.id === g.id ? shapeRef : null}
              options={options}
              draggable={editing?.id === g.id}
              editable={editing?.id === g.id}
              paths={JSON.parse(g.paths as string)}
              onClick={() => handleShapeClick(g)}
            />
          );
        })}
        <ConfirmDialog
          title="Are you sure you want to delete this coverage?"
          open={renderDelete}
          onConfirm={handleDelete}
          onCancel={() => setRenderDelete(false)}
        />
      </GoogleMap>
    </Box>
  );
}
