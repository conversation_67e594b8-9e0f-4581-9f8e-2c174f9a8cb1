import AppointmentDetailDialog from '@/components/Appointment/AppointmentDetailDialog';
import AppointmentsListDialog from '@/components/AppointmentsListDialog';
import AppointmentsTable from '@/components/AppointmentsTable';
import CardDataRow from '@/components/CardDataRow';
import ClientProfileCreateEditDialog from '@/components/ClientProfileCreateEditDialog';
import ClinicalRecordContent from '@/components/ClinicalRecordContent';
import ConfirmDialog from '@/components/ConfirmDialog';
import SignedConsentFormsContent from '@/components/SignedConsentFormsContent';
import CreateOrderDialog from '@/components/CreateOrderDialog';
import OrderDetailDialog from '@/components/OrderDetailDialog';
import OrdersContent from '@/components/OrdersContent';
import StandardDialogTitle from '@/components/StandardDialogTitle';
import {
  FullClientProfileFieldsFragment,
  FullOrganizationFragment,
  RoleScope,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useLayoutStyles } from '@/hooks/styles';
import { dob, formatPhoneNumber, fullName } from '@/utils/common';
import {
  Avatar,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  Divider,
  makeStyles,
  Menu,
  MenuItem,
  Typography,
  useMediaQuery,
  useTheme,
} from '@material-ui/core';
import ArchiveIcon from '@material-ui/icons/Archive';
import PersonIcon from '@material-ui/icons/Person';
import { sortBy } from 'lodash';
import { useSnackbar } from 'notistack';
import React, { useState } from 'react';

const editPermission = [
  'appointments:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.appointments:full'],
  },
];

interface ClientProfileDetailDialogProps {
  clientProfile?: FullClientProfileFieldsFragment | null;
  organization: FullOrganizationFragment;
  open: boolean;
  onClose: () => void;
}

const useStyles = makeStyles((theme) => ({
  avatar: {
    width: 60,
    height: 60,
    fontSize: '2rem',
    '& > svg': {
      width: '75%',
      height: '75%',
    },
  },
  content: {
    padding: 0,
  },
  section: {
    padding: theme.spacing(3, 3, 1),
  },
}));

const formatSexAssignedAtBirth = (value?: string | null): string => {
  if (!value) return 'Not specified';
  switch (value) {
    case 'male':
      return 'Male';
    case 'female':
      return 'Female';
    case 'unknown':
      return 'Unknown / Not Recorded';
    default:
      return 'Not specified';
  }
};

export default function ClientProfileDetailDialog({
  clientProfile = null,
  organization,
  open,
  onClose,
}: ClientProfileDetailDialogProps): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const theme = useTheme();
  const classes = useStyles();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const { enqueueSnackbar } = useSnackbar();

  const [menuAnchor, setMenuAnchor] = useState<HTMLElement | null>(null);
  const [editing, setEditing] = useState(false);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [deleteErrors, setDeleteErrors] = useState<string[]>([]);

  const [showAppointmentsList, setShowAppointmentsList] =
    useState<boolean>(false);
  const [showCreateOrderDialog, setShowCreateOrderDialog] =
    useState<boolean>(false);

  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);
  const [selectedAppointment, setSelectedAppointment] = useState<string | null>(
    null,
  );

  const [canEdit] = useAuthorize(editPermission, {
    resourceId: organization?.id,
  });

  const fullname = fullName([
    clientProfile?.givenName,
    clientProfile?.familyName,
  ]);

  const recentAppointments = sortBy(clientProfile?.appointments ?? [], 'start')
    .slice(-3)
    .reverse();

  const handleClose = () => {
    setConfirmDeleteOpen(false);
    setDeleteErrors([]);
    setEditing(false);
    onClose();
  };

  const handleEditClose = () => {
    setEditing(false);
  };

  const handleCloseMenu = () => {
    setMenuAnchor(null);
  };

  const handleArchiveClientProfile = async () => {
    enqueueSnackbar('not implemented');
  };

  const handleAppointmentsListClose = () => {
    setShowAppointmentsList(false);
  };

  const handleCreateOrderClose = () => {
    setShowCreateOrderDialog(false);
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        fullScreen={fullScreen}
        maxWidth="md"
        fullWidth
      >
        <StandardDialogTitle
          fullScreen={fullScreen}
          onClose={handleClose}
          {...(canEdit &&
            clientProfile && {
              onClickEdit: () => setEditing(true),
              onClickContext: (event: React.MouseEvent<HTMLElement>) =>
                setMenuAnchor(event.currentTarget),
            })}
        >
          {fullScreen ? (
            <>
              <Box display="flex" justifyContent="center">
                <Avatar classes={{ root: classes.avatar }}>
                  {fullname ? fullname[0].toUpperCase() : <PersonIcon />}
                </Avatar>
              </Box>
              <Typography variant="h5" id="client-dialog-title" align="center">
                {fullname}
              </Typography>
            </>
          ) : (
            <>
              <Box mr={3} display="flex">
                <Avatar classes={{ root: classes.avatar }}>
                  {fullname ? fullname[0].toUpperCase() : <PersonIcon />}
                </Avatar>
              </Box>
              <Typography variant="h4" id="client-dialog-title">
                {fullname}
              </Typography>
            </>
          )}
        </StandardDialogTitle>
        <DialogContent classes={{ root: classes.content }} dividers>
          {!clientProfile ? (
            <CircularProgress />
          ) : (
            <>
              <div className={classes.section}>
                <Typography variant="h6">Client Details</Typography>
                <Box py={1} pl={2}>
                  <CardDataRow label="Name" value={fullname} />
                  <CardDataRow label="Email" value={clientProfile.email} />
                  <CardDataRow
                    label="Phone Number"
                    value={formatPhoneNumber(clientProfile.phone)}
                  />
                  <CardDataRow label="DOB" value={dob(clientProfile.dob)} />
                  <CardDataRow
                    label="Sex assigned at birth"
                    value={formatSexAssignedAtBirth(
                      clientProfile.sexAssignedAtBirth,
                    )}
                  />
                  <CardDataRow label="Address" value={clientProfile.address} />
                  <CardDataRow label="Timezone" value={clientProfile.tzid} />
                  <CardDataRow
                    label="Internal Notes"
                    value={clientProfile.internalNotes}
                  />
                </Box>
              </div>

              {clientProfile.patient && (
                <>
                  <Divider />
                  <div className={classes.section}>
                    <ClinicalRecordContent clientProfile={clientProfile} />
                  </div>
                </>
              )}

              {clientProfile.patient && (
                <>
                  <Divider />
                  <div className={classes.section}>
                    <OrdersContent
                      orders={clientProfile.patient.orders}
                      organization={organization}
                      clientProfile={clientProfile}
                    />
                  </div>
                </>
              )}

              <Divider />

              <div className={classes.section}>
                <Box display="flex" justifyContent="space-between">
                  <Typography variant="h6">Appointments</Typography>
                  <Button
                    color="primary"
                    onClick={() => setShowAppointmentsList(true)}
                  >
                    View all
                  </Button>
                </Box>
                <Box py={1}>
                  {!clientProfile.appointments.length ? (
                    <Box my={2} ml={2}>
                      <Typography color="textSecondary">
                        <em>No appointments</em>
                      </Typography>
                    </Box>
                  ) : (
                    <AppointmentsTable
                      appointments={recentAppointments}
                      onClickRow={(appt) => setSelectedAppointment(appt.id)}
                      tzid={organization.tzid}
                    />
                  )}
                </Box>
              </div>

              {clientProfile.patient && (
                <>
                  <Divider />
                  <div className={classes.section}>
                    <SignedConsentFormsContent clientProfile={clientProfile} />
                  </div>
                </>
              )}
            </>
          )}
        </DialogContent>

        {canEdit && (
          <Menu
            open={Boolean(menuAnchor)}
            onClose={handleCloseMenu}
            anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
            transformOrigin={{ vertical: 'top', horizontal: 'right' }}
            getContentAnchorEl={null}
            anchorEl={menuAnchor}
            keepMounted
          >
            <MenuItem
              onClick={() => {
                handleCloseMenu();
                setConfirmDeleteOpen(true);
              }}
              disabled
            >
              <ArchiveIcon className={layoutClasses.menuIcon} />
              Archive client profile
            </MenuItem>
          </Menu>
        )}

        <ClientProfileCreateEditDialog
          type="edit"
          clientProfile={clientProfile}
          organization={organization}
          open={editing}
          onClose={handleEditClose}
        />

        <ConfirmDialog
          open={confirmDeleteOpen}
          title="Archive this client profile?"
          errors={deleteErrors}
          confirmText="Delete"
          onConfirm={handleArchiveClientProfile}
          onCancel={() => {
            setConfirmDeleteOpen(false);
            setDeleteErrors([]);
          }}
          // submitting={deleteLoading}
        />

        {showCreateOrderDialog && clientProfile && (
          <CreateOrderDialog
            open={showCreateOrderDialog}
            onClose={handleCreateOrderClose}
            organization={organization}
            clientProfile={clientProfile}
          />
        )}

        {showAppointmentsList && clientProfile && (
          <AppointmentsListDialog
            open={showAppointmentsList}
            onClose={handleAppointmentsListClose}
            organization={organization}
            clientProfile={clientProfile}
          />
        )}

        {selectedAppointment && clientProfile?.appointments && (
          <AppointmentDetailDialog
            open={!!selectedAppointment}
            onClose={() => setSelectedAppointment(null)}
            appointment={clientProfile.appointments.find(
              (item) => item.id === selectedAppointment,
            )}
            organization={organization}
          />
        )}
      </Dialog>

      {selectedOrder && clientProfile && (
        <OrderDetailDialog
          open={!!selectedOrder}
          onClose={() => setSelectedOrder(null)}
          clientProfile={clientProfile}
          orderId={selectedOrder}
          organization={organization}
        />
      )}
    </>
  );
}
