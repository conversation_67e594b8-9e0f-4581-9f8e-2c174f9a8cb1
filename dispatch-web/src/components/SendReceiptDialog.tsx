import StandardDialogTitle from '@/components/StandardDialogTitle';
import {
  FullCheckoutFieldsFragment,
  PaymentFieldsFragment,
  useSendReceiptMutation,
} from '@/generated/graphql';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  FormControlLabel,
  TextField,
  Typography,
  makeStyles,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { useSnackbar } from 'notistack';
import React, { useEffect, useState } from 'react';
import { currencyFormatter } from './Checkout/CheckoutItemReadOnly';

const useStyles = makeStyles((theme) => ({
  deliveryRow: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    gap: theme.spacing(2),
  },
  checkbox: {
    minWidth: 120,
  },
  textField: {
    flex: 1,
  },
}));

interface SendReceiptDialogProps {
  open: boolean;
  onClose: () => void;
  checkout?: FullCheckoutFieldsFragment | null;
  fullScreen?: boolean;
  customerContact?: {
    email?: string;
    phone?: string;
  };
  selectedPayment: PaymentFieldsFragment;
}

export default function SendReceiptDialog({
  open,
  onClose,
  checkout,
  fullScreen = false,
  customerContact,
  selectedPayment,
}: SendReceiptDialogProps): JSX.Element {
  const classes = useStyles();
  const { enqueueSnackbar } = useSnackbar();

  const [emailEnabled, setEmailEnabled] = useState(false);
  const [smsEnabled, setSmsEnabled] = useState(false);
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [error, setError] = useState<string[]>([]);

  const [sendReceipt, { loading }] = useSendReceiptMutation();

  // Prefill contact information
  useEffect(() => {
    if (open && customerContact) {
      if (customerContact.email) {
        setEmail(customerContact.email);
      }
      if (customerContact.phone) {
        setPhone(customerContact.phone);
      }
    }
  }, [open, customerContact]);

  useEffect(() => {
    if (!open) {
      setEmailEnabled(false);
      setSmsEnabled(false);
      setEmail('');
      setPhone('');
      setError([]);
    }
  }, [open]);

  const canSend =
    (emailEnabled && email.trim()) || (smsEnabled && phone.trim());

  const handleSend = async () => {
    if (!checkout?.id || !canSend) {
      return;
    }

    setError([]);

    try {
      const deliveryMethods: { email?: string; phone?: string } = {};

      if (emailEnabled && email.trim()) {
        deliveryMethods.email = email.trim();
      }

      if (smsEnabled && phone.trim()) {
        deliveryMethods.phone = phone.trim();
      }

      const { data } = await sendReceipt({
        variables: {
          input: {
            paymentId: selectedPayment.id,
            deliveryMethods,
          },
        },
      });

      if (data?.sendReceipt.success) {
        enqueueSnackbar('Receipt sent successfully', {
          variant: 'success',
        });
        onClose();
      } else {
        setError([data?.sendReceipt.message || 'Failed to send receipt']);
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
    >
      <StandardDialogTitle
        fullScreen={fullScreen}
        onClose={onClose}
        disableTypography
      >
        <Typography variant="h6" component="div">
          Send Receipt
          {selectedPayment && (
            <Typography variant="body2" color="textSecondary">
              {selectedPayment.description} (
              {currencyFormatter.format(selectedPayment.amount / 100)})
            </Typography>
          )}
        </Typography>
      </StandardDialogTitle>

      <DialogContent dividers>
        {error.length > 0 &&
          error.map((err) => (
            <Box key={err} mb={2}>
              <Alert severity="error" variant="outlined">
                {err}
              </Alert>
            </Box>
          ))}

        <Typography variant="body2" color="textSecondary" paragraph>
          Choose how you&apos;d like to send the receipt to the customer:
        </Typography>

        <div className={classes.deliveryRow}>
          <FormControlLabel
            className={classes.checkbox}
            control={
              <Checkbox
                checked={emailEnabled}
                // onChange={(e) => handleEmailChange(e.target.checked)}
                onChange={(e) => setEmailEnabled(e.target.checked)}
                color="primary"
              />
            }
            label="Email"
          />
          <TextField
            className={classes.textField}
            type="email"
            placeholder={email}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={!emailEnabled}
            size="small"
            variant="outlined"
          />
        </div>

        <div className={classes.deliveryRow}>
          <FormControlLabel
            className={classes.checkbox}
            control={
              <Checkbox
                checked={smsEnabled}
                // onChange={(e) => handleSmsChange(e.target.checked)}
                onChange={(e) => setSmsEnabled(e.target.checked)}
                color="primary"
              />
            }
            label="SMS"
          />
          <TextField
            className={classes.textField}
            type="tel"
            placeholder={phone}
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
            disabled={!smsEnabled}
            size="small"
            variant="outlined"
          />
        </div>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSend}
          disabled={!canSend || loading}
          variant="contained"
          color="primary"
        >
          {loading ? 'Sending...' : 'Send Receipt'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
