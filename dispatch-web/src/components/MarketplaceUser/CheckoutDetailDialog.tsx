import CheckoutContent from '@/components/Checkout/CheckoutContent';
import StandardDialogTitle from '@/components/StandardDialogTitle';
import { FullCheckoutFieldsFragment } from '@/generated/graphql';
import { useLayoutStyles } from '@/hooks/styles';
import { Dialog, Menu, MenuItem } from '@material-ui/core';
import LaunchIcon from '@material-ui/icons/Launch';
import { useState } from 'react';

type CheckoutDetailDialogProps = {
  open: boolean;
  onClose: () => void;
  fullScreen?: boolean;
  checkout?: FullCheckoutFieldsFragment;
  expandPayouts?: boolean;
};

export default function CheckoutDetailDialog({
  open,
  onClose,
  fullScreen = false,
  checkout,
  expandPayouts = false,
}: CheckoutDetailDialogProps): JSX.Element {
  const layoutClasses = useLayoutStyles();

  const [menuAnchor, setMenuAnchor] = useState<HTMLElement | null>(null);

  const apptPath = `/appt?id=${checkout?.appointment?.id}`;

  const showMenu = Boolean(checkout?.appointment);

  return (
    <Dialog
      onClose={onClose}
      open={open}
      fullScreen={fullScreen}
      fullWidth
      maxWidth="sm"
    >
      <StandardDialogTitle
        onClose={onClose}
        fullScreen={fullScreen}
        {...(showMenu && {
          onClickContext: (event: React.MouseEvent<HTMLElement>) =>
            setMenuAnchor(event.currentTarget),
        })}
      >
        Checkout Detail
      </StandardDialogTitle>

      <CheckoutContent
        checkout={checkout}
        fullScreen={fullScreen}
        pending={Boolean(
          checkout?.appointmentRequest && !checkout?.appointment,
        )}
        expandPayouts={expandPayouts}
      />

      <Menu
        open={Boolean(menuAnchor)}
        onClose={() => {
          setMenuAnchor(null);
        }}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        getContentAnchorEl={null}
        anchorEl={menuAnchor}
        keepMounted
      >
        {checkout?.appointment && (
          <MenuItem
            onClick={() => {
              window.open(apptPath, '_blank');
              setMenuAnchor(null);
            }}
          >
            <LaunchIcon className={layoutClasses.menuIcon} />
            Appointment
          </MenuItem>
        )}
      </Menu>
    </Dialog>
  );
}
