import TimeZoneFormControl from '@/components/TimeZoneFormControl';
import { Box, Typography } from '@material-ui/core';
import { Field } from 'formik';
import { TextField } from 'formik-material-ui';
import React from 'react';
import * as yup from 'yup';
import ColorPicker from '@/components/ColorPicker';
import { colorNames } from '@/utils/color';

export interface ProfileFields {
  givenName: string;
  familyName: string;
  title?: string;
  email: string;
  phone: string;
  color?: string;
  address: string;
  tzid: string;
  dob?: string;
  allowSmsNotifications?: boolean;
}

interface ProfileFormFieldsProps {
  canEditColor?: boolean;
  tzid?: string | null;
  native?: boolean;
  color?: string;
  setColor?: (s: string) => void;
}

export const ProfileSchema = yup
  .object()
  .shape({
    givenName: yup.string().max(100, 'Too long'),
    familyName: yup.string().max(100, 'Too long'),
    title: yup.string().max(100, 'Too long'),
    dob: yup.string().max(100, 'Too long'),
    email: yup.string().email('Enter a valid email address'),
    phone: yup.string().max(100, 'Too long'),
    address: yup.string().max(255, 'Too long'),
    tzid: yup.string().required('Required'),
  })
  .required();

export default function ProfileFormFields({
  canEditColor,
  tzid = null,
  native = false,
  color,
  setColor,
}: ProfileFormFieldsProps): JSX.Element {
  const colorBox = () => (
    <Box
      borderColor="#bdbdbd"
      borderRadius={4}
      border={1}
      px={2}
      py={1}
      mb={2}
      display="flex"
      flexDirection="row"
      alignItems="center"
      justifyContent="space-between"
    >
      <Box display="flex" flexDirection="row" alignItems="center">
        <div
          style={{
            display: 'flex',
            backgroundColor: color,
            width: 30,
            height: 30,
            borderRadius: '50%',
            marginRight: 12,
          }}
        />
        <Typography variant="subtitle1">
          {color && (colorNames[color] || 'Custom Color')}
        </Typography>
      </Box>
      <ColorPicker color={color} setColor={setColor} />
    </Box>
  );
  return (
    <>
      <Box mb={2}>
        <Field
          component={TextField}
          name="givenName"
          label="First name"
          variant="outlined"
          fullWidth
          inputProps={{ maxLength: 100 }}
        />
      </Box>
      <Box mb={2}>
        <Field
          component={TextField}
          name="familyName"
          label="Last name"
          variant="outlined"
          fullWidth
          inputProps={{ maxLength: 100 }}
        />
      </Box>
      <Box mb={2}>
        <Field
          component={TextField}
          name="title"
          label="Title"
          variant="outlined"
          fullWidth
          inputProps={{ maxLength: 100 }}
        />
      </Box>
      <Box mb={2}>
        <Field
          component={TextField}
          name="email"
          type="email"
          label="Email"
          variant="outlined"
          fullWidth
          inputProps={{ maxLength: 100 }}
        />
      </Box>
      <Box mb={2}>
        <Field
          component={TextField}
          name="phone"
          type="tel"
          label="Mobile phone"
          variant="outlined"
          fullWidth
          inputProps={{ maxLength: 100 }}
        />
      </Box>
      <Box mb={2}>
        <Field
          component={TextField}
          name="address"
          label="Location"
          variant="outlined"
          fullWidth
          inputProps={{ maxLength: 255 }}
        />
      </Box>
      <Box mb={2}>
        <TimeZoneFormControl name="tzid" initialValue={tzid} native={native} />
      </Box>
      {canEditColor && <Field component={colorBox} name="color" />}
    </>
  );
}
