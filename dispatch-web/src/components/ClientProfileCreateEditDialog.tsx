import KeyboardDate<PERSON>ickerField from '@/components/KeyboardDatePickerField';
import LocationAutocomplete from '@/components/LocationAutocomplete';
import TimeZoneFormControl from '@/components/TimeZoneFormControl';
import {
  ClientProfileFieldsFragment,
  CreateClientProfileInput,
  FullClientProfileFieldsFragment,
  OrganizationFieldsFragment,
  UpdateClientProfileInput,
  useCreateClientProfileMutation,
  useUpdateClientProfileMutation,
} from '@/generated/graphql';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  MenuItem,
  TextField as MuiTextField,
  useMediaQuery,
} from '@material-ui/core';
import { makeStyles, useTheme } from '@material-ui/core/styles';
import { Alert } from '@material-ui/lab';
import { Field, Form, Formik } from 'formik';
import { TextField } from 'formik-material-ui';
import { useSnackbar } from 'notistack';
import React, { useState } from 'react';
import { validatePhone } from '@/utils/common';
import * as yup from 'yup';

const useStyles = makeStyles((theme) => ({
  form: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    overflowY: 'auto',
  },
  row: {
    marginBottom: theme.spacing(2),
  },
}));

const Schema = yup
  .object()
  .shape({
    givenName: yup.string().max(100, 'Too long').required('Required'),
    familyName: yup.string().max(100, 'Too long').required('Required'),
    dob: yup
      .string()
      .required('Required')
      .test(
        'DateStringValidator',
        'Invalid date',
        (value) => !!value?.length && dayjs(value).isValid(),
      ),
    email: yup
      .string()
      .nullable()
      .email('Enter a valid email address')
      .required('Required'),
    phone: yup
      .string()
      .test(
        'USPhoneNumberValidator',
        'A valid phone number must be provided. Provide an international code for non-US numbers.',
        (value) => (value ? validatePhone(value) : false),
      )
      .required('Required'),
    address: yup.string().max(255, 'Too long').required('Required'),
    tzid: yup.string().required('Required'),
    sexAssignedAtBirth: yup
      .string()
      .nullable()
      .oneOf(
        ['male', 'female', 'unknown'],
        'Must be one of: Male, Female, or Unknown / Not Recorded',
      ),
    internalNotes: yup.string().max(200, 'Too long'),
  })
  .required();

interface FormFields {
  givenName: string;
  familyName: string;
  dob: string;
  email: string;
  phone: string;
  address: string;
  tzid: string;
  sexAssignedAtBirth: string;
  internalNotes: string;
}

interface ClientProfileCreateEditDialogProps {
  type: 'create' | 'edit';
  open: boolean;
  onClose: (result?: FullClientProfileFieldsFragment | null) => void;
  clientProfile?: Partial<ClientProfileFieldsFragment> | null;
  organization?: OrganizationFieldsFragment | null;
}

export default function ClientProfileCreateEditDialog({
  type,
  open,
  onClose,
  clientProfile = null,
  organization = null,
}: ClientProfileCreateEditDialogProps): JSX.Element {
  const classes = useStyles();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const { enqueueSnackbar } = useSnackbar();
  const [error, setError] = useState<string[]>([]);

  const [createClientProfile, { loading: loadingCreate }] =
    useCreateClientProfileMutation();

  const [updateClientProfile, { loading: loadingUpdate }] =
    useUpdateClientProfileMutation();

  const submitting = loadingCreate || loadingUpdate;

  const initialValues: FormFields = {
    givenName: clientProfile?.givenName ?? '',
    familyName: clientProfile?.familyName ?? '',
    dob: clientProfile?.dob
      ? dayjs(clientProfile?.dob).format('MM/DD/YYYY')
      : '',
    email: clientProfile?.email ?? '',
    phone: clientProfile?.phone ?? '',
    address: clientProfile?.address ?? '',
    tzid: clientProfile?.tzid ?? dayjs.tz.guess(),
    sexAssignedAtBirth: clientProfile?.sexAssignedAtBirth ?? '',
    internalNotes: clientProfile?.internalNotes ?? '',
  };

  function handleClose(result?: FullClientProfileFieldsFragment | null) {
    setError([]);
    onClose(result);
  }

  async function handleSubmitCreate(values: FormFields) {
    if (submitting || !organization) {
      return;
    }

    const input = {
      ...(Object.fromEntries(
        Object.entries(values).filter(([, value]) => Boolean(value)),
      ) as CreateClientProfileInput),
      organizationId: organization.id,
    };

    if (input.dob) {
      input.dob = dayjs(values.dob).format('YYYY-MM-DD');
    }

    try {
      const { errors, data: result } = await createClientProfile({
        variables: { input },
      });

      if (errors?.length) {
        setError(formatGraphQLErrors(errors) ?? ['Error creating the profile']);
      } else {
        enqueueSnackbar('New client profile created');
        handleClose(result?.createClientProfile);
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  }

  async function handleSubmitUpdate(values: FormFields) {
    if (submitting || !clientProfile?.id) {
      return;
    }

    const input = {
      ...Object.fromEntries(
        Object.entries(values).filter(
          ([key, value]) => initialValues[key as keyof FormFields] !== value,
        ),
      ),
      id: clientProfile.id,
    } as UpdateClientProfileInput;

    if (input.dob) {
      input.dob = dayjs(values.dob).format('YYYY-MM-DD');
    }

    try {
      const { errors, data: result } = await updateClientProfile({
        variables: { input },
      });

      if (errors?.length) {
        setError(formatGraphQLErrors(errors) ?? ['Error updating the profile']);
      } else {
        enqueueSnackbar('Client profile updated');
        handleClose(result?.updateClientProfile);
      }
    } catch (err) {
      setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  }

  async function handleSubmit(values: FormFields) {
    if (type === 'create') {
      return handleSubmitCreate(values);
    }

    if (type === 'edit') {
      return handleSubmitUpdate(values);
    }

    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={() => handleClose()}
      aria-labelledby="create-client-profile-dialog-title"
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
    >
      <Formik
        initialValues={initialValues}
        validationSchema={Schema}
        onSubmit={handleSubmit}
      >
        {({
          dirty,
          isValid,
          values,
          errors,
          touched,
          handleBlur,
          setFieldValue,
        }) => (
          <Form id="create-client-profile-form" className={classes.form}>
            <DialogTitle id="create-client-profile-dialog-title">
              {type === 'create'
                ? 'Create new client profile'
                : 'Edit client profile'}
            </DialogTitle>
            <DialogContent dividers>
              {error.length > 0 &&
                error.map((err) => (
                  <Box key={err} mb={2}>
                    <Alert severity="error" variant="outlined">
                      {err}
                    </Alert>
                  </Box>
                ))}

              <div className={classes.row}>
                <Field
                  component={TextField}
                  name="givenName"
                  label="First name"
                  variant="outlined"
                  fullWidth
                  inputProps={{ maxLength: 100 }}
                  required
                  autoFocus
                />
              </div>
              <div className={classes.row}>
                <Field
                  component={TextField}
                  name="familyName"
                  label="Last name"
                  variant="outlined"
                  fullWidth
                  inputProps={{ maxLength: 100 }}
                  required
                />
              </div>
              <div className={classes.row}>
                <Field
                  component={KeyboardDatePickerField}
                  name="dob"
                  label="DOB"
                  inputVariant="outlined"
                  minDate="1900-01-01"
                  openTo="year"
                  format="MM/DD/YYYY"
                  disableFuture
                  fullWidth
                  required
                />
              </div>
              <div className={classes.row}>
                <Field
                  component={TextField}
                  name="sexAssignedAtBirth"
                  label="Sex assigned at birth"
                  variant="outlined"
                  fullWidth
                  select
                >
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                  <MenuItem value="unknown">Unknown / Not Recorded</MenuItem>
                </Field>
              </div>
              <div className={classes.row}>
                <Field
                  component={TextField}
                  name="email"
                  type="email"
                  label="Email"
                  variant="outlined"
                  fullWidth
                  inputProps={{ maxLength: 100 }}
                  required
                />
              </div>
              <div className={classes.row}>
                <Field
                  component={TextField}
                  name="phone"
                  type="tel"
                  label="Mobile phone"
                  variant="outlined"
                  fullWidth
                  inputProps={{ maxLength: 100 }}
                  required
                />
              </div>
              <Box mt={-2} mb={1}>
                <LocationAutocomplete
                  onChange={(value) => setFieldValue('address', value)}
                  initialValue={clientProfile?.address ?? ''}
                  TextFieldProps={{
                    name: 'address',
                    error: Boolean(touched.address && errors.address),
                    helperText: touched.address ? errors.address : '',
                    onBlur: handleBlur,
                  }}
                />
              </Box>
              <div className={classes.row}>
                <TimeZoneFormControl
                  name="tzid"
                  initialValue={values.tzid}
                  native={false}
                />
              </div>
              <div className={classes.row}>
                <MuiTextField
                  variant="outlined"
                  label="Organization"
                  value={organization?.name ?? ''}
                  disabled
                  fullWidth
                />
              </div>
              <div className={classes.row}>
                <Field
                  multiline
                  minRows={3}
                  component={TextField}
                  name="internalNotes"
                  label="Internal Notes"
                  variant="outlined"
                  fullWidth
                  inputProps={{ maxLength: 200 }}
                />
              </div>
            </DialogContent>
            <DialogActions>
              <Button
                onClick={() => handleClose()}
                disabled={submitting}
                color="primary"
                size="large"
              >
                Cancel
              </Button>
              <Button
                variant="text"
                color="primary"
                size="large"
                disabled={!dirty || !isValid || submitting}
                type="submit"
                form="create-client-profile-form"
              >
                Save
              </Button>
            </DialogActions>
          </Form>
        )}
      </Formik>
    </Dialog>
  );
}
