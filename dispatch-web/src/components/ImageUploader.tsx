import { useSignedPostUrl } from '@/hooks/upload-file';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import {
  Box,
  Card,
  CardActionArea,
  IconButton,
  Typography,
  useMediaQuery,
  useTheme,
} from '@material-ui/core';
import Clear from '@material-ui/icons/Clear';
import PublishIcon from '@material-ui/icons/Publish';
import { Alert } from '@material-ui/lab';
import { isNumber } from 'lodash';
import NextImage from 'next/image';
import { useState } from 'react';
import { FileUploader } from 'react-drag-drop-files';

const fileTypes = ['PNG', 'JPG', 'JPEG'];

export type ImageData = {
  file?: File;
  token?: string;
  url?: string;
};

type Props = {
  image?: ImageData | null;
  onImageChange: (f: ImageData | null, i?: number) => void;
  index?: number;
  square?: boolean;
  text?: string;
};

export default function ImageUploader({
  onImageChange,
  index,
  image,
  square,
  text,
}: Props): JSX.Element {
  const theme = useTheme();
  const [error, setError] = useState<string[] | null>(null);
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const [upload, { sending }] = useSignedPostUrl();

  const handleChange = async (f: File) => {
    if (sending) {
      return;
    }

    if (f.size > 1024 * 1024) {
      setError(['File size is too large, must be 1 MB or less.']);
      return;
    }

    try {
      const result = await upload(f);
      if (result) {
        const imageData: ImageData = {
          file: f,
          token: result,
        };
        if (isNumber(index)) {
          onImageChange(imageData, index);
        } else {
          onImageChange(imageData);
        }
        setError(null);
      }
      if (!result) {
        setError(['Error uploading image']);
      }
    } catch (err) {
      setError(formatGraphQLErrors([err]) || ['Error uploading image']);
    }
  };

  const handleClear = () => {
    if (isNumber(index)) {
      onImageChange(null, index);
    } else {
      onImageChange(null);
    }
  };

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        width: square && !fullScreen ? 150 : '100%',
      }}
    >
      {error &&
        error.length > 0 &&
        error.map((e) => (
          <Alert
            key={e}
            style={{ marginBottom: 8 }}
            severity="error"
            icon={<></>}
          >
            {e}
          </Alert>
        ))}
      <Card
        variant="outlined"
        style={{
          width: square && !fullScreen ? 150 : '100%',
          height: square ? 107 : 'auto',
          minHeight: 56,
          border: 'dashed',
          borderColor: theme.palette.primary.main,
          padding: 4,
          display: 'flex',
          flexDirection: fullScreen || square ? 'column' : 'row',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          gap: 8,
        }}
      >
        {sending && (
          <Typography
            variant="body2"
            color="primary"
            style={{ textAlign: 'center' }}
          >
            Uploading image...
          </Typography>
        )}
        {!!(image?.url || image?.file) && !error && !sending && (
          <>
            <IconButton
              size="small"
              onClick={handleClear}
              style={{ position: 'absolute', top: 4, right: 4, zIndex: 0 }}
            >
              <Clear color="primary" fontSize="small" />
            </IconButton>
            <Box
              position="relative"
              height={100}
              width={square ? 70 : '80%'}
              mx={4}
              zIndex={2}
            >
              <NextImage
                src={image.url || URL.createObjectURL(image.file as File)}
                layout="fill"
                objectFit="contain"
                onLoad={(e) => {
                  const img = e.currentTarget;
                  if (square && img.naturalHeight !== img.naturalWidth) {
                    onImageChange(null, index);
                    setError([
                      'Square images must be equal in width and height',
                    ]);
                  }
                }}
              />
            </Box>
          </>
        )}
        {!(image?.url || image?.file) && !sending && (
          <Box width="100%">
            <FileUploader
              handleChange={handleChange}
              name="file"
              types={fileTypes}
            >
              <CardActionArea
                color="primary"
                style={{
                  height: square ? 95 : 'auto',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 8,
                  padding: 4,
                }}
              >
                <PublishIcon color="primary" fontSize="small" />
                <Typography variant="body2" color="primary">
                  {fullScreen || square
                    ? text || 'Upload image'
                    : text || 'Click or drag and drop to upload an image'}
                </Typography>
              </CardActionArea>
            </FileUploader>
          </Box>
        )}
      </Card>
    </div>
  );
}
