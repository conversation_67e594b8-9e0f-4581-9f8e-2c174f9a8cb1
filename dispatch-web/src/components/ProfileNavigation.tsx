import NavigationItem from '@/components/NavigationItem';
import {
  OrganizationFieldsFragment,
  ProfileFieldsFragment,
  RoleScope,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { ListSubheader } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import ProfileIcon from '@material-ui/icons/AccountCircle';
import RolesIcon from '@material-ui/icons/AdminPanelSettings';
import MarketplaceIcon from '@material-ui/icons/Api';
import BarChartIcon from '@material-ui/icons/BarChart';
import OrganizationIcon from '@material-ui/icons/Business';
import ConsentFormsIcon from '@material-ui/icons/ContentPaste';
import CreditCardIcon from '@material-ui/icons/CreditCard';
import EventNoteIcon from '@material-ui/icons/EventNote';
import MedicationsIcon from '@material-ui/icons/Healing';
import EmrIcon from '@material-ui/icons/HealthAndSafety';
import FormTemplatesIcon from '@material-ui/icons/IntegrationInstructions';
import ListIcon from '@material-ui/icons/List';
import ProceduresIcon from '@material-ui/icons/MedicalServices';
import ReceiptIcon from '@material-ui/icons/Payments';
import ProfilesIcon from '@material-ui/icons/PeopleAlt';
import ClientsIcon from '@material-ui/icons/PeopleOutline';
import PlaceIcon from '@material-ui/icons/Place';
import UsersIcon from '@material-ui/icons/RecentActors';
import SettingsIcon from '@material-ui/icons/Settings';
import { useRouter } from 'next/router';
import { MouseEventHandler } from 'react';

const useStyles = makeStyles(() => ({
  subheader: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
}));

interface ProfileNavigationProps {
  profile: ProfileFieldsFragment & {
    organization?: OrganizationFieldsFragment | null;
  };
  onClick?: MouseEventHandler;
}

const permListOrganizations = ['organizations:list', 'organizations:full'];
const permListUsers = ['users:list', 'users:full'];
const permListProfiles = ['profiles:list', 'profiles:full'];
const permListRoles = ['roles:list', 'roles:full'];
const permListEmrInstances = ['emr:full'];

const permListMarketplaces = [
  'marketplaces:full',
  'marketplaces:list',
  {
    scope: RoleScope.Marketplace,
    permission: [],
  },
];

const permListOrgProfiles = [
  'profiles:full',
  'profiles:list',
  {
    scope: RoleScope.Organization,
    permission: ['organization.profiles:full', 'organization.profiles:list'],
  },
];

const permListOrgMedications = [
  'medications:full',
  'medications:list',
  {
    scope: RoleScope.Organization,
    permission: [
      'organization.medications:full',
      'organization.medications:list',
    ],
  },
];

const permOrgSettings = [
  'organizations:full',
  'roles:list',
  'roles:full',
  'marketplaces:list',
  'marketplaces:full',
  {
    scope: RoleScope.Organization,
    permission: [
      'organization:update',
      'organization.roles:list',
      'organization.roles:full',
      'organization.marketplaces:list',
    ],
  },
];

const permListOrgClients = [
  'appointments:list',
  'appointments:full',
  {
    scope: RoleScope.Organization,
    permission: [
      'organization.appointments:list',
      'organization.appointments:full',
    ],
  },
];

const permListOrgAppointments = [
  'appointments:list',
  'appointments:full',
  {
    scope: RoleScope.Organization,
    permission: [
      'organization.appointments:list',
      'organization.appointments:full',
      'organization.appointments:self',
    ],
  },
];

const permListRequests = [
  'appointments:full',
  'appointments:list',
  {
    scope: RoleScope.Marketplace,
    resourceId: null,
    permission: [
      'marketplace.appointments:full',
      'marketplace.appointments:list',
    ],
  },
];

const permEditEmr = [
  'emr:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.emr:full'],
  },
];

const permEditPayments = ['payments:full'];

const permMetrics = ['tester'];

const permListReports = [
  'reports:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.reports:full'],
  },
];

export default function ProfileNavigation({
  profile,
  onClick = () => null,
}: ProfileNavigationProps): JSX.Element {
  const classes = useStyles();
  const { pathname } = useRouter();
  const [showOrganizations] = useAuthorize(permListOrganizations);
  const [showMetrics] = useAuthorize(permMetrics);
  const [showMarketplaces] = useAuthorize(permListMarketplaces);
  const [showUsers] = useAuthorize(permListUsers);
  const [showProfiles] = useAuthorize(permListProfiles);
  const [showRoles] = useAuthorize(permListRoles);
  const [showAppointments] = useAuthorize(permListOrgAppointments);
  const [showClients] = useAuthorize(permListOrgClients);
  const [showPersonnel] = useAuthorize(permListOrgProfiles);
  const [showMedications] = useAuthorize(permListOrgMedications);
  const [showSettings] = useAuthorize(permOrgSettings);
  const [showRequests] = useAuthorize(permListRequests);
  const [showEmrInstances] = useAuthorize(permListEmrInstances);
  const [showEmr] = useAuthorize(permEditEmr);
  const [showPayments] = useAuthorize(permEditPayments);
  const [showReports] = useAuthorize(permListReports);

  return (
    <div>
      {showRequests && (
        <>
          <ListSubheader className={classes.subheader}>
            Marketplace
          </ListSubheader>
          <NavigationItem
            text="Dispatch"
            icon={<PlaceIcon />}
            href={`/p/${profile.pid}/marketplace/dispatch`}
            selected={pathname === '/p/[pid]/marketplace/dispatch'}
            onClick={onClick}
          />
          <NavigationItem
            text="Accounts"
            icon={<ProfilesIcon />}
            href={`/p/${profile.pid}/marketplace/accounts`}
            selected={pathname === '/p/[pid]/marketplace/accounts'}
            onClick={onClick}
          />
          <NavigationItem
            text="Transactions"
            icon={<ReceiptIcon />}
            href={`/p/${profile.pid}/marketplace/transactions`}
            selected={pathname === '/p/[pid]/marketplace/transactions'}
            onClick={onClick}
          />
        </>
      )}

      <ListSubheader className={classes.subheader}>
        {profile.organization?.name ?? 'Organization'}
      </ListSubheader>
      {showAppointments && (
        <>
          <NavigationItem
            text="Calendar"
            icon={<EventNoteIcon />}
            href={`/p/${profile.pid}/calendar`}
            selected={pathname === '/p/[pid]/calendar'}
            onClick={onClick}
          />
          <NavigationItem
            text="Appointments"
            icon={<ListIcon />}
            href={`/p/${profile.pid}/appointments`}
            selected={pathname === '/p/[pid]/appointments'}
            onClick={onClick}
          />
        </>
      )}

      {showClients && (
        <NavigationItem
          text="Clients"
          icon={<ClientsIcon />}
          href={`/p/${profile.pid}/clients`}
          selected={pathname === '/p/[pid]/clients'}
          onClick={onClick}
        />
      )}
      {showPersonnel && (
        <NavigationItem
          text="Personnel"
          icon={<ProfilesIcon />}
          href={`/p/${profile.pid}/personnel`}
          selected={pathname === '/p/[pid]/personnel'}
          onClick={onClick}
        />
      )}
      <NavigationItem
        text="Procedures"
        icon={<ProceduresIcon />}
        href={`/p/${profile.pid}/procedures`}
        selected={pathname === '/p/[pid]/procedures'}
        onClick={onClick}
      />
      {showMedications && (
        <NavigationItem
          text="Medications"
          icon={<MedicationsIcon />}
          href={`/p/${profile.pid}/medications`}
          selected={pathname === '/p/[pid]/medications'}
          onClick={onClick}
        />
      )}
      {showSettings && (
        <NavigationItem
          text="Settings"
          icon={<SettingsIcon />}
          href={`/p/${profile.pid}/settings`}
          selected={pathname === '/p/[pid]/settings'}
          onClick={onClick}
        />
      )}
      {showReports && (
        <NavigationItem
          text="Reports"
          icon={<BarChartIcon />}
          href={`/p/${profile.pid}/reports`}
          selected={pathname === '/p/[pid]/reports'}
          onClick={onClick}
        />
      )}
      <NavigationItem
        text="My profile"
        icon={<ProfileIcon />}
        href={`/p/${profile.pid}/profile`}
        selected={pathname === '/p/[pid]/profile'}
        onClick={onClick}
      />
      {showEmr && Boolean(profile.organization?.emrInstanceId) && (
        <>
          <ListSubheader>EMR</ListSubheader>
          <NavigationItem
            text="Form Templates"
            icon={<FormTemplatesIcon />}
            href={`/p/${profile.pid}/emr/form-templates`}
            selected={pathname === '/p/[pid]/emr/form-templates'}
            onClick={onClick}
          />
          <NavigationItem
            text="Consent Forms"
            icon={<ConsentFormsIcon />}
            href={`/p/${profile.pid}/emr/consent-forms`}
            selected={pathname === '/p/[pid]/emr/consent-forms'}
            onClick={onClick}
          />
        </>
      )}
      {(showOrganizations || showMarketplaces || showProfiles || showRoles) && (
        <ListSubheader>Global</ListSubheader>
      )}
      {showMarketplaces && (
        <NavigationItem
          text="Marketplaces"
          icon={<MarketplaceIcon />}
          href={`/p/${profile.pid}/root/marketplaces`}
          selected={pathname === '/p/[pid]/root/marketplaces'}
          onClick={onClick}
        />
      )}
      {showOrganizations && (
        <NavigationItem
          text="Organizations"
          icon={<OrganizationIcon />}
          href={`/p/${profile.pid}/root/organizations`}
          selected={pathname === '/p/[pid]/root/organizations'}
          onClick={onClick}
        />
      )}
      {showUsers && (
        <NavigationItem
          text="Users"
          icon={<UsersIcon />}
          href={`/p/${profile.pid}/root/users`}
          selected={pathname === '/p/[pid]/root/users'}
          onClick={onClick}
        />
      )}
      {showProfiles && (
        <NavigationItem
          text="Profiles"
          icon={<ProfilesIcon />}
          href={`/p/${profile.pid}/root/profiles`}
          selected={pathname === '/p/[pid]/root/profiles'}
          onClick={onClick}
        />
      )}
      {showRoles && (
        <NavigationItem
          text="Admin Roles"
          icon={<RolesIcon />}
          href={`/p/${profile.pid}/root/roles`}
          selected={pathname === '/p/[pid]/root/roles'}
          onClick={onClick}
        />
      )}
      {showEmrInstances && (
        <NavigationItem
          text="EMR Instances"
          icon={<EmrIcon />}
          href={`/p/${profile.pid}/root/emr-instances`}
          selected={pathname === '/p/[pid]/root/emr-instances'}
          onClick={onClick}
        />
      )}
      {showPayments && (
        <NavigationItem
          text="Payments"
          icon={<CreditCardIcon />}
          href={`/p/${profile.pid}/root/payments`}
          selected={pathname === '/p/[pid]/root/payments'}
          onClick={onClick}
        />
      )}
      {showMetrics && (
        <NavigationItem
          text="Metrics"
          icon={<BarChartIcon />}
          href={`/p/${profile.pid}/root/metrics`}
          selected={pathname.includes('/p/[pid]/root/metrics')}
          onClick={onClick}
        />
      )}
    </div>
  );
}
