import ImageUploader, { ImageData } from '@/components/ImageUploader';
import { Box, FormControl, FormHelperText } from '@material-ui/core';
import { Field, useFormikContext } from 'formik';
import { CheckboxWithLabel, TextField } from 'formik-material-ui';
import * as yup from 'yup';

export const MarketplaceSchema = yup
  .object()
  .shape({
    name: yup.string().required('Required').max(100, 'Too long'),
    requireDispatchApproval: yup.boolean().required('Required'),
    requirePractitionerApproval: yup.boolean().required('Required'),
    slackWebhookUrl: yup.string().max(255),
    reviewsIoStoreId: yup.string().max(100),
    reviewsIoKey: yup
      .string()
      .min(32, 'Too short, must be 32 characters')
      .max(32, 'Too long, must be 32 characters'),
    primaryColor: yup
      .string()
      .matches(/^#[0-9A-Fa-f]{6}$/, 'Must be a valid hex color'),
  })
  .required();

export interface MarketplaceFields {
  name: string;
  requireDispatchApproval: boolean;
  requirePractitionerApproval: boolean;
  slackWebhookUrl: string;
  reviewsIoKey: string;
  reviewsIoStoreId: string;
  primaryColor: string;
}

interface MarketplaceFormFieldsProps {
  autoFocus?: boolean;
  logo?: ImageData | null;
  onLogoChange?: (logo: ImageData | null) => void;
}

export default function MarketplaceFormFields({
  autoFocus = false,
  logo,
  onLogoChange,
}: MarketplaceFormFieldsProps): JSX.Element {
  const { values } = useFormikContext<MarketplaceFields>();

  return (
    <>
      <Box mb={2}>
        <Field
          component={TextField}
          name="name"
          label="Marketplace name"
          variant="outlined"
          fullWidth
          inputProps={{ maxLength: 100 }}
          autoFocus={autoFocus}
        />
      </Box>

      <Box mb={2}>
        <FormControl variant="outlined" fullWidth>
          <Field
            component={CheckboxWithLabel}
            type="checkbox"
            color="primary"
            name="requireDispatchApproval"
            Label={{ label: 'Require dispatch approval' }}
          />
          <FormHelperText>
            Appointment requests will require approval before being dispatched.
          </FormHelperText>
        </FormControl>
      </Box>

      <Box mb={2}>
        <FormControl variant="outlined" fullWidth>
          <Field
            component={CheckboxWithLabel}
            type="checkbox"
            color="primary"
            name="requirePractitionerApproval"
            Label={{ label: 'Require practitioner approval' }}
          />
          <FormHelperText>
            The practitioner will be required to accept or decline every
            dispatched appointment. If this option is disabled, dispatched
            appointments are automatically booked when the practitioner has
            matching availability.
          </FormHelperText>
        </FormControl>
      </Box>

      <Box mb={2} mt={3}>
        <Field
          component={TextField}
          name="slackWebhookUrl"
          label="Slack webhook"
          placeholder="Slack webhook for notifications (optional)"
          variant="outlined"
          multiline
          minRows={3}
          fullWidth
          inputProps={{ maxLength: 255 }}
        />
      </Box>

      <Box mb={2} mt={3}>
        <Field
          component={TextField}
          name="reviewsIoStoreId"
          label="Reviews.io Store ID"
          placeholder="Marketplace store ID (optional)"
          variant="outlined"
          fullWidth
          inputProps={{ maxLength: 100 }}
        />
      </Box>

      <Box mb={2} mt={3}>
        <Field
          component={TextField}
          name="reviewsIoKey"
          label="Reviews.io API Key"
          placeholder="Marketplace API key (optional)"
          variant="outlined"
          fullWidth
          inputProps={{ maxLength: 32 }}
        />
      </Box>

      <Box mb={2} mt={3}>
        <Field
          component={TextField}
          name="primaryColor"
          label="Primary Color (hex)"
          placeholder="#000000"
          variant="outlined"
          fullWidth
          inputProps={{
            maxLength: 7,
            onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
              const { value } = e.target;
              // Add # prefix if not present and value is not empty
              if (value && !value.startsWith('#')) {
                e.target.value = `#${value}`;
              }
            },
          }}
          InputProps={{
            endAdornment:
              values.primaryColor &&
              /^#[0-9A-Fa-f]{6}$/.test(values.primaryColor) ? (
                <Box
                  border="1px solid #bdbdbd"
                  display="flex"
                  flexShrink={0}
                  width={30}
                  height={30}
                  borderRadius="50%"
                  marginRight={1}
                  style={{
                    backgroundColor: values.primaryColor,
                  }}
                />
              ) : undefined,
          }}
        />
      </Box>

      <Box mb={2} mt={3}>
        <ImageUploader
          image={logo}
          onImageChange={onLogoChange || (() => {})}
          text="Upload marketplace logo"
        />
      </Box>
    </>
  );
}
