import DatePickerButton from '@/components/DatePickerButton';
import {
  useOrganizationQuery,
  useReportPersonnelMutation,
} from '@/generated/graphql';
import { useDenormalizedOrganization } from '@/hooks/organization';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import { fullName } from '@/utils/common';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Checkbox,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  Typography,
  useMediaQuery,
  useTheme,
} from '@material-ui/core';
import { SelectInputProps } from '@material-ui/core/Select/SelectInput';
import { Alert } from '@material-ui/lab';
import { sortBy } from 'lodash';
import { useSnackbar } from 'notistack';
import { useState } from 'react';

interface CreatePersonnelReportDialogProps {
  open: boolean;
  onClose: () => void;
  onCreated: () => void;
  organizationId: string;
}

export default function CreatePersonnelReportDialog({
  open,
  onClose,
  onCreated,
  organizationId,
}: CreatePersonnelReportDialogProps): JSX.Element {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const { enqueueSnackbar } = useSnackbar();

  const [startDate, setStartDate] = useState<Date | null>(
    dayjs().subtract(30, 'days').toDate(),
  );
  const [endDate, setEndDate] = useState<Date | null>(dayjs().toDate());
  const [selectedProfileIds, setSelectedProfileIds] = useState<string[]>([]);
  const [error, setError] = useState<string[]>([]);

  const [reportPersonnel, { loading }] = useReportPersonnelMutation();

  const { data: organizationData } = useOrganizationQuery({
    variables: { id: organizationId },
    skip: !organizationId,
  });

  const organization = useDenormalizedOrganization(
    organizationData?.organization,
  );

  const profiles = sortBy(
    (organization?.profiles || []).map((profile) => ({
      ...profile,
      fullName: fullName(
        [profile.givenName, profile.familyName],
        profile.title,
      ),
    })),
    (p) => p.fullName.toLowerCase(),
  );

  const handleSubmit = async () => {
    if (!startDate || !endDate) {
      setError(['Please select both start and end dates']);
      return;
    }

    if (dayjs(startDate).isAfter(dayjs(endDate), 'day')) {
      setError(['Start date must be on or before end date']);
      return;
    }

    setError([]);

    try {
      const profileIds =
        selectedProfileIds.length > 0 ? selectedProfileIds : undefined;
      const result = await reportPersonnel({
        variables: {
          input: {
            organizationId,
            profileIds,
            dateRange: [
              dayjs(startDate).startOf('day').toISOString(),
              dayjs(endDate).endOf('day').toISOString(),
            ],
          },
        },
      });

      if (result.errors?.length) {
        setError(
          formatGraphQLErrors(result.errors) ?? ['Error creating the report'],
        );
      } else {
        enqueueSnackbar('Practitioner Productivity Report generation started', {
          variant: 'success',
        });
        setError([]);
        onCreated();
        onClose();
      }
    } catch (err) {
      setError(
        formatGraphQLErrors(err.graphQLErrors) ?? [err.message] ?? [
            'Error creating the report',
          ],
      );
    }
  };

  const handleClose = () => {
    setError([]);
    setSelectedProfileIds([]);
    onClose();
  };

  const handleUserSelection: SelectInputProps['onChange'] = (event) => {
    const { value } = event.target;
    setSelectedProfileIds(
      typeof value === 'string' ? value.split(',') : (value as string[]),
    );
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="create-personnel-report-dialog-title"
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle id="create-personnel-report-dialog-title">
        Practitioner Productivity Report
      </DialogTitle>
      <DialogContent dividers>
        {error.length > 0 &&
          error.map((err) => (
            <Box key={err} mb={2}>
              <Alert severity="error" variant="outlined">
                {err}
              </Alert>
            </Box>
          ))}

        <Typography variant="body2" color="textSecondary" paragraph>
          Generate a CSV report of personnel statistics and performance metrics
          within the selected date range:
        </Typography>

        <Box mb={3}>
          <FormControl variant="outlined" fullWidth>
            <InputLabel>Personnel (leave blank for all)</InputLabel>
            <Select
              multiple
              value={selectedProfileIds}
              onChange={handleUserSelection}
              label="Personnel (leave blank for all)"
              renderValue={(selected) => {
                const selectedArray = selected as string[];
                if (selectedArray.length === 0) {
                  return 'All Personnel';
                }
                return (
                  <Box display="flex" flexWrap="wrap">
                    {selectedArray.map((profileId) => {
                      const profile = profiles.find((p) => p.id === profileId);
                      const name = profile
                        ? profile.fullName || profile.email
                        : `Profile ${profileId}`;
                      return <Chip key={profileId} label={name} size="small" />;
                    })}
                  </Box>
                );
              }}
            >
              {profiles.map((profile) => (
                <MenuItem key={profile.id} value={profile.id}>
                  <Checkbox checked={selectedProfileIds.includes(profile.id)} />
                  <ListItemText primary={profile.fullName || profile.email} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Box display="flex" mt={3} style={{ gap: '16px' }}>
          <Box flex={1}>
            <Typography
              style={{ fontWeight: 'bold' }}
              color="textPrimary"
              gutterBottom
            >
              Start Date
            </Typography>
            <DatePickerButton
              variant="outlined"
              fullWidth
              value={startDate}
              onChange={setStartDate}
              defaultLabel="Select start date"
            />
          </Box>
          <Box flex={1}>
            <Typography
              style={{ fontWeight: 'bold' }}
              color="textPrimary"
              gutterBottom
            >
              End Date
            </Typography>
            <DatePickerButton
              variant="outlined"
              fullWidth
              value={endDate}
              onChange={setEndDate}
              defaultLabel="Select end date"
            />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          color="primary"
          variant="contained"
          disabled={loading || !startDate || !endDate}
        >
          {loading ? 'Creating...' : 'Create Report'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
