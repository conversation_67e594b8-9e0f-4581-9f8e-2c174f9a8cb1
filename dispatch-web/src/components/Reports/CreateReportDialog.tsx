import DatePickerButton from '@/components/DatePickerButton';
import { useReportAppointmentsMutation } from '@/generated/graphql';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography,
  useMediaQuery,
  useTheme,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { useSnackbar } from 'notistack';
import React, { useState } from 'react';

interface CreateReportDialogProps {
  open: boolean;
  onClose: () => void;
  onCreated: () => void;
  organizationId: string;
}

export default function CreateReportDialog({
  open,
  onClose,
  onCreated,
  organizationId,
}: CreateReportDialogProps): JSX.Element {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const { enqueueSnackbar } = useSnackbar();

  const [startDate, setStartDate] = useState<Date | null>(
    dayjs().subtract(30, 'days').toDate(),
  );
  const [endDate, setEndDate] = useState<Date | null>(dayjs().toDate());
  const [error, setError] = useState<string[]>([]);

  const [reportAppointments, { loading }] = useReportAppointmentsMutation();

  const handleSubmit = async () => {
    if (!startDate || !endDate) {
      setError(['Please select both start and end dates']);
      return;
    }

    if (dayjs(startDate).isAfter(dayjs(endDate), 'day')) {
      setError(['Start date must be on or before end date']);
      return;
    }

    try {
      const { errors } = await reportAppointments({
        variables: {
          input: {
            organizationId,
            dateRange: [
              dayjs(startDate).startOf('day').toISOString(),
              dayjs(endDate).endOf('day').toISOString(),
            ],
          },
        },
      });

      if (errors?.length) {
        setError(formatGraphQLErrors(errors) ?? ['Error creating the report']);
      } else {
        enqueueSnackbar('Report generation started', { variant: 'success' });
        setError([]);
        onCreated();
        onClose();
      }
    } catch (err) {
      setError(
        formatGraphQLErrors(err.graphQLErrors) ?? [err.message] ?? [
            'Error creating the report',
          ],
      );
    }
  };

  const handleClose = () => {
    setError([]);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="create-report-dialog-title"
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle id="create-report-dialog-title">
        Create Appointments Report
      </DialogTitle>
      <DialogContent dividers>
        {error.length > 0 &&
          error.map((err) => (
            <Box key={err} mb={2}>
              <Alert severity="error" variant="outlined">
                {err}
              </Alert>
            </Box>
          ))}

        <Typography variant="body2" color="textSecondary" paragraph>
          Generate a CSV report of appointments within the selected date range.
        </Typography>

        <Box display="flex" mt={3} style={{ gap: '16px' }}>
          <Box flex={1}>
            <Typography
              style={{ fontWeight: 'bold' }}
              color="textPrimary"
              gutterBottom
            >
              Start Date
            </Typography>
            <DatePickerButton
              variant="outlined"
              fullWidth
              value={startDate}
              onChange={setStartDate}
              defaultLabel="Select start date"
            />
          </Box>
          <Box flex={1}>
            <Typography
              style={{ fontWeight: 'bold' }}
              color="textPrimary"
              gutterBottom
            >
              End Date
            </Typography>
            <DatePickerButton
              variant="outlined"
              fullWidth
              value={endDate}
              onChange={setEndDate}
              defaultLabel="Select end date"
            />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          color="primary"
          variant="contained"
          disabled={loading || !startDate || !endDate}
        >
          {loading ? 'Creating...' : 'Create Report'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
