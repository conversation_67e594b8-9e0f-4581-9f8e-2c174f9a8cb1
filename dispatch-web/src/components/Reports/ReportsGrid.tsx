import SelectableRowComponent from '@/components/Grid/SelectableRowComponent';
import {
  PartialColumnState,
  sanitizeColumns as sanitizeColumnsImpl,
} from '@/components/Grid/common';
import { ReportStatus, ReportsQuery } from '@/generated/graphql';
import dayjs from '@/utils/dayjs';
import {
  DataTypeProvider,
  SelectionState,
  Sorting,
  SortingState,
  Table,
  TableColumnWidthInfo,
} from '@devexpress/dx-react-grid';
import {
  DragDropProvider,
  Grid,
  TableColumnReordering,
  TableColumnResizing,
  TableHeaderRow,
  TableSelection,
  VirtualTable,
} from '@devexpress/dx-react-grid-material-ui';
import { Button, makeStyles } from '@material-ui/core';
import { startCase, unionBy } from 'lodash';
import React, { useRef } from 'react';

type Report = NonNullable<ReportsQuery['reports']>['data'][0];

const columns = [
  { name: 'id', title: 'ID', width: 80 },
  { name: 'status', title: 'Status', width: 120 },
  { name: 'type', title: 'Type', width: 120 },
  {
    name: 'filename',
    title: 'Filename',
    width: 400,
  },
  {
    name: 'user',
    title: 'User',
    width: 150,
    getCellValue: (row: Report) => row.userName || `User ${row.userId}`,
  },
  { name: 'createdAt', title: 'Created', width: 150 },
  {
    name: 'download',
    title: 'Download',
    width: 120,
    getCellValue: (row: Report) =>
      row.status === ReportStatus.Completed ? 'Download' : '-',
  },
];

export const sanitizeColumns = <T extends PartialColumnState | string>(
  cols: T[],
): T[] => sanitizeColumnsImpl(cols, columns);

const defaultColumnWidths = columns.map(({ name: columnName, width }) => ({
  columnName,
  width,
}));

const defaultColumnOrder = columns.map(({ name }) => name);

const sortingStateColumnExtensions: SortingState.ColumnExtension[] = [
  { columnName: 'user', sortingEnabled: false },
  { columnName: 'download', sortingEnabled: false },
];

const useRootStyles = makeStyles(() => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
}));

const Root = ({ children }: React.PropsWithChildren<unknown>) => {
  const classes = useRootStyles();
  return <Grid.Root className={classes.root}>{children}</Grid.Root>;
};

const Loading = () => <tbody />;

const DateFromNowFormatter = ({ value = '' }: { value: string }) => (
  <div>{dayjs(value).fromNow()}</div>
);

const useStatusStyles = makeStyles({
  label: {
    borderRadius: 6,
    textAlign: 'center',
    width: 100,
    margin: 'auto',
    padding: '4px 8px',
    fontSize: '12px',
    fontWeight: 'bold',
  },
});

const StatusFormatter = ({ value }: { value: ReportStatus }) => {
  const classes = useStatusStyles();

  const getStatusColor = (status: ReportStatus) => {
    switch (status) {
      case ReportStatus.Completed:
        return { backgroundColor: '#4caf50', color: 'white' };
      case ReportStatus.Processing:
        return { backgroundColor: '#ff9800', color: 'white' };
      case ReportStatus.Failed:
        return { backgroundColor: '#f44336', color: 'white' };
      default:
        return { backgroundColor: '#9e9e9e', color: 'white' };
    }
  };

  return (
    <div className={classes.label} style={getStatusColor(value)}>
      {startCase(value.toLowerCase())}
    </div>
  );
};

const TypeFormatter = ({ value }: { value: string }) => (
  <div>{startCase(value.toLowerCase())}</div>
);

const DownloadCell = (props: Table.DataCellProps) => {
  const { tableRow, ...restProps } = props;
  const report = tableRow.row as Report;

  if (restProps.column.name === 'download') {
    return (
      <VirtualTable.Cell {...props}>
        {report.status === ReportStatus.Completed && report.downloadUrl ? (
          <Button
            size="small"
            color="primary"
            variant="outlined"
            onClick={() => window.open(report.downloadUrl, '_blank')}
          >
            Download
          </Button>
        ) : (
          <div>-</div>
        )}
      </VirtualTable.Cell>
    );
  }

  return <VirtualTable.Cell {...props} />;
};

interface ReportsGridProps {
  reports: Report[];
  totalCount: number;
  loading?: boolean;
  sorting: Sorting[];
  columnOrder: string[];
  columnWidths: TableColumnWidthInfo[];
  onSortingChange: (sorting: Sorting[]) => void;
  onColumnOrderChange: (nextOrder: string[]) => void;
  onColumnWidthsChange: (nextColumnWidths: TableColumnWidthInfo[]) => void;
  onLoadMore: (after: number) => void;
  onSelect?: (report: Report) => void;
}

export default function ReportsGrid({
  reports,
  totalCount,
  loading = false,
  sorting,
  columnOrder,
  columnWidths,
  onSortingChange,
  onColumnOrderChange,
  onColumnWidthsChange,
  onLoadMore,
  onSelect = () => null,
}: ReportsGridProps): JSX.Element {
  const cursorRef = useRef(0);

  const handleSelectionChange = (value: (string | number)[]) => {
    onSelect(reports[value[0] as number]);
  };

  const handleTopRowChange = (rowId: number | string) => {
    const cursor = reports.length;

    if (cursor <= cursorRef.current || cursor >= totalCount) {
      return;
    }

    const index = Number(rowId);

    if (index >= 0 && cursor - index < 50) {
      onLoadMore(cursor);
      cursorRef.current = cursor;
    }
  };

  return (
    <Grid rows={reports} columns={columns} rootComponent={Root}>
      <DragDropProvider />
      <DataTypeProvider formatterComponent={StatusFormatter} for={['status']} />
      <DataTypeProvider formatterComponent={TypeFormatter} for={['type']} />
      <DataTypeProvider
        formatterComponent={DateFromNowFormatter}
        for={['createdAt']}
      />
      <SortingState
        sorting={sorting}
        onSortingChange={onSortingChange}
        columnExtensions={sortingStateColumnExtensions}
      />
      <VirtualTable
        onTopRowChange={handleTopRowChange}
        bodyComponent={
          loading && !reports?.length ? Loading : VirtualTable.TableBody
        }
        cellComponent={DownloadCell}
      />
      <TableColumnResizing
        columnWidths={unionBy(columnWidths, defaultColumnWidths, 'columnName')}
        onColumnWidthsChange={onColumnWidthsChange}
      />
      <TableColumnReordering
        order={unionBy(columnOrder, defaultColumnOrder)}
        onOrderChange={onColumnOrderChange}
      />
      <TableHeaderRow showSortingControls />
      <SelectionState
        selection={[]}
        onSelectionChange={handleSelectionChange}
      />
      <TableSelection
        showSelectionColumn={false}
        rowComponent={SelectableRowComponent}
        selectByRowClick
      />
    </Grid>
  );
}
