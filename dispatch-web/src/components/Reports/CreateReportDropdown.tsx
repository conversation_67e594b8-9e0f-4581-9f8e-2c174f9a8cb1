import {
  Button,
  IconButton,
  Menu,
  MenuItem,
  useMediaQuery,
  useTheme,
} from '@material-ui/core';
import {
  Add as AddIcon,
  ArrowDropDown as ArrowDropDownIcon,
} from '@material-ui/icons';
import React, { useState } from 'react';

interface CreateReportDropdownProps {
  onCreateAppointmentReport: () => void;
  onCreatePersonnelReport: () => void;
}

export default function CreateReportDropdown({
  onCreateAppointmentReport,
  onCreatePersonnelReport,
}: CreateReportDropdownProps): JSX.Element {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleAppointmentReport = () => {
    onCreateAppointmentReport();
    handleClose();
  };

  const handlePersonnelReport = () => {
    onCreatePersonnelReport();
    handleClose();
  };

  return (
    <>
      {fullScreen ? (
        <IconButton color="primary" onClick={handleClick}>
          <AddIcon />
        </IconButton>
      ) : (
        <Button
          color="primary"
          variant="contained"
          size="medium"
          style={{ marginLeft: '1rem' }}
          onClick={handleClick}
          endIcon={<ArrowDropDownIcon />}
        >
          Create Report
        </Button>
      )}
      <Menu
        anchorEl={anchorEl}
        getContentAnchorEl={null}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <MenuItem onClick={handleAppointmentReport}>
          Appointment Report
        </MenuItem>
        <MenuItem onClick={handlePersonnelReport}>
          Practitioner Productivity Report
        </MenuItem>
      </Menu>
    </>
  );
}
