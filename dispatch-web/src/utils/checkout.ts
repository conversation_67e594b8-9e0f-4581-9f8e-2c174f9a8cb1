import {
  FullCheckoutFieldsFragment,
  FullClientProfileFieldsFragment,
} from '@/generated/graphql';

export function getCustomerContactFromCheckout(
  checkout?: FullCheckoutFieldsFragment | null,
  clientProfile?: FullClientProfileFieldsFragment | null,
): { email?: string; phone?: string } | undefined {
  if (!checkout) return undefined;

  // Get contact info from marketplace user or appointment client profile
  const { marketplaceUser } = checkout;

  // If no explicit client profile provided, try to get it from appointment participants
  const fallbackClientProfile =
    clientProfile ||
    checkout.appointment?.participants?.find((p) => p.type === 'PATIENT')
      ?.participant;

  const email = marketplaceUser?.email || fallbackClientProfile?.email;
  const phone = marketplaceUser?.phone || fallbackClientProfile?.phone;

  return { email, phone };
}
