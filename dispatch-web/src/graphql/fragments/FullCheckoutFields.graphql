fragment FullCheckoutFields on Checkout {
  ...CheckoutFields
  items {
    ...CheckoutItemFields
  }
  payments {
    ...PaymentFields
  }
  payouts {
    ...PayoutFields
  }
  paymentInstrument {
    ...PaymentInstrumentFields
  }
  marketplaceUser {
    ...MarketplaceUserFields
  }
  appointment {
    ...AppointmentFields
    participants {
      ...FullAppointmentParticipantFields
    }
  }
  appointmentRequest {
    ...AppointmentRequestFields
  }
}
