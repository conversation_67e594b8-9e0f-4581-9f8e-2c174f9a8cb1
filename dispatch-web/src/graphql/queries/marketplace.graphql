query Marketplace($id: ID!) {
  marketplace(id: $id) {
    ...MarketplaceSettingsFields
    roles {
      ...RoleFields
    }
    procedureBaseDefs {
      ...ProcedureBaseDefFields
      tags
    }
    procedureBaseDefGroups {
      ...ProcedureBaseDefGroupFields
    }
    organizations {
      ...OrganizationFields
    }
    group {
      id
      label
    }
    packages {
      ...PackageFields
    }
    membershipDefinitions {
      ...MembershipDefinitionFields
    }
    attentive {
      ...AttentiveFields
    }
    sendgrid {
      ...SendgridFields
    }
    paymentAccounts {
      ...PaymentAccountFields
    }
    twilio {
      ...TwilioFields
    }
    segment {
      ...SegmentFields
    }
  }
}
